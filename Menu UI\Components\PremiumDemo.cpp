#include "nav_elements.h"
#include "PremiumSystem.h"
#include "../ImGui/imgui.h"
#include "../Fonts/font_defines.h"
#include <iostream>

/**
 * @brief Demo function showing how to use premium CheckboxComponent features
 * 
 * This function demonstrates how to integrate premium features into your menu.
 * Call this function in your main menu rendering loop.
 */
void RenderPremiumDemo() {
    PremiumSystem& premiumSystem = PremiumSystem::getInstance();
    
    // Initialize premium features on first call
    static bool initialized = false;
    if (!initialized) {
        premiumSystem.initializeDefaultPremiumFeatures();
        initialized = true;
    }
    
    ImGui::Begin("Premium Features Demo");
    
    // Premium status controls (for testing)
    if (ImGui::CollapsingHeader("Premium Status Control")) {
        bool isPremium = premiumSystem.isPremiumUser();
        if (ImGui::Checkbox("Premium User", &isPremium)) {
            premiumSystem.setPremiumUser(isPremium);
        }
        
        ImGui::Text("Current Status: %s", isPremium ? "Premium" : "Free");
        ImGui::Separator();
    }
    
    // Demo features
    static bool silentAimbot = false;
    static bool freezePlayer = false;
    static bool normalFeature = false;
    static bool aimLock = false;
    static bool prediction = false;
    
    // Hotkey variables (optional)
    static int silentAimbotKey = 0;
    static bool silentAimbotMode = true;
    static int freezePlayerKey = 0;
    static bool freezePlayerMode = true;
    
    // Color states (optional)
    std::vector<nav_elements::ColorState> premiumColors = {
        {"Primary Color", {1.0f, 0.84f, 0.0f, 1.0f}},
        {"Secondary Color", {1.0f, 0.92f, 0.4f, 1.0f}}
    };
    
    std::vector<nav_elements::ColorState> normalColors = {
        {"Color", {0.0f, 1.0f, 0.0f, 1.0f}}
    };
    
    ImGui::Text("Premium Features:");
    ImGui::Separator();
    
    // Premium feature examples - these will show as locked for non-premium users
    nav_elements::CheckboxComponent("Silent Aimbot", &silentAimbot, 
        "Advanced aimbot with silent targeting^🎯", 
        true, premiumColors, &silentAimbotKey, &silentAimbotMode, true);
    
    nav_elements::CheckboxComponent("Freeze Player", &freezePlayer, 
        "Freeze target players in place^❄️", 
        true, premiumColors, &freezePlayerKey, &freezePlayerMode, true);
    
    nav_elements::CheckboxComponent("Aim Lock", &aimLock, 
        "Lock aim on target automatically^🔒", 
        false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true);
    
    nav_elements::CheckboxComponent("Prediction", &prediction, 
        "Predict player movement patterns^📈", 
        false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true);
    
    ImGui::Separator();
    ImGui::Text("Standard Features:");
    ImGui::Separator();
    
    // Normal feature - always available
    nav_elements::CheckboxComponent("Normal Feature", &normalFeature, 
        "This feature is always available^✅", 
        false, normalColors, nullptr, nullptr, false);
    
    // Show premium feature list
    if (ImGui::CollapsingHeader("Premium Feature Management")) {
        ImGui::Text("Current Premium Features:");
        auto premiumFeatures = premiumSystem.getPremiumFeatures();
        for (const auto& feature : premiumFeatures) {
            ImGui::BulletText("%s", feature.c_str());
        }
        
        ImGui::Separator();
        
        // Add/Remove premium features dynamically
        static char newFeatureName[256] = "";
        ImGui::InputText("Feature Name", newFeatureName, sizeof(newFeatureName));
        
        if (ImGui::Button("Add Premium Feature") && strlen(newFeatureName) > 0) {
            premiumSystem.addPremiumFeature(newFeatureName);
            memset(newFeatureName, 0, sizeof(newFeatureName));
        }
        
        ImGui::SameLine();
        
        if (ImGui::Button("Remove Premium Feature") && strlen(newFeatureName) > 0) {
            premiumSystem.removePremiumFeature(newFeatureName);
            memset(newFeatureName, 0, sizeof(newFeatureName));
        }
    }
    
    // Instructions
    if (ImGui::CollapsingHeader("Usage Instructions")) {
        ImGui::TextWrapped("To use premium features in your menu:");
        ImGui::BulletText("Set isPremium=true when calling CheckboxComponent");
        ImGui::BulletText("Or add feature names to PremiumSystem using addPremiumFeature()");
        ImGui::BulletText("Non-premium users will see locked features with upgrade button");
        ImGui::BulletText("Premium users will see animated PRO badges on premium features");
        ImGui::BulletText("All animations are smooth and match the current UI theme");
        
        ImGui::Separator();
        
        ImGui::TextWrapped("Code example:");
        ImGui::Text("nav_elements::CheckboxComponent(\"Feature Name\", &variable,");
        ImGui::Text("    \"Description^Icon\", hasHotkey, colorStates,");
        ImGui::Text("    &key, &mode, true); // <- isPremium = true");
    }
    
    ImGui::End();
}

/**
 * @brief Initialize premium system for your application
 * 
 * Call this once during application startup
 */
void InitializePremiumSystem() {
    PremiumSystem& premiumSystem = PremiumSystem::getInstance();
    
    // Add your premium features
    premiumSystem.addPremiumFeature("Silent Aimbot");
    premiumSystem.addPremiumFeature("Freeze Player");
    premiumSystem.addPremiumFeature("Aim Lock");
    premiumSystem.addPremiumFeature("Prediction");
    premiumSystem.addPremiumFeature("Advanced ESP");
    premiumSystem.addPremiumFeature("Weapon ESP");
    premiumSystem.addPremiumFeature("Health ESP");
    premiumSystem.addPremiumFeature("Shield ESP");
    
    // Set initial premium status (check license, read from config, etc.)
    // For demo, start as non-premium
    premiumSystem.setPremiumUser(false);
    
    std::cout << "Premium system initialized with " 
              << premiumSystem.getPremiumFeatures().size() 
              << " premium features" << std::endl;
} 