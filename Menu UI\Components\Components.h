#pragma once
#include <Windows.h>
#include <d3d11.h>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../ImGui/imgui.h"
#include "../ImGui/imgui_internal.h"
#include <string>

// Original UI Components
class UIComponents
{
public:
    void Initialize(ID3D11Device* d3dDevice);
    
    // Button Rendering
    bool RenderButton(const char* label, ImVec2 size = ImVec2(0, 0));
    
    // Checkbox Rendering
    bool RenderCheckbox(const char* label, bool* value);
    
    // Slider Rendering
    bool RenderSlider(const char* label, float* value, float min, float max, const char* format = "%.3f");
    bool RenderSlider(const char* label, int* value, int min, int max, const char* format = "%d");
    
    // Color Picker Rendering
    bool RenderColorPicker(const char* label, float* color, ImGuiColorEditFlags flags = 0);
    
    // Toggle Button
    bool RenderToggleButton(const char* label, bool* value, ImVec2 size = ImVec2(0, 0));
};

// Changed from extern to just declaration - will be defined in Components.cpp
extern UIComponents Components;

// New modern UI system components
namespace ModernUI {
    // Main menu container that holds all elements
    class MenuContainer {
    public:
        MenuContainer();
        void Render(bool& showMenu, const char* title = "BITCHEATS");
        ImVec2 GetWindowSize() const { return m_WindowSize; }
        ImVec2 GetWindowPos() const { return m_WindowPos; }

    private:
        ImVec2 m_WindowSize;
        ImVec2 m_WindowPos;
        float m_Opacity;
    };

    // Navigation panel (sidebar) component
    class NavigationPanel {
    public:
        NavigationPanel();
        void Render(int* currentTab);
        
        // Define tab indices for easy reference
        enum TabIndex {
            TAB_AIMBOT = 0,
            TAB_VISUALS,
            TAB_ITEMS,
            TAB_RADAR,
            TAB_SETTINGS,
            TAB_CONFIGS,  // New configurations tab
            TAB_COUNT
        };

    private:
        const char* m_TabIcons[TAB_COUNT];
        const char* m_TabNames[TAB_COUNT];
        bool m_TabAnimations[TAB_COUNT];
    };

    // Content section that displays the active tab content
    class ContentSection {
    public:
        ContentSection();
        void Begin();
        void End();
        
        // Utility methods for consistent content layout
        void BeginSection(const char* title);
        void EndSection();
        void CreateTwoColumnLayout(float leftColumnWidth = 350.0f);
        void NextColumn();
        void EndColumns();
        
    private:
        ImVec2 m_SectionPadding;
        ImVec2 m_ContentSize;
        bool m_InTwoColumnMode;
    };

    // Shared UI elements with consistent styling
    class UIElements {
    public:
        UIElements();
        
        // Modern styled controls
        bool Checkbox(const char* label, bool* value, const char* description = nullptr);
        bool Slider(const char* label, float* value, float min, float max, const char* format = "%.3f", const char* description = nullptr);
        bool Slider(const char* label, int* value, int min, int max, const char* format = "%d", const char* description = nullptr);
        bool Button(const char* label, const ImVec2& size = ImVec2(0, 0), ImVec4 color = ImVec4(0, 0, 0, 0));
        bool Keybind(const char* label, int* key, bool* mode = nullptr, const char* description = nullptr);
        bool ColorEdit(const char* label, float* color, const char* description = nullptr, ImGuiColorEditFlags flags = 0);
        bool Combo(const char* label, int* current_item, const char* const items[], int items_count, const char* description = nullptr, int height_in_items = -1);
        
        // Layout helpers
        void Separator(float spacing = 5.0f);
        void SectionTitle(const char* title);
    };
}

// Ensure these are declared as extern to prevent multiple definitions
extern ModernUI::MenuContainer MainMenu;
extern ModernUI::NavigationPanel NavPanel;
extern ModernUI::ContentSection Content;
extern ModernUI::UIElements Elements;

