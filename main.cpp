#include <Windows.h>
#include <dwmapi.h>
#include <d3d11.h>
#include <string>
#include <thread>
#include <chrono>
#include <shellscalingapi.h>
#include <mutex>
#include <TlHelp32.h>
#include <string>
#include <vector>
#include <iostream>
#include <filesystem>

#include "Menu UI/ImGui/imgui_impl_win32.h"
#include "Menu UI/ImGui/imgui_impl_dx11.h"
#define IMGUI_DEFINE_MATH_OPERATORS
#include "Menu UI/ImGui/imgui.h"
#include "Menu UI/Framwork/GUI.h"
#include "Cheat Core/Features/Caching/Cache.h"
#include "Cheat Core/GameClass/GameSettings.h"
#include "Cheat Core/Features/Visuals/PlayerVisuals.h"
#include "Cheat Core/Kernel Driver/Driver/Driver.h"
#include "Cheat Core/Kernel Driver/Include/utils.h"
#include "Cheat Core/GameClass/Offsets.h"
#include "Cheat Core/Features/Config/Config.h"
#include "Cheat Core/Kernel Driver/Include/utils.h"
#include "Cheat Core/Features/Input/HotkeySystem.h"
#pragma comment(lib, "winmm.lib")
#pragma comment(lib, "dwmapi.lib")
#pragma comment(lib, "d3d11.lib")
#pragma comment(lib, "Shcore.lib")

// DirectX resources
ID3D11Device* g_pd3dDevice = nullptr;
ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
IDXGISwapChain* g_pSwapChain = nullptr;
ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;

//-----------------------------------------------------------------------------------
// Forward Declarations
//-----------------------------------------------------------------------------------
LRESULT CALLBACK WndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
bool CreateDeviceD3D();
void CleanupRenderTarget();
void CreateRenderTarget();
void Shutdown();
void LoadKeys(ImGuiIO& io);
void DrawCheat();
void RenderOverlay();
bool GetProcessNameByID(DWORD processID, std::string& processName);
HWND FindGameWindowAndProcessID(DWORD& outProcessID);
void MakeWindowTransparent(HWND hwnd);

// Implementation of the previously forward-declared function
void SetupDPIAwareness() {
	// Set DPI awareness to ensure correct scaling
	SetProcessDpiAwareness(PROCESS_PER_MONITOR_DPI_AWARE);

	// Optional: You can add additional DPI-related setup here
	// For example, loading proper font sizes based on system DPI
}

// External functions from ImGui
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

//-----------------------------------------------------------------------------------
// DirectX Setup and Cleanup
//-----------------------------------------------------------------------------------
bool CreateDeviceD3D()
{
	DXGI_SWAP_CHAIN_DESC sd;
	ZeroMemory(&sd, sizeof(sd));
	sd.BufferCount = 2;
	sd.BufferDesc.Width = 0;
	sd.BufferDesc.Height = 0;
	sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
	sd.BufferDesc.RefreshRate.Numerator = 60;
	sd.BufferDesc.RefreshRate.Denominator = 1;
	sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
	sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
	sd.OutputWindow = Window;
	sd.SampleDesc.Count = 1;
	sd.SampleDesc.Quality = 0;
	sd.Windowed = TRUE;
	sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

	UINT createDeviceFlags = 0;
	D3D_FEATURE_LEVEL featureLevel;
	const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };

	auto addr = GetModuleA(("d3d11.dll"));
	if (!addr)
		addr = (LoadLibraryA)((("d3d11.dll")));

	auto D3D11CreateDeviceAndSwapChain_ = GetExportAddress(addr, ("D3D11CreateDeviceAndSwapChain"), true);
	if (!D3D11CreateDeviceAndSwapChain_)
		return false;

	if (reinterpret_cast<decltype(&D3D11CreateDeviceAndSwapChain)>(D3D11CreateDeviceAndSwapChain_)(NULL, D3D_DRIVER_TYPE_HARDWARE, NULL, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext) != S_OK)
		return false;

	ID3D11Texture2D* pBackBuffer;
	g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
	g_pd3dDevice->CreateRenderTargetView(pBackBuffer, NULL, &g_mainRenderTargetView);
	pBackBuffer->Release();

	return true;
}

void CleanupRenderTarget() {
	if (g_mainRenderTargetView) {
		g_mainRenderTargetView->Release();
		g_mainRenderTargetView = NULL;
	}
}

void CreateRenderTarget() {
	ID3D11Texture2D* pBackBuffer;
	g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
	g_pd3dDevice->CreateRenderTargetView(pBackBuffer, NULL, &g_mainRenderTargetView);
	pBackBuffer->Release();
}

bool InitializeD3D()
{
	if (!CreateDeviceD3D())
	{
		return false;
	}

	IMGUI_CHECKVERSION();
	ImGui::CreateContext();
	pGUi.Initialize(g_pd3dDevice);

	////// Initialize blur with the valid swap chain
	//if (blur::initialize(g_pd3dDevice, g_pd3dDeviceContext, g_pSwapChain)) {
	//	// Configure blur settings
	//	blur::BlurConfig config;
	//	config.strength = 10.2f;
	//	config.alpha = 1.8f;
	//	config.enable_overlay = false;
	//	blur::set_config(config);

	//	std::cout << "Blur system initialized successfully" << std::endl;
	//}
	//else {
	//	std::cout << "Failed to initialize blur system" << std::endl;
	//}

	auto ret = ImGui_ImplWin32_Init(Window);
	if (!ret)
		return false;

	ret = ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);
	if (!ret)
		return false;

	return true;
}
//-----------------------------------------------------------------------------------
// Window Setup Functions
//-----------------------------------------------------------------------------------
bool SetupOverlayWindow() {
	// Find game window
	const char* ClassName = ("Fortnite");

	WNDCLASSEXA wc;
	ZeroMemory(&wc, sizeof(wc));
	wc.cbSize = sizeof(wc);
	wc.lpfnWndProc = WndProc;
	wc.lpszClassName = ClassName;
	(RegisterClassExA)(&wc);

	Window = (CreateWindowExA)(
		(DWORD)NULL,
		ClassName,
		ClassName,
		WS_POPUP | WS_VISIBLE,
		0, 0, 0, 0,
		(HWND)0, (HMENU)0, (HINSTANCE)0, (LPVOID)0);

	if (!Window)
		return FALSE;

	(SetWindowLongA)(Window, GWL_EXSTYLE, WS_EX_TOOLWINDOW | WS_EX_LAYERED | WS_EX_TRANSPARENT);

	MARGINS Margin = { -1 };
	DwmExtendFrameIntoClientArea(Window, &Margin);
	(ShowWindow)(Window, SW_SHOW);
	(UpdateWindow)(Window);

	if (!Window)
		return FALSE;
}

void MakeWindowTransparent(HWND hwnd) {
	// Set layered window attributes for transparency
	SetLayeredWindowAttributes(hwnd, RGB(0, 0, 0), 0, LWA_COLORKEY);

	// Allow click-through
	MARGINS margins = { -1 };
	DwmExtendFrameIntoClientArea(hwnd, &margins);

	// Set window to topmost and prevent focus stealing
	SetWindowPos(hwnd, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
}

bool GetProcessNameByID(DWORD processID, std::string& processName) {
	HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (hSnapshot == INVALID_HANDLE_VALUE) {
		return false;
	}

	PROCESSENTRY32 pe;
	pe.dwSize = sizeof(PROCESSENTRY32);

	if (Process32First(hSnapshot, &pe)) {
		do {
			if (pe.th32ProcessID == processID) {
				// pe.szExeFile is a CHAR array, assign directly to std::string
				processName = pe.szExeFile;
				CloseHandle(hSnapshot);
				return true;
			}
		} while (Process32Next(hSnapshot, &pe));
	}

	CloseHandle(hSnapshot);
	return false;
}

HWND FindGameWindowAndProcessID(DWORD& outProcessID) {
	HWND gameWindow = nullptr;
	std::string targetProcessName = "FortniteClient-Win64-Shipping.exe";
	std::string currentProcessName;

	do {
		gameWindow = FindWindowA("UnrealWindow", "Fortnite  ");
		if (gameWindow) {
			GetWindowThreadProcessId(gameWindow, &outProcessID);
			if (outProcessID && GetProcessNameByID(outProcessID, currentProcessName)) {
				if (currentProcessName == targetProcessName) {
					// Found the correct window and process
					break;
				}
				else {
					outProcessID = 0; // Reset if the process name does not match
				}
			}
		}
		Sleep(100); // Check again after 100 ms
	} while (true);

	return gameWindow;
}

//-----------------------------------------------------------------------------------
// ImGui/Render Related Functions
//-----------------------------------------------------------------------------------
void LoadKeys(ImGuiIO& io) {
	bool isShift = (GetAsyncKeyState(VK_SHIFT) & 0x8000) || (GetAsyncKeyState(VK_LSHIFT) & 0x8000) || (GetAsyncKeyState(VK_RSHIFT) & 0x8000);
	bool isCaps = GetKeyState(0x14); // CAPS LOCK

	// Handle numbers
	for (int i = '0'; i <= '9'; i++) {
		if (GetAsyncKeyState(i) & 1) {
			io.AddInputCharacter(i);
		}
	}

	// Handle letters
	for (int i = 'A'; i <= 'Z'; i++) {
		if (GetAsyncKeyState(i) & 1) {
			if (isCaps && !isShift) {
				io.AddInputCharacter(i);
			}
			else if (isShift && !isCaps) {
				io.AddInputCharacter(i);
			}
			else {
				io.AddInputCharacter(i + 32); // Convert to lowercase
			}
		}
	}
}

void DrawCheat() {
	ImGui_ImplDX11_NewFrame();
	ImGui_ImplWin32_NewFrame();
	ImGui::NewFrame();

	//blur::begin_frame();

	ImGuiIO& io = ImGui::GetIO();

	// Make sure blur has valid swap chain every frame
	/*if (!blur::check_swap_chain()) {
		blur::set_swap_chain(g_pSwapChain);
	}*/

	// Mouse input handling
	io.MouseDown[0] = (GetAsyncKeyState)(VK_LBUTTON) & 0x8000;
	io.MouseDown[1] = (GetAsyncKeyState)(VK_RBUTTON) & 0x8000;
	io.MouseDown[2] = (GetAsyncKeyState)(VK_MBUTTON) & 0x8000;
	io.MouseDown[3] = (GetAsyncKeyState)(VK_XBUTTON1) & 0x8000;
	io.MouseDown[4] = (GetAsyncKeyState)(VK_XBUTTON2) & 0x8000;


	// Keyboard input handling
	for (int i = 0; i < 512; i++) {
		auto key = ImGui_ImplWin32_VirtualKeyToImGuiKey(i);
		if (key != ImGuiKey_None) {
			io.AddKeyEvent(key, ((GetKeyState)(i) & 0x8000) != 0);
		}
	}

	LoadKeys(io);

	if (((GetKeyState)(VK_LEFT) & 0x8000) != 0) {
		io.MouseWheelH = +0.2;
	}
	if (((GetKeyState)(VK_RIGHT) & 0x8000) != 0) {
		io.MouseWheelH = -0.2;
	}
	if (((GetKeyState)(VK_UP) & 0x8000) != 0) {
		io.MouseWheel = +0.2;
	}
	if (((GetKeyState)(VK_DOWN) & 0x8000) != 0) {
		io.MouseWheel = -0.2;
	}

	if ((GetAsyncKeyState(HOTKEY_SETTINGS.global.menu.key) & 0x8001) == 0x8001) {
		ShowMenu = !ShowMenu;

		if (ShowMenu) {
			// Load settings when opening the menu
			SettingsHelper::LoadSettings();
		}
		else {
			// Save settings when closing the menu
			SettingsHelper::SaveSettings();
		}
	}

	// Setup main drawing area with fully transparent background
	ImGui::SetNextWindowPos({0, 0}, ImGuiCond_Always);
	ImGui::SetNextWindowSize({io.DisplaySize.x, io.DisplaySize.y}, ImGuiCond_Always);

	ImGui::Begin("##scene", nullptr, ImGuiWindowFlags_NoInputs | ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoBackground);
	{
		pGUi.Render(ShowMenu);

		// Update the input system (processes all hotkeys)
		InputManager::UpdateKeyStates();

		// Always sync settings before rendering player visuals to ensure real-time updates
		SettingsHelper::SyncSettings();
		

		PlayerVisuals::DrawAllPlayers();
	}
	ImGui::End();

	// Render ImGui
	ImGui::EndFrame();
	float clearColor[4] = {0.0f, 0.0f, 0.0f, 0.0f};
	g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clearColor);
	ImGui::Render();
	g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
	ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
	g_pSwapChain->Present(0, 0);
}

//-----------------------------------------------------------------------------------
// Cleanup Functions
//-----------------------------------------------------------------------------------
void Shutdown() {
	// Cleanup DirectX resources
	ImGui_ImplDX11_Shutdown();
	ImGui_ImplWin32_Shutdown();
	ImGui::DestroyContext();
	PostQuitMessage(0);
	(exit)(0x100);
}

//-----------------------------------------------------------------------------------
// Main Window Procedure
//-----------------------------------------------------------------------------------
LRESULT CALLBACK WndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
	// Forward ImGui input handling
	if (ImGui_ImplWin32_WndProcHandler(hwnd, msg, wParam, lParam))
		return true;

	switch (msg) {
	case WM_SIZE:
		// Handle resize
		if (wParam != SIZE_MINIMIZED && g_pd3dDevice != nullptr) {
			CleanupRenderTarget();
			g_pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
			CreateRenderTarget();
		}
		return 0;

	case WM_DESTROY:
		// Handle destruction
		Shutdown();
		PostQuitMessage(0);
		return 0;

	default:
		return DefWindowProc(hwnd, msg, wParam, lParam);
	}
}

//-----------------------------------------------------------------------------------
// Main Application Loop
//-----------------------------------------------------------------------------------
void MainLoop() {
	RECT old_rc = { 0,0,0,0 };
	MSG Message = { NULL };
	ZeroMemory(&Message, sizeof(MSG));

	while (Message.message != WM_QUIT)
	{
		if (PeekMessage(&Message, Window, 0, 0, PM_REMOVE))
		{
			(TranslateMessage)(&Message);
			(DispatchMessage)(&Message);
		}

		GameWindow = (FindWindowA)(("UnrealWindow"), ("Fortnite  "));
		if ((GetWindow)(GameWindow, (UINT)NULL) == NULL) {
			SendMessage(Window, WM_DESTROY, 0, 0);
			break;
		}

		HWND hwnd_active = (GetForegroundWindow)();
		if (hwnd_active == GameWindow || hwnd_active == Window) {
			RECT rc;
			POINT xy;

			ZeroMemory(&rc, sizeof(RECT));
			ZeroMemory(&xy, sizeof(POINT));
			(GetClientRect)(GameWindow, (LPRECT)&rc);
			(ClientToScreen)(GameWindow, (LPPOINT)&xy);
			rc.left = xy.x;
			rc.top = xy.y;

			if (rc.left != old_rc.left || rc.right != old_rc.right || rc.top != old_rc.top || rc.bottom != old_rc.bottom)
			{
				old_rc = rc;
				OverlayWidth = rc.right;
				OverlayHeight = rc.bottom;
				SetWindowPos(Window, (HWND)0, xy.x, xy.y, OverlayWidth, OverlayHeight, SWP_NOREDRAW);

				// Ensure blur system has the correct swap chain after window resize
				//blur::set_swap_chain(g_pSwapChain);
			}

			ImGui::GetIO().MouseDrawCursor = ShowMenu;

			DrawCheat();

			HWND hwnd = GetWindow(hwnd_active, (UINT)GW_HWNDPREV);
			if (hwnd_active == Window)
			{
				hwnd = GetWindow(GameWindow, (UINT)GW_HWNDPREV);
			}
			SetWindowPos(Window, hwnd, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);

		}
		else {
			float clearColor[4] = { 0.0f,0.0f,0.0f,0.0f };
			g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clearColor);
			g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, NULL);
			g_pSwapChain->Present(0, 0); // Present without vsync;
		}
		(Sleep)(1);
	}
}

//-----------------------------------------------------------------------------------
// Entry Point
//-----------------------------------------------------------------------------------


static bool initialized = false;

void EntryPoint() {
	//std::cout << "step 10 is good" << std::endl;
	SetupDPIAwareness();

	auto ret = SetupOverlayWindow();
	if (!ret) {
		ShowMessageBox(("Error #0002"), ("Error"), MB_SYSTEMMODAL | MB_ICONERROR, 3000, true);
		return;
	}

//#if IsBitResell
//	std::filesystem::path BypassSteamProof = "C:\\Default\\BypassRecord.Chair";
//
//#else
//	std::filesystem::path BypassSteamProof = "C:\\BitCheats\\BypassRecord.Chair";
//
//#endif
	std::filesystem::path BypassSteamProof = "C:\\BitCheats\\BypassRecord.Chair";

	if (!std::filesystem::exists(BypassSteamProof)) {
		auto HideCreatedWindow = Driver->HideCreatedWindow(Window, WDA_EXCLUDEFROMCAPTURE);
		if (!NT_SUCCESS(HideCreatedWindow)) {
			ShowMessageBox(("Error #0003"), ("Error"), MB_SYSTEMMODAL | MB_ICONERROR, 3000, true);
			return;
		}
	}
	//std::cout << "step 11 is good" << std::endl;

	auto MaskCreatedWindow = Driver->MaskCreatedWindow(Window);
	if (!NT_SUCCESS(MaskCreatedWindow)) {
		ShowMessageBox(("Error #0004"), ("Error"), MB_SYSTEMMODAL | MB_ICONERROR, 3000, true);
		return;
	}

	ret = InitializeD3D();
	if (!ret) {

		ShowMessageBox(("Error #0005"), ("Error"), MB_SYSTEMMODAL | MB_ICONERROR, 3000, true);
		return;
	}

	DWORD processID = 0;
	HWND GameWindow = FindGameWindowAndProcessID(processID);

	if (!GameWindow || !processID) {
		ShowMessageBox(("Error #0006P"), ("Error"), MB_SYSTEMMODAL | MB_ICONERROR, 3000, true);
		return;
	}

	Driver->AttachProcessPID(processID);

	oBaseAddress = uint64_t(Driver->GetProcessBase());

	while (!oBaseAddress || oBaseAddress == (uint64_t)-1) {
		oBaseAddress = uint64_t(Driver->GetProcessBase());
		printf("Trying %llx\n", oBaseAddress);
		(Sleep)(1000);
	}
	printf("BaseAddress %llx\n", oBaseAddress);

	if (!ret) {
		ShowMessageBox(("Error #0006"), ("Error"), MB_SYSTEMMODAL | MB_ICONERROR, 3000, true);
		return;
	}

	// Initialize settings first
	SettingsHelper::InitializeSettings();

	// Load settings from file if it exists
	std::filesystem::path Config = "C:\\BitCheats\\Fortnite.Settings";
	// Load settings if config file exists
	if (std::filesystem::exists(Config)) {
		SettingsHelper::LoadSettings();
	} else {
		std::cout << "Config file not found, using default settings" << std::endl;
	}

	
	PlayerVisuals::isReady = true;

	MainLoop();
	Shutdown();
}

BOOL WINAPI DllMain(
	HINSTANCE hModule,
	DWORD fdwReason,
	LPVOID lpReserved)
{
	switch (fdwReason)
	{
	case DLL_PROCESS_ATTACH:
		{ //Test console
			AllocConsole();
			FILE* file;
			freopen_s(&file, "CONOUT$", "w", stdout);
			printf("Injected\n");
		}

		(DisableThreadLibraryCalls)(hModule);
		if (!InitDriver(lpReserved)) {
			ShowMessageBox(("Error #0001"), ("Error"), MB_SYSTEMMODAL | MB_ICONERROR, 3000, true);
		}

		std::thread(EntryPoint).detach();
		CacheSystem::StartCacheThreads();
		ShowMessageBox(("Injected"), ("Info"), MB_SYSTEMMODAL | MB_OK | MB_ICONINFORMATION, 3000, false);

		break;
	}

	return TRUE;
}