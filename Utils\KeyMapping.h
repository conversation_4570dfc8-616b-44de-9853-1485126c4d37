#pragma once
#include <Windows.h>
#include <string>
#include <unordered_map>

// Key mapping utilities for converting between ImGui keys and Windows virtual key codes
namespace KeyMapping {
    // Convert Windows VK code to string representation
    inline std::string VirtualKeyToString(int vk) {
        if (vk == 0) return "None";
        
        // Handle special keys
        switch (vk) {
            case VK_LBUTTON: return "Mouse 1";
            case VK_RBUTTON: return "Mouse 2";
            case VK_MBUTTON: return "Mouse 3";
            case VK_XBUTTON1: return "Mouse 4";
            case VK_XBUTTON2: return "Mouse 5";
            case VK_BACK: return "Backspace";
            case VK_TAB: return "Tab";
            case VK_RETURN: return "Enter";
            case VK_SHIFT: return "Shift";
            case VK_CONTROL: return "Ctrl";
            case VK_MENU: return "Alt";
            case VK_PAUSE: return "Pause";
            case VK_CAPITAL: return "Caps Lock";
            case VK_ESCAPE: return "Escape";
            case VK_SPACE: return "Space";
            case VK_PRIOR: return "Page Up";
            case VK_NEXT: return "Page Down";
            case VK_END: return "End";
            case VK_HOME: return "Home";
            case VK_LEFT: return "Left";
            case VK_UP: return "Up";
            case VK_RIGHT: return "Right";
            case VK_DOWN: return "Down";
            case VK_SNAPSHOT: return "Print Screen";
            case VK_INSERT: return "Insert";
            case VK_DELETE: return "Delete";
            case VK_LWIN: return "Left Win";
            case VK_RWIN: return "Right Win";
            case VK_APPS: return "Apps";
            case VK_NUMPAD0: return "Numpad 0";
            case VK_NUMPAD1: return "Numpad 1";
            case VK_NUMPAD2: return "Numpad 2";
            case VK_NUMPAD3: return "Numpad 3";
            case VK_NUMPAD4: return "Numpad 4";
            case VK_NUMPAD5: return "Numpad 5";
            case VK_NUMPAD6: return "Numpad 6";
            case VK_NUMPAD7: return "Numpad 7";
            case VK_NUMPAD8: return "Numpad 8";
            case VK_NUMPAD9: return "Numpad 9";
            case VK_MULTIPLY: return "Numpad *";
            case VK_ADD: return "Numpad +";
            case VK_SUBTRACT: return "Numpad -";
            case VK_DECIMAL: return "Numpad .";
            case VK_DIVIDE: return "Numpad /";
            case VK_F1: return "F1";
            case VK_F2: return "F2";
            case VK_F3: return "F3";
            case VK_F4: return "F4";
            case VK_F5: return "F5";
            case VK_F6: return "F6";
            case VK_F7: return "F7";
            case VK_F8: return "F8";
            case VK_F9: return "F9";
            case VK_F10: return "F10";
            case VK_F11: return "F11";
            case VK_F12: return "F12";
            case VK_NUMLOCK: return "Num Lock";
            case VK_SCROLL: return "Scroll Lock";
            case VK_LSHIFT: return "Left Shift";
            case VK_RSHIFT: return "Right Shift";
            case VK_LCONTROL: return "Left Ctrl";
            case VK_RCONTROL: return "Right Ctrl";
            case VK_LMENU: return "Left Alt";
            case VK_RMENU: return "Right Alt";
            case VK_OEM_1: return ";";
            case VK_OEM_PLUS: return "=";
            case VK_OEM_COMMA: return ",";
            case VK_OEM_MINUS: return "-";
            case VK_OEM_PERIOD: return ".";
            case VK_OEM_2: return "/";
            case VK_OEM_3: return "`";
            case VK_OEM_4: return "[";
            case VK_OEM_5: return "\\";
            case VK_OEM_6: return "]";
            case VK_OEM_7: return "'";
        }
        
        // Handle alphanumeric keys
        if (vk >= '0' && vk <= '9') {
            char key = static_cast<char>(vk);
            return std::string(1, key);
        }
        
        if (vk >= 'A' && vk <= 'Z') {
            char key = static_cast<char>(vk);
            return std::string(1, key);
        }
        
        // For any other key, return the hex code
        char buffer[16];
        sprintf_s(buffer, "Key 0x%02X", vk);
        return buffer;
    }
    
    // Check if a key is currently pressed
    inline bool IsKeyPressed(int vk) {
        if (vk == 0) return false;
        return (GetAsyncKeyState(vk) & 0x8000) != 0;
    }
    
    // Check if a key was just pressed (went from not pressed to pressed)
    inline bool IsKeyJustPressed(int vk) {
        if (vk == 0) return false;
        return (GetAsyncKeyState(vk) & 0x0001) != 0;
    }
    
    // Convert ImGui key to Windows VK code
    inline int ImGuiKeyToVK(int imguiKey) {
        // This is a simplified mapping - extend as needed
        if (imguiKey >= 0x30 && imguiKey <= 0x39) return imguiKey; // 0-9
        if (imguiKey >= 0x41 && imguiKey <= 0x5A) return imguiKey; // A-Z
        
        // Special keys
        switch (imguiKey) {
            case ImGuiKey_Escape: return VK_ESCAPE;
            case ImGuiKey_Enter: return VK_RETURN;
            case ImGuiKey_Tab: return VK_TAB;
            case ImGuiKey_Backspace: return VK_BACK;
            case ImGuiKey_Insert: return VK_INSERT;
            case ImGuiKey_Delete: return VK_DELETE;
            case ImGuiKey_RightArrow: return VK_RIGHT;
            case ImGuiKey_LeftArrow: return VK_LEFT;
            case ImGuiKey_DownArrow: return VK_DOWN;
            case ImGuiKey_UpArrow: return VK_UP;
            case ImGuiKey_PageUp: return VK_PRIOR;
            case ImGuiKey_PageDown: return VK_NEXT;
            case ImGuiKey_Home: return VK_HOME;
            case ImGuiKey_End: return VK_END;
            case ImGuiKey_F1: return VK_F1;
            case ImGuiKey_F2: return VK_F2;
            case ImGuiKey_F3: return VK_F3;
            case ImGuiKey_F4: return VK_F4;
            case ImGuiKey_F5: return VK_F5;
            case ImGuiKey_F6: return VK_F6;
            case ImGuiKey_F7: return VK_F7;
            case ImGuiKey_F8: return VK_F8;
            case ImGuiKey_F9: return VK_F9;
            case ImGuiKey_F10: return VK_F10;
            case ImGuiKey_F11: return VK_F11;
            case ImGuiKey_F12: return VK_F12;
            case ImGuiKey_LeftShift: return VK_LSHIFT;
            case ImGuiKey_LeftCtrl: return VK_LCONTROL;
            case ImGuiKey_LeftAlt: return VK_LMENU;
            case ImGuiKey_LeftSuper: return VK_LWIN;
            case ImGuiKey_RightShift: return VK_RSHIFT;
            case ImGuiKey_RightCtrl: return VK_RCONTROL;
            case ImGuiKey_RightAlt: return VK_RMENU;
            case ImGuiKey_RightSuper: return VK_RWIN;
            case ImGuiKey_Space: return VK_SPACE;
            default: return imguiKey;
        }
    }
}
