#pragma once
#include <vector>
#include <mutex>
#include <string>
#include <chrono>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../../Menu UI/ImGui/imgui.h"

struct CachedPlayer {
    uintptr_t Pawn;
    uintptr_t RootComponent;
    uintptr_t PlayerState;
    uintptr_t Mesh;
    uintptr_t PlayerStates;
    uintptr_t AcknowledgedPawn;
    uintptr_t TargetedPawn;
    std::string WeaponName;  
    std::string PowerRanking;
    std::string CurrentRank;
    ImColor PickColor;
};

class CacheSystem {
public:    
    // Main caching methods
    static void InitializeOffsets();
    static void UpdatePlayerCache();
    static void StartCacheThreads();
    static void StopCacheThreads();
    
    // Getter for the cached player list
    static const std::vector<CachedPlayer>& GetCachedPlayers() {
        return cachedPlayerList;
    }
    
private:
    static std::vector<CachedPlayer> cachedPlayerList;
    static std::mutex cacheMutex;
    static bool isRunning;
    static bool isReady;
    
    // Cache thread functions
    static void PlayerCacheThread();
    static void LootCacheThread();
};

// External thread function declarations
void ResetLootCache();
