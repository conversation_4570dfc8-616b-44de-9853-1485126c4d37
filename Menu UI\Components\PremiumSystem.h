#pragma once
#include <string>
#include <vector>
#include <unordered_set>
#include <memory>
#include "../ImGui/imgui.h"

/**
 * @class PremiumSystem
 * @brief Manages premium features and user subscription status
 */
class PremiumSystem {
public:
    /**
     * @brief Get the singleton instance of PremiumSystem
     * @return Reference to the singleton instance
     */
    static PremiumSystem& getInstance();

    /**
     * @brief Check if the user has premium access
     * @return true if user is premium, false otherwise
     */
    bool isPremiumUser() const;

    /**
     * @brief Set premium user status
     * @param premium Premium status to set
     */
    void setPremiumUser(bool premium);

    /**
     * @brief Check if a specific feature is premium-only
     * @param featureName Name of the feature to check
     * @return true if feature is premium-only, false otherwise
     */
    bool isFeaturePremium(const std::string& featureName) const;

    /**
     * @brief Add a feature to the premium features list
     * @param featureName Name of the feature to add
     */
    void addPremiumFeature(const std::string& featureName);

    /**
     * @brief Remove a feature from the premium features list
     * @param featureName Name of the feature to remove
     */
    void removePremiumFeature(const std::string& featureName);

    /**
     * @brief Get all premium features
     * @return Vector of premium feature names
     */
    std::vector<std::string> getPremiumFeatures() const;

    /**
     * @brief Check if a feature should be locked for current user
     * @param featureName Name of the feature to check
     * @return true if feature should be locked, false otherwise
     */
    bool shouldLockFeature(const std::string& featureName) const;

    /**
     * @brief Initialize default premium features
     */
    void initializeDefaultPremiumFeatures();

    /**
     * @brief Callback for upgrade button click
     */
    void onUpgradeButtonClick();

private:
    PremiumSystem() = default;
    ~PremiumSystem() = default;
    
    // Non-copyable
    PremiumSystem(const PremiumSystem&) = delete;
    PremiumSystem& operator=(const PremiumSystem&) = delete;

    bool isPremium = false;
    std::unordered_set<std::string> premiumFeatures;
};

/**
 * @brief Animation state for premium UI elements
 */
struct PremiumAnimationState {
    float premiumBadgeOpacity = 0.0f;
    float premiumBadgeScale = 1.0f;
    float upgradeButtonOpacity = 0.0f;
    float upgradeButtonScale = 1.0f;
    float lockOverlayOpacity = 0.0f;
    float premiumGlow = 0.0f;
    float premiumPulse = 0.0f;
    float time = 0.0f;
};

/**
 * @brief Premium colors for consistent theming
 */
namespace PremiumColors {
    inline const ImVec4 PREMIUM_GOLD = ImVec4(1.0f, 0.84f, 0.0f, 1.0f);
    inline const ImVec4 PREMIUM_GOLD_LIGHT = ImVec4(1.0f, 0.92f, 0.4f, 1.0f);
    inline const ImVec4 PREMIUM_GOLD_DARK = ImVec4(0.8f, 0.67f, 0.0f, 1.0f);
    inline const ImVec4 LOCK_OVERLAY = ImVec4(0.0f, 0.0f, 0.0f, 0.7f);
    inline const ImVec4 UPGRADE_BUTTON = ImVec4(0.2f, 0.7f, 1.0f, 1.0f);
    inline const ImVec4 UPGRADE_BUTTON_HOVER = ImVec4(0.3f, 0.8f, 1.0f, 1.0f);
} 