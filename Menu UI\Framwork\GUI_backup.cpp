#define IMGUI_DEFINE_MATH_OPERATORS
#include "GUI.h"
#include "../Components/Components.h"
#include <functional>
#include "../../Utils.h"
#include "../Components/nav_elements.h"
#include "../Fonts/icons.h"
#include "../Fonts/font_defines.h"
#include "../Fonts/custom_icons.h"
#include <iostream>
#include "../../Cheat Core/Features/Config/Config.h"
#include "../../Cheat Core/Settings/Settings.h"
#include <algorithm>  // for std::transform
#include <string.h>    // for strstr
#include <sstream>     // for std::istringstream
#include "../HImGuiImageManager/HImGuiImageManager.h"
#include <vector>      // for std::vector
#include <cmath>       // for sin/cos/pow functions
#include <cstdlib>     // for rand()
#include "GUI_Helper.h"
#include "../../Menu UI/ImGui/stb_image.h"
#include <chrono>
#include "../HImGuiImageManager/Logger.h"


// Custom icon definitions for missing icons
#define ICON_DIRECTION_LINE "\uea77"
#define ICON_USER_LINE "\uf409"
#define ICON_USER_UNFOLLOW_LINE "\uf3fe"
#define ICON_ROBOT_LINE "\uf157"
#define ICON_SPEED_LINE "\uf288"
#define ICON_USER_SETTINGS_LINE "\uf401"
#define ICON_GUN_LINE "\uee0e"
#define ICON_FOCUS_2_LINE "\uee99"
#define ICON_FOCUS_3_LINE "\uee9a"
#define ICON_CIRCLE_FILL "\uea80"
#define ICON_CIRCLE_LINE "\uea81"
#define ICON_BOX_LINE "\uea4e"
#define ICON_BOX_3D_FILL "\uebbc"
#define ICON_TEAM_LINE "\uf2fe"
#define ICON_GHOST_LINE "\uee11"
#define ICON_ID_CARD_LINE "\uef21"
#define ICON_MEDAL_LINE "\uefaa"
#define ICON_GAME_PAD_LINE "\uee08"
#define ICON_RULER_LINE "\uf167"
#define ICON_FONT_SIZE_LINE "\uee9c"
#define ICON_DOWNLOAD_LINE "\ued1d"
#define ICON_SHARE_LINE "\uf1e9"
#define ICON_DELETE_BIN_LINE "\uec45"
#define ICON_CHECKBOX_CIRCLE_FILL "\ueb9e"
#define ICON_SEARCH_LINE "\uf0d1"
#define ICON_ADD_LINE "\uea12"
#define ICON_SETTINGS_LINE "\uf1de"
#define ICON_CIRCLE_FILL "\uea80"

#define ICON_CORNER_LINE "\ueb03"
#define ICON_SHAPE_LINE "\uf1dc"
#define ICON_BORDER_RADIUS_LINE "\uea47"
#define ICON_LINE_HEIGHT_LINE "\uef57"
#define ICON_SKELETON_LINE "\uf217"
#define ICON_HEAD_LINE "\ueed2"
#define ICON_MEDICINE_BOTTLE_LINE "\uefae"
#define ICON_CAPSULE_LINE "\uea5a"
#define ICON_FIRST_AID_KIT_LINE "\uee73"
#define ICON_SHIELD_LINE "\uf1ea"
#define ICON_WATER_FLASH_LINE "\uf4e1"
#define ICON_BOTTLE_LINE "\uea49"
#define ICON_AMMO_LINE "\uea22"
#define ICON_BULLET_LINE "\uea56"
#define ICON_ROCKET_LINE "\uf14f"
#define ICON_TREASURE_CHEST_FILL "\uf39b"
#define ICON_CAR_LINE "\uea5d"
#define ICON_PINWHEEL_LINE "\uf0c3"
#define ICON_BOX_2_LINE "\uea4d"
#define ICON_KEYBOARD_LINE "\uef4a"
#define ICON_KEY_LINE "\uef46"
#define ICON_HOTKEY_LINE "\uef0a"
#define ICON_TOGGLE_LINE "\uf361"
#define ICON_SETTINGS_LINE "\uf1d7"
#define ICON_GLOBAL_LINE "\ueef7"
#define ICON_TRANSLATE_LINE "\uf347"
#define ICON_INFORMATION_LINE "\uef30"
#define ICON_ANIMATION_LINE "\uea2d"
#define ICON_SAVE_LINE "\uf17c"
#define ICON_UPLOAD_LINE "\uf3e9"
#define ICON_DOWNLOAD_LINE "\uec44"
#define ICON_RESTART_LINE "\uf149"
#define ICON_VERSION_LINE "\ueaeb"
#define ICON_CONTACTS_LINE "\ueb9f"
#define ICON_CALENDAR_LINE "\uea58"
#define ICON_MAIL_LINE "\uef8a"
#define ICON_FILE_LIST_3_LINE "\uedd7"

// Add missing icon definitions
#define ICON_WIDTH_FILL "\uf533"
#define ICON_FONT_SIZE_FILL "\uee9b"
#define ICON_WATER_FLASH_FILL "\uf4e0"
#define ICON_SPEED_FILL "\uf287"
#define ICON_SHELL_FILL "\uf1de"
#define ICON_ROBOT_FILL "\uf156"
#define ICON_PERSON_FILL "\uf21e"
#define ICON_KEY_FILL "\uef45"
#define ICON_IMAGE_FILL "\uef16"
#define ICON_GUN_FILL "\uee0d"
#define ICON_GAMEPAD_FILL "\uee07"
#define ICON_CROSSHAIR_FILL "\ueb07"
#define ICON_BULLET_FILL "\uea55"
#define ICON_BLUR_FILL "\uea48"
#define ICON_AMMO_FILL "\uea21"
#define ICON_RADAR_FILL "\uf12b"
#define ICON_HEART_FILL "\ueedd"
#define ICON_TEXT_FILL "\uf32e"
#define ICON_RULER_FILL "\uf166"
#define ICON_SHIELD_FILL "\uf1e9"
#define ICON_LINE_FILL "\uef52"
#define ICON_SQUARE_FILL "\uf290"
#define ICON_CHECKBOX_FILL "\uea78"
#define ICON_BODY_FILL "\uea4b"
#define ICON_ZOOM_IN_FILL "\uf54d"
#define ICON_PACKAGE_FILL "\uf0a6"
#define ICON_CAR_FILL "\uea5c"
#define ICON_LOCK_FILL "\uef69"
#define ICON_FOLDER_OPEN_FILL "\uee81"
#define ICON_COLOR_FILL "\ueb16"
#define ICON_INVENTORY_FILL "\uef35"
#define ICON_MENU_FILL "\uef86"
#define ICON_TRANSLATE_FILL "\uf346"
#define ICON_INFORMATION_FILL "\uef2f"
#define ICON_TOGGLE_FILL "\uf360"

//new
#define ICON_DELETE        "\uf5de"    // Delete/trash icon
#define ICON_DOWNLOAD      "\uf407"    // Download icon
#define ICON_UPLOAD        "\uf435"    // Upload icon
#define ICON_SHARE         "\uf3ef"    // Share icon
#define ICON_NEW           "\uf49e"    // Plus/new icon
#define ICON_HASHTAG       "\uf292"    // Hashtag icon
#define ICON_USER          "\uf2bd"    // User profile icon

// Enable logging for debugging
#define DEBUG_LOG 1

#if DEBUG_LOG
#define LOG_DEBUG(msg, ...) printf("[DEBUG] " msg "\n", ##__VA_ARGS__)
#else
#define LOG_DEBUG(msg, ...)
#endif

// External DirectX resources from main.cpp
extern IDXGISwapChain* g_pSwapChain;

CGui pGUi = CGui();

float accent_color[4] = { 0.f / 255.f, 145.f / 255.f, 255.f / 255.f, 1.00f };

// Animation helper methods
float CGui::EaseOutExpo(float x) {
    return x == 1.0f ? 1.0f : 1.0f - powf(2.0f, -10.0f * x);
}

float CGui::EaseInOutQuad(float x) {
    return x < 0.5f ? 2.0f * x * x : 1.0f - powf(-2.0f * x + 2.0f, 2.0f) / 2.0f;
}

// Gradient text rendering helper - relative to current window
void CGui::RenderGradientText(const char* text, ImVec2 pos, ImColor startColor, ImColor endColor, ImFont* font) {
    // Use current window draw list instead of foreground
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Get current cursor position if pos is zero
    ImVec2 cursorPos = ImGui::GetCursorPos();
    if (pos.x == 0 && pos.y == 0) {
        pos = cursorPos;
    }

    // Calculate text size for gradient end position
    ImVec2 textSize = ImGui::CalcTextSize(text);

    // Set the cursor position
    ImGui::SetCursorPos(pos);

    // Get the absolute position for drawing
    ImVec2 textPos = ImGui::GetCursorScreenPos();

    // Horizontally center the text in window
    ImVec2 windowSize = ImGui::GetWindowSize();
    textPos.x = textPos.x + (windowSize.x - textSize.x) * 0.5f - ImGui::GetScrollX();

    // Push font if provided
    if (font) {
        ImGui::PushFont(font);
    }

    // Get vertex buffer index before drawing text
    const int vtx_idx_start = drawList->VtxBuffer.Size;

    // Draw the text (this adds vertices to the buffer)
    drawList->AddText(textPos, ImColor(1.0f, 1.0f, 1.0f, 1.0f), text);

    // Get vertex buffer index after drawing text
    const int vtx_idx_end = drawList->VtxBuffer.Size;

    // Apply gradient to the text vertices
    ImGui::ShadeVertsLinearColorGradientSetAlpha(
        drawList,
        vtx_idx_start,
        vtx_idx_end,
        textPos,
        ImVec2(textPos.x + textSize.x, textPos.y),
        startColor,
        endColor
    );

    // Pop font if pushed
    if (font) {
        ImGui::PopFont();
    }

    // Add spacing after text - advance cursor
    //ImGui::SetCursorPosY(pos.y + textSize.y);

    // Ensure ImGui knows this space was used (for layout)
    ImGui::Dummy(ImVec2(textSize.x, textSize.y));
}

// Gradient rectangle drawing helper for current window
void CGui::DrawGradientRect(ImRect rect, ImU32 color1, ImU32 color2, float thickness) {
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Left part of the gradient
    const int vtx_idx_1 = drawList->VtxBuffer.Size;
    drawList->AddRectFilled(rect.Min, ImVec2(rect.GetCenter().x, rect.Max.y), color1);
    drawList->AddShadowRect(rect.Min, ImVec2(rect.GetCenter().x - 5.5f, rect.Max.y), color1, thickness, ImVec2(0, 0));
    const int vtx_idx_2 = drawList->VtxBuffer.Size;
    ImGui::ShadeVertsLinearColorGradientSetAlpha(drawList, vtx_idx_1, vtx_idx_2, rect.Min, ImVec2(rect.GetCenter().x, rect.Max.y), color1, color2);

    // Right part of the gradient
    const int vtx_idx_3 = drawList->VtxBuffer.Size;
    drawList->AddRectFilled(ImVec2(rect.GetCenter().x, rect.Min.y), rect.Max, color1);
    drawList->AddShadowRect(ImVec2(rect.GetCenter().x + 5.5f, rect.Min.y), rect.Max, color1, thickness, ImVec2(0, 0));
    const int vtx_idx_4 = drawList->VtxBuffer.Size;
    ImGui::ShadeVertsLinearColorGradientSetAlpha(drawList, vtx_idx_3, vtx_idx_4, ImVec2(rect.GetCenter().x, rect.Min.y), rect.Max, color2, color1);
}

// Render gradient separator line - relative to current window
void CGui::RenderGradientSeparator(float y_offset, float width_percentage = 0.75f) {
    // Get current window draw list
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Save current cursor position
    ImVec2 cursorPos = ImGui::GetCursorPos();

    // Calculate separator position
    ImVec2 windowSize = ImGui::GetWindowSize();
    float line_height = 2.0f; // Height of the separator line

    // Calculate the width based on percentage of window width
    float line_width = (windowSize.x - ImGui::GetStyle().WindowPadding.x * 2) * width_percentage;

    // Set cursor position for the separator
    ImGui::SetCursorPosY(cursorPos.y + y_offset);

    // Get screen position for drawing
    ImVec2 lineStart = ImGui::GetCursorScreenPos();

    // Calculate center position for alignment
    float remaining_space = windowSize.x - ImGui::GetStyle().WindowPadding.x * 2 - line_width;
    float start_x_offset = remaining_space / 2.0f; // Center the line

    // Adjust lineStart to center the line
    lineStart.x += start_x_offset;

    // Draw the main filled rect for the separator line
    drawList->AddRectFilled(
        lineStart,
        ImVec2(lineStart.x + line_width, lineStart.y + line_height),
        ImGui::ColorConvertFloat4ToU32(ImVec4(accent_color[2], accent_color[1], accent_color[0], 1.0f))
    );

    // Create a rectangle for the gradient effect
    ImRect gradient_rect(
        lineStart,
        ImVec2(lineStart.x + line_width, lineStart.y + line_height)
    );

    // Apply the gradient effect
    DrawGradientRect(
        gradient_rect,
        ImColor(1.0f, 1.0f, 1.0f, 0.1f),
        ImGui::ColorConvertFloat4ToU32(ImVec4(accent_color[2], accent_color[1], accent_color[0], 1.0f)),
        35.0f
    );

    // Restore cursor position with an offset for spacing
    ImGui::SetCursorPosY(cursorPos.y + y_offset + line_height + 5.0f);
}

// Helper to render a section header that scrolls with the content
void CGui::RenderSectionHeader(const char* title, float titleY, float separatorY) {
    // Save cursor position for ImGui layout
    ImVec2 startCursorPos = ImGui::GetCursorPos();

    // Set cursor position for the title if specified
    if (titleY > 0) {
        ImGui::SetCursorPosY(titleY);
    }

    // Render the title with gradient
    RenderGradientText(
        title,
        ImVec2(0, 0), // Use current cursor position
        ImColor(accent_color[2], accent_color[1], accent_color[0], 1.0f),
        ImColor(1.0f, 1.0f, 1.0f, 1.0f)
    );

    // Render the separator line
    RenderGradientSeparator(0, 0.75f); // Use current cursor position

    // Add spacing after the section header
    ImGui::Dummy(ImVec2(0, 8));
}

void CGui::Initialize(ID3D11Device* g_pd3dDevice) {
    ImGuiIO& io = ImGui::GetIO();
    (void)io;
    ImFontConfig fontConfig;
    fontConfig.MergeMode = true;
    fontConfig.PixelSnapH = true;
    static ImFontConfig icons_config;
    icons_config.OversampleH = icons_config.OversampleV = 1;
    icons_config.MergeMode = true;
    icons_config.GlyphOffset.y = 6.5f;

    static ImFontConfig icomoon_config2;
    icomoon_config2.OversampleH = icomoon_config2.OversampleV = 1;
    icomoon_config2.MergeMode = true;
    icomoon_config2.GlyphOffset.y = 6.5f;

    auto fontPath = LoadChinese();
    std::string savepath = "C:\\BitCheats\\IconsFont.ttf";
    io.Fonts->AddFontFromMemoryTTF(&normal_font, sizeof(normal_font), 20, NULL, io.Fonts->GetGlyphRangesCyrillic());
    if (!fontPath.empty()) {
        io.Fonts->AddFontFromFileTTF(fontPath.c_str(), 20, &fontConfig, nav_elements::GetGlyphRangesChinese());
    }
    io.AddInputCharacter(0x410);
    io.AddInputCharacter(0x4E00);
    static ImWchar icomoon_ranges[] = { 0x1, 0x10FFFD, 0 };

    ico = io.Fonts->AddFontFromMemoryTTF(&icon, sizeof sizeof(icon), 23.0f);
    Personfont = io.Fonts->AddFontFromMemoryTTF(all_icons, sizeof(all_icons), 300.0f);
    tab_icons = io.Fonts->AddFontFromMemoryTTF(all_icons, sizeof(all_icons), 20.0f);

    static const ImWchar icons_ranges[] = { ICON_MIN_MS, ICON_MAX_16_MS, 0 };


    ImFontConfig small_cfg = icons_config;
    small_cfg.MergeMode = false;             // ← this is the key
    small_cfg.OversampleH = icons_config.OversampleV = 1;
    small_cfg.GlyphOffset.y = 6.5f;

    float baseSize = 55.0f * 1.3f / 2.0f;   // ≈ 35.75px
    float smallSize = 45.0f * 1.3f / 2.0f;   // ≈ 35.75px

    // big icons (merged into your default font)
    iconsBig = io.Fonts->AddFontFromFileTTF(
        savepath.c_str(), baseSize, &icons_config, icons_ranges);

    // small icons (stand-alone, separate atlas entry)
    iconsSmall = io.Fonts->AddFontFromFileTTF(
        savepath.c_str(), smallSize, &small_cfg, icons_ranges);


    nav_elements::Theme();

    // Initialize states
    isMenuOpen = false;
    lastTabChangeTime = ImGui::GetTime();

    // Initialize the animation system
    InitializeAnimations();

    // Initialize the configuration system
    InitializeConfigSystem();
}

void CGui::Render(bool& ShowMenu) {
    // Update animation system
    UpdateAnimations();
    
    // Animated menu state tracking
    if (ShowMenu != isMenuOpen) {
        isMenuOpen = ShowMenu;
        lastMenuToggleTime = ImGui::GetTime();
        
        if (isMenuOpen) {
            // Take snapshot of current settings when menu is opened
            g_ConfigSnapshot.CaptureSettings();
            // Clear notifications when menu is opened to fix the bug
            g_NotificationManager.ClearChanges();
            // Start show animation
            StartMenuShowAnimation();
        } else {
            // Track changes and save config when menu is hidden
            TrackConfigChanges();
            // Start hide animation
            StartMenuHideAnimation();
        }
    }

    // Always render profile window first
    this->RenderProfileWindow();
    
    // g_NotificationManager.ShowNotifications(); // Disabled for cleaner UI without animated lines

#ifndef DISABLE_ANIMATIONS
    // Get window animation state for both header and main menu
    auto& earlyWindowState = AnimationSystem::g_AnimationManager.GetWindowState();
    
    // Render header window with same visibility logic as main menu 
    if (ShowMenu || earlyWindowState.isVisible || earlyWindowState.isAnimating) {
        this->RenderHeaderWindow();
    }
    
    // Skip rendering main window if not visible and not animating
    if (!ShowMenu && !earlyWindowState.isVisible && !earlyWindowState.isAnimating) {
        return;
    }
#else
    // Without animations, render header when menu is visible
    if (ShowMenu) {
        this->RenderHeaderWindow();
    }
    
    // Without animations, use simple menu state check
    if (!ShowMenu) {
        return;
    }
#endif

    // Handle tab switching with animations
    float currentTime = ImGui::GetTime();
    if (currentTab != previousTab) {
        StartTabSwitchAnimation(previousTab, currentTab);
        previousTab = currentTab;
        lastTabChangeTime = currentTime;
        featureStartTime = currentTime;
    }

    // Simple fade animation without scaling or overlays
    ImGui::SetNextWindowSize(ImVec2(1020, 635));
    
#ifndef DISABLE_ANIMATIONS
    // Only apply simple alpha fade, no scaling
    auto& mainBgState = AnimationSystem::g_AnimationManager.GetWindowState();
    if (mainBgState.isAnimating) {
        ImGui::SetNextWindowBgAlpha(mainBgState.alpha);
    } else {
        // Default transparency for the main menu window
        ImGui::SetNextWindowBgAlpha(0.95f);
    }
#else
    // Default transparency when animations are disabled
    ImGui::SetNextWindowBgAlpha(0.95f);
#endif

    if (!BeginAnimatedWindow("##MainWindow", &ShowMenu, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse)) {
        EndAnimatedWindow();
        return;
    }
    {
        // Apply content alpha animation only within this window context
#ifndef DISABLE_ANIMATIONS
        auto& mainWindowState = AnimationSystem::g_AnimationManager.GetWindowState();
        if (mainWindowState.alpha < 1.0f) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, mainWindowState.alpha);
        }
#endif
        gui.window_pos = ImGui::GetWindowPos();
        gui.window_size = ImGui::GetWindowSize();

        auto draw = ImGui::GetWindowDrawList();
        const auto& p = ImGui::GetWindowPos();
        ImGuiStyle& style = ImGui::GetStyle();

        // Navigation tabs moved to top
        ImGui::SetCursorPos(ImVec2(10, 10));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(81 / 255.0f, 65 / 255.0f, 55 / 255.0f, 0.5f));
        // Use exact same background color as main window
        ImVec4 tabsBgColor = ImGui::GetStyleColorVec4(ImGuiCol_WindowBg);
#ifndef DISABLE_ANIMATIONS
        auto& tabsBgState = AnimationSystem::g_AnimationManager.GetWindowState();
        tabsBgColor.w = tabsBgState.isAnimating ? tabsBgState.alpha : 0.95f;
#else
        tabsBgColor.w = 0.95f; // Default transparency to match main menu
#endif
        ImGui::PushStyleColor(ImGuiCol_ChildBg, tabsBgColor);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.5f);
        ImGui::BeginChild("Tabs", ImVec2(200, 615), ImGuiChildFlags_Border, ImGuiWindowFlags_NoScrollbar);
        {
            ImGui::SetCursorPos(ImVec2(0, 50));
            if (nav_elements::Tab("AIMBOT", ICON_MS_TARGET, &currentTab, ModernUI::NavigationPanel::TAB_AIMBOT)) {}
            if (nav_elements::Tab("VISUALS", ICON_MS_VISIBILITY, &currentTab, ModernUI::NavigationPanel::TAB_VISUALS)) {}
            if (nav_elements::Tab("ITEMS", ICON_MS_INVENTORY, &currentTab, ModernUI::NavigationPanel::TAB_ITEMS)) {}
            if (nav_elements::Tab("RADAR", ICON_MS_RADAR, &currentTab, ModernUI::NavigationPanel::TAB_RADAR)) {}
            if (nav_elements::Tab("SETTINGS", ICON_MS_SETTINGS, &currentTab, ModernUI::NavigationPanel::TAB_SETTINGS)) {}
            if (nav_elements::Tab("CONFIGS", ICON_MS_FOLDER, &currentTab, ModernUI::NavigationPanel::TAB_CONFIGS)) {}
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(); // Pop border size
        ImGui::PopStyleColor(2); // Pop border color and main window background color for navigation tabs

        // Content area with animations
        ImGui::SetCursorPos(ImVec2(220, 10));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(81 / 255.0f, 65 / 255.0f, 55 / 255.0f, 0.5f));
        // Use exact same background color as main window
        ImVec4 contentBgColor = ImGui::GetStyleColorVec4(ImGuiCol_WindowBg);
#ifndef DISABLE_ANIMATIONS
        auto& contentBgState = AnimationSystem::g_AnimationManager.GetWindowState();
        contentBgColor.w = contentBgState.isAnimating ? contentBgState.alpha : 0.95f;
#else
        contentBgColor.w = 0.95f; // Default transparency to match main menu
#endif
        ImGui::PushStyleColor(ImGuiCol_ChildBg, contentBgColor);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.5f);
        ImGui::BeginChild("ContentArea", ImVec2(790, 615), ImGuiChildFlags_Border);
        {
            BeginAnimatedContent();
            
            ImGui::Indent(5.0f);
            
            // Animated page title and description
            RenderAnimatedElement(0, [this]() {
                ImGui::Dummy(ImVec2(0, 15));
                
                // Get tab name and description
                const char* tabName = GetTabName(currentTab);
                const char* tabDesc = GetTabDescription(currentTab);
                
                // Title with larger font and accent color
                ImGui::PushFont(iconsBig);
                ImGui::TextColored(ImVec4(accent_color[2], accent_color[1], accent_color[0], 1.0f), "%s", tabName);
                ImGui::PopFont();
                
                // Description on same line with spacing
                ImGui::SameLine();
                ImGui::Dummy(ImVec2(20, 0)); // Add some spacing
                ImGui::SameLine();
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "- %s", tabDesc);
                
                ImGui::Dummy(ImVec2(0, 10));
            });
            
#ifndef DISABLE_ANIMATIONS
            // Render tab content with smooth transitions
            auto& tabState = AnimationSystem::g_AnimationManager.GetTabState();
            
            // Removed animated tab transition overlay for cleaner UI
#endif
            
            // Render current tab content with animations
            RenderAnimatedElement(1, [this]() {
                switch (currentTab) {
                    case ModernUI::NavigationPanel::TAB_AIMBOT:
                        RenderAimbotTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_VISUALS:
                        RenderVisualsTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_ITEMS:
                        RenderMainTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_RADAR:
                        RenderRadarTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_SETTINGS:
                        RenderSettingsTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_CONFIGS:
                        RenderConfigsTab();
                        break;
                }
            });

            ImGui::Unindent(5.0f);
            ImGui::Dummy(ImVec2(100, 10));
            
            EndAnimatedContent();
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(); // Pop border size
        ImGui::PopStyleColor(2); // Pop border color and main window background color for content area
        
        // Pop content alpha animation if it was applied
#ifndef DISABLE_ANIMATIONS
        auto& mainContentState = AnimationSystem::g_AnimationManager.GetWindowState();
        if (mainContentState.alpha < 1.0f) {
            ImGui::PopStyleVar(); // Pop content alpha
        }
#endif
    }
    EndAnimatedWindow();

    ImGuiContext& context = *GImGui;
    ImGuiStyle& style = ImGui::GetStyle();

    // Force the dark overlay to be visible when picker_active is set
    // This ensures the overlay appears immediately when the popup is opened
    static float dark_overlay = 0.f;

    // Check if any popup with "ColorPickerPopup" in the name is open
    bool isColorPickerPopupOpen = ImGui::IsPopupOpen("##ColorPickerPopup", ImGuiPopupFlags_AnyPopupId);

    // Additional check to prevent overlay from showing during hover states
    // Check if we're just hovering over interactive elements without opening popup
    bool isHoveringInteractiveElement = false;

    // Specifically check for color-picker button hovers that should NOT trigger overlay
    // Only consider it hovering if we're showing tooltips on non-popup elements
    if (!isColorPickerPopupOpen && context.HoveredId != 0) {
        // Check if we're hovering over a color button or settings button
        // These should show tooltips but NOT trigger the dark overlay
        ImGuiWindow* hoveredWindow = context.HoveredWindow;
        if (hoveredWindow && !(hoveredWindow->Flags & ImGuiWindowFlags_Popup)) {
            // We're hovering over a main window element (not a popup)
            // Check if it's just a tooltip hover without any picker state
            // Use ImGui::IsItemHovered() to detect if we're just hovering over UI elements
            if (context.HoveredId != 0 && !gui.picker_active) {
                isHoveringInteractiveElement = true;
            }
        }
    }

    // Only activate picker_active if popup is actually open, not just on hover
    static int delayCounter = 0;
    if (gui.picker_active && !isColorPickerPopupOpen && !isHoveringInteractiveElement) {
        if (delayCounter < 5) {
            delayCounter++;
            isColorPickerPopupOpen = true;
        }
        else {
            gui.picker_active = false;
            delayCounter = 0;
        }
    }
    else if (isColorPickerPopupOpen && !isHoveringInteractiveElement) {
        gui.picker_active = true;
        delayCounter = 0;
    }
    else if (isHoveringInteractiveElement) {
        // Reset picker_active if we're just hovering
        gui.picker_active = false;
        delayCounter = 0;
    }

    // Color picker overlay disabled to prevent transparency issues

    // Sync settings with global variables to apply changes in real-time
    SettingsHelper::SyncSettings();

    // Profile and header windows already rendered earlier - don't render again
    // g_NotificationManager.ShowNotifications(); // Disabled for cleaner UI without animated lines
    
    // Final safeguard: Reset any potential global alpha leaks from UI systems
    ImGuiContext* ctx = ImGui::GetCurrentContext();
    ctx->Style.Alpha = 1.0f; // Ensure global alpha is always reset to full opacity
}

// Simple feature animation - no longer used for individual elements
bool CGui::AnimateNextFeature() {
    featureIndex++;
    return true; // Always render content immediately
}

// Removed animated water background for cleaner UI
void CGui::RenderIOSWaterAnimation(float progress, ImVec2 contentSize) {
    // Animation disabled for cleaner menu experience
    return;
}

// Simple slide-up animation for content elements
void CGui::AnimateContentElement(float delay) {
    const float baseDelay = 0.04f; // Stagger timing between elements
    const float animationDuration = 0.5f; // Animation duration
    
    float animationStartTime = featureStartTime + (featureIndex * baseDelay) + delay;
    float currentTime = ImGui::GetTime();
    float elapsed = currentTime - animationStartTime;

    // Only apply animation if we're in the animation timeframe
    if (elapsed >= 0.0f && elapsed <= animationDuration) {
        float progress = elapsed / animationDuration;
        
        // Simple slide up animation
        float easedProgress = EaseOutBack(progress);
        float slideOffset = (1.0f - easedProgress) * 25.0f;
        
        // Apply the slide offset
        ImVec2 currentPos = ImGui::GetCursorPos();
        ImGui::SetCursorPos(ImVec2(currentPos.x, currentPos.y + slideOffset));
    }

    featureIndex++;
}

// Helper function to get tab name
const char* CGui::GetTabName(int tabIndex) {
    switch (tabIndex) {
        case 0: return "Aimbot";
        case 1: return "Visuals";
        case 2: return "Items";
        case 3: return "Radar";
        case 4: return "Settings";
        case 5: return "Configs";
        default: return "Unknown";
    }
}

// Helper function to get tab description
const char* CGui::GetTabDescription(int tabIndex) {
    switch (tabIndex) {
        case 0: return "Advanced targeting and aim assistance features";
        case 1: return "ESP, player visualization and rendering options";
        case 2: return "Item and loot highlighting configurations";
        case 3: return "Radar and minimap display settings";
        case 4: return "Application preferences and configurations";
        case 5: return "Save, load and share configuration profiles";
        default: return "Unknown tab";
    }
}

// iOS-style smooth easing functions
float CGui::EaseInOutCubic(float x) {
    return x < 0.5f ? 4.0f * x * x * x : 1.0f - powf(-2.0f * x + 2.0f, 3.0f) / 2.0f;
}

float CGui::EaseOutBack(float x) {
    const float c1 = 1.70158f;
    const float c3 = c1 + 1.0f;
    return 1.0f + c3 * powf(x - 1.0f, 3.0f) + c1 * powf(x - 1.0f, 2.0f);
}

// Removed animated tab transition for cleaner UI
void CGui::RenderTabTransition(float progress, ImVec2 contentSize) {
    // Animation disabled for cleaner menu experience
    return;
}

// Star data structure for glowing animation
struct StarData {
    ImVec2 position;
    ImVec2 velocity;
    float brightness;
    float brightnessPhase;
    float size;
    float sizePhase;
    ImVec4 color;
};

// Removed animated glowing stars for cleaner UI
void CGui::RenderGlowingStars(ImVec2 contentSize) {
    // Animation disabled for cleaner menu experience
    return;
}

void CGui::RenderAimbotTab() {
    // Render all content immediately - no individual delays
    //RenderSectionHeader("Aimbot Settings", 0, 0);

    // Main aimbot toggles
    if (Settings.Aim.AimbotConfig.ByWeapon) {
        Aimbots& weaponSettings = Settings.Aim.WeaponConfigs.weaponAimSettings[Settings.Config.ConfigMenu.selectedWeapon];

        // All checkboxes render immediately for smooth scrolling
            nav_elements::CheckboxComponent("Enable Aimbot", &weaponSettings.Enable, "Activate the aimbot functionality^" ICON_MS_TARGET, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotEnable, &Keys.AimbotEnableMode, false);
            nav_elements::CheckboxComponent("Aim Lock", &weaponSettings.AimLock, "Keep aim locked on target^" ICON_MS_LOCK, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true);
            nav_elements::CheckboxComponent("Prediction", &weaponSettings.Predict, "Predict target movement^" ICON_MS_TRENDING_UP, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Save Target", &weaponSettings.SaveTarget, "Remember last aimed target^" ICON_MS_SAVE, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Visibility Check", &weaponSettings.VisibilityCheck, "Only aim at visible targets^" ICON_MS_VISIBILITY, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Humanized Smooth", &weaponSettings.HumanizedSmooth, "Add human-like smoothing^" ICON_MS_PERSON, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Ignore Knocked Players", &weaponSettings.IgnoreDowned, "Skip downed players^" ICON_MS_PERSON_OFF, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Target Player AI", &weaponSettings.PlayerAi, "Include AI players as targets^" ICON_MS_SMART_TOY, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Weapon Only", &weaponSettings.WeaponOnly, "Only activate when holding weapon^" ICON_MS_GAVEL, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        } else {
            // Standard mode - all content renders immediately
            nav_elements::CheckboxComponent("Enable Aimbot", &Settings.Aim.AimbotConfig.Enable, "Activate the aimbot functionality^" ICON_MS_TARGET, true, std::vector<nav_elements::ColorState>{}, &Keys.AimbotEnable, &Keys.AimbotEnableMode, false);
            nav_elements::CheckboxComponent("Aim Lock", &Settings.Aim.AimbotConfig.AimLock, "Keep aim locked on target^" ICON_MS_LOCK, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true);
            nav_elements::CheckboxComponent("Prediction", &Settings.Aim.AimbotConfig.Predict, "Predict target movement^" ICON_MS_TRENDING_UP, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Save Target", &Settings.Aim.AimbotConfig.SaveTarget, "Remember last aimed target^" ICON_MS_SAVE, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Visibility Check", &Settings.Aim.AimbotConfig.VisibilityCheck, "Only aim at visible targets^" ICON_MS_VISIBILITY, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Humanized Smooth", &Settings.Aim.AimbotConfig.HumanizedSmooth, "Add human-like smoothing^" ICON_MS_PERSON, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Ignore Knocked Players", &Settings.Aim.AimbotConfig.IgnoreDowned, ("Skip downed players^" + std::string(ICON_MS_PERSON_OFF)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Target Player AI", &Settings.Aim.AimbotConfig.PlayerAi, ("Include AI players as targets^" + std::string(ICON_MS_SMART_TOY)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Weapon Only", &Settings.Aim.AimbotConfig.WeaponOnly, ("Only activate when holding weapon^" + std::string(ICON_MS_GAVEL)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
    }

    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Aimbot Configuration", 0, 0);

    // Aimbot configuration
    if (Settings.Aim.AimbotConfig.ByWeapon) {
        Aimbots& weaponSettings = Settings.Aim.WeaponConfigs.weaponAimSettings[Settings.Config.ConfigMenu.selectedWeapon];

            nav_elements::SliderInt("Max Distance", &weaponSettings.MaxDistance, 1, 1000, ("Maximum targeting distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");

        const char* hitboxes[] = { "Head", "Neck", "Chest", "Pelvis", "Random" };
            nav_elements::Combo("Hitbox", &weaponSettings.HitBox, hitboxes, IM_ARRAYSIZE(hitboxes), ("Target body part^" + std::string(ICON_MS_BODY_PART)).c_str());

            nav_elements::SliderFloat("Smoothness", &weaponSettings.Smooth, 1.0f, 20.0f, ("Aim movement smoothness^" + std::string(ICON_MS_SPEED)).c_str(), "%.1f");

            nav_elements::SliderFloat("Humanized Amount", &weaponSettings.HumanizedSmoothPercent, 1.0f, 10.0f, ("Human-like aim behavior level^" + std::string(ICON_MS_PERSON_SETTINGS)).c_str(), "%.1f");
    } else {
            nav_elements::SliderInt("Max Distance", &Settings.Aim.AimbotConfig.MaxDistance, 1, 1000, ("Maximum targeting distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");

        const char* hitboxes[] = { "Head", "Neck", "Chest", "Pelvis", "Random" };
            nav_elements::Combo("Hitbox", &Settings.Aim.AimbotConfig.HitBox, hitboxes, IM_ARRAYSIZE(hitboxes), ("Target body part^" + std::string(ICON_MS_BODY_PART)).c_str());

            nav_elements::SliderFloat("Smoothness", &Settings.Aim.AimbotConfig.Smooth, 1.0f, 20.0f, ("Aim movement smoothness^" + std::string(ICON_MS_SPEED)).c_str(), "%.1f");

            nav_elements::SliderFloat("Humanized Amount", &Settings.Aim.AimbotConfig.HumanizedSmoothPercent, 1.0f, 10.0f, ("Human-like aim behavior level^" + std::string(ICON_MS_PERSON_SETTINGS)).c_str(), "%.1f");
    }

    // Weapon selection section
    if (Settings.Aim.AimbotConfig.ByWeapon) {
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Weapon Selection", 0, 0);

        const char* weaponTypes[] = { "DMR", "SMG", "Shotgun", "Pistol", "AssaultRifle", "Sniper" };
            nav_elements::Combo("Weapon Type", &Settings.Config.ConfigMenu.selectedWeapon, weaponTypes, IM_ARRAYSIZE(weaponTypes), ("Select weapon for configuration^" + std::string(ICON_MS_WEAPONS)).c_str());
    }

    ImGui::Dummy(ImVec2(0, 10));
        nav_elements::CheckboxComponent("Configure By Weapon", &Settings.Aim.AimbotConfig.ByWeapon, ("Enable per-weapon configuration^" + std::string(ICON_MS_TUNE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

    // Visual indicators section
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Aimbot Visuals", 0, 0);

    // FOV settings
        nav_elements::CheckboxComponent("Draw FOV", &Settings.Aim.AimbotConfig.DrawFov, ("Show field of view circle^" + std::string(ICON_MS_CIRCLE)).c_str(), true, GetAimbotColorStates("Draw FOV"), nullptr, nullptr, false);

    if (Settings.Aim.AimbotConfig.DrawFov) {
            nav_elements::CheckboxComponent("FOV Filled", &Settings.Aim.AimbotConfig.DrawFovFilled, ("Fill the FOV circle^" + std::string(ICON_MS_CIRCLE_FILLED)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("FOV Outline", &Settings.Aim.AimbotConfig.DrawFovOutline, ("Draw FOV circle outline^" + std::string(ICON_MS_CIRCLE_OUTLINE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("FOV RGB Mode", &Settings.Aim.AimbotConfig.DrawFovRGB, ("Cycle RGB colors for FOV^" + std::string(ICON_MS_PALETTE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::SliderFloat("FOV Size", &Settings.Aim.AimbotConfig.FOV, 1.0f, 500.0f, ("Adjust FOV circle size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");
    }

    // Crosshair settings
        nav_elements::CheckboxComponent("Draw Crosshair", &Settings.Aim.AimbotConfig.DrawCrossHair, ("Show custom crosshair^" + std::string(ICON_MS_ADD_CIRCLE)).c_str(), true, GetAimbotColorStates("Draw Crosshair"), nullptr, nullptr, false);

    if (Settings.Aim.AimbotConfig.DrawCrossHair) {
        const char* crosshairTypes[] = { "Plus", "Circle Cross", "Cross Dot", "Square", "Circle Dot", "X Dot" };
            nav_elements::Combo("Crosshair Type", &Settings.Aim.AimbotConfig.DrawCrossHairType, crosshairTypes, IM_ARRAYSIZE(crosshairTypes), ("Select crosshair style^" + std::string(ICON_MS_CROSSHAIRS)).c_str());

            nav_elements::SliderFloat("Crosshair Size", &Settings.Aim.AimbotConfig.DrawSize, 1.0f, 50.0f, ("Adjust crosshair size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");
    }

        nav_elements::CheckboxComponent("Draw Target Line", &Settings.Aim.AimbotConfig.DrawTarget, ("Show line to current target^" + std::string(ICON_MS_TRENDING_FLAT)).c_str(), true, GetAimbotColorStates("Draw Target Line"), nullptr, nullptr, false);

        nav_elements::SliderFloat("Drawing Thickness", &Settings.Aim.AimbotConfig.DrawThickness, 1.0f, 5.0f, ("Adjust line thickness^" + std::string(ICON_MS_BRUSH)).c_str(), "%.1f");
}

void CGui::RenderVisualsTab() {
    // Render all content immediately for smooth scrolling
    //RenderSectionHeader("Players", 0, 0);

    // All player settings render at once
        nav_elements::CheckboxComponent("Enable Player ESP", &Settings.Esp.Player.Enable, ("Toggle player ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.PlayerEspEnable, &Keys.PlayerEspEnableMode, false);
        nav_elements::CheckboxComponent("Bounding Box", &Settings.Esp.Player.Box, ("Draw bounding box^" + std::string(ICON_MS_SQUARE)).c_str(), true, GetPlayerColorStates("Bounding Box"), nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Corner Box", &Settings.Esp.Player.Box, ("Draw corner box^" + std::string(ICON_MS_CROP_SQUARE)).c_str(), true, GetPlayerColorStates("Corner Box"), nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Skeleton", &Settings.Esp.Player.Skeleton, ("Draw skeleton^" + std::string(ICON_MS_SKELETON)).c_str(), true, GetPlayerColorStates("Skeleton"), nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Names", &Settings.Esp.Player.NickName, ("Show player names^" + std::string(ICON_MS_PERSON)).c_str(), true, GetPlayerColorStates("Names"), nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Distance", &Settings.Esp.Player.Distance, ("Show distance to players^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), true, GetPlayerColorStates("Distance"), nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Head Circle", &Settings.Esp.Player.HeadCircle, ("Draw head circle^" + std::string(ICON_MS_CIRCLE)).c_str(), true, GetPlayerColorStates("Head Circle"), nullptr, nullptr, false);
    /*nav_elements::CheckboxComponent("Health", &Settings.Esp.Player.Health, ("Show player health^" + std::string(ICON_MS_FAVORITE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true);
        nav_elements::CheckboxComponent("Shield", &Settings.Esp.Player.Shield, ("Show player shield^" + std::string(ICON_MS_SHIELD)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true);*/
        nav_elements::CheckboxComponent("Snapline", &Settings.Esp.Player.Lines, ("Draw snaplines^" + std::string(ICON_MS_TRENDING_FLAT)).c_str(), true, GetPlayerColorStates("Snapline"), nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Weapon", &Settings.Esp.Player.Weapon, ("Show player weapons^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true);

    ImGui::Dummy(ImVec2(0, 5));

        nav_elements::SliderInt("Max Distance", &Settings.Esp.Player.MaxDistance, 1, 500, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
        nav_elements::SliderFloat("Box Thickness", &Settings.Esp.Player.BoxThickness, 1.0f, 5.0f, ("Adjust box thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
        nav_elements::SliderFloat("Corner Box Thickness", &Settings.Esp.Player.BoxThickness, 1.0f, 5.0f, ("Adjust corner box thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
        nav_elements::SliderFloat("Text Size", &Settings.Esp.Player.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
        nav_elements::SliderFloat("Snapline Thickness", &Settings.Esp.Player.LinesThickness, 1.0f, 5.0f, ("Adjust snapline thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
        nav_elements::SliderFloat("Skeleton Thickness", &Settings.Esp.Player.SkeletonThickness, 1.0f, 5.0f, ("Adjust skeleton thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
        nav_elements::SliderFloat("Head Circle Thickness", &Settings.Esp.Player.HeadCircleThickness, 1.0f, 5.0f, ("Adjust head circle thickness^" + std::string(ICON_MS_LINE_WEIGHT)).c_str(), "%.1f");
}

void CGui::RenderMainTab() {
    // Tab selectors for loot categories
    static int lootTab = 0;
    ImGui::BeginGroup();

    if (nav_elements::HorizontalTab("Consumables", &lootTab, 0)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab("Weapons", &lootTab, 1)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab("Ammo", &lootTab, 2)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab("Other Items", &lootTab, 3)) {}

    ImGui::EndGroup();

        ImGui::Dummy(ImVec2(0, 10));

    // Show selected tab content
    switch (lootTab) {
        case 0: // Consumables
            RenderConsumablesTab();
            break;
        case 1: // Weapons
            RenderWeaponsTab();
            break;
        case 2: // Ammo
            RenderAmmoTab();
            break;
        case 3: // Other Items
            RenderOtherItemsTab();
            break;
    }
}

void CGui::RenderConsumablesTab() {
        // Consumables section
        //RenderSectionHeader("Consumables", 0, 0);

        // Consumable settings
        nav_elements::CheckboxComponent("Enable Consumables ESP", &Settings.Esp.Item.Consumable.Enable, ("Toggle consumables ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.ItemConsumableEnable, &Keys.ItemConsumableEnableMode);
        nav_elements::CheckboxComponent("Show Names", &Settings.Esp.Item.Consumable.Name, ("Display item names^" + std::string(ICON_MS_PERSON)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Distance", &Settings.Esp.Item.Consumable.Distance, ("Show distance to items^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Icons", &Settings.Esp.Item.Consumable.Icons, ("Display item icons^" + std::string(ICON_MS_IMAGE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

        nav_elements::SliderInt("Max Distance", &Settings.Esp.Item.Consumable.MaxDistance, 1, 300, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
        nav_elements::SliderFloat("Font Size", &Settings.Esp.Item.Consumable.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
        nav_elements::SliderFloat("Icon Size", &Settings.Esp.Item.Consumable.IconSize, 5.0f, 40.0f, ("Adjust icon size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");

    // Consumable items selection
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Consumables to Display", 0, 0);

        nav_elements::CheckboxComponent("Bandages", &Settings.Esp.Item.Consumable.Bandages, ("Show bandages^" + std::string(ICON_MS_HEALING)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Medkit", &Settings.Esp.Item.Consumable.Medkit, ("Show medkits^" + std::string(ICON_MS_MEDICAL_SERVICES)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Small Shield", &Settings.Esp.Item.Consumable.SmallShieldPotion, ("Show small shields^" + std::string(ICON_MS_SHIELD)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Big Shield", &Settings.Esp.Item.Consumable.ShieldPotion, ("Show big shields^" + std::string(ICON_MS_SHIELD)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Chug Splash", &Settings.Esp.Item.Consumable.ChugSplash, ("Show chug splash^" + std::string(ICON_MS_WATER_DROP)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Nitro Splash", &Settings.Esp.Item.Consumable.NitroSplash, ("Show nitro splash^" + std::string(ICON_MS_SCIENCE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("FlowBerry Fizz", &Settings.Esp.Item.Consumable.FlowBerryFizz, ("Show flowberry fizz^" + std::string(ICON_MS_BUBBLE_CHART)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Nuka-Cola", &Settings.Esp.Item.Consumable.NukaCola, ("Show nuka-cola^" + std::string(ICON_MS_LOCAL_DRINK)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
}

void CGui::RenderWeaponsTab() {
        // Weapons section
        //RenderSectionHeader("Weapons", 0, 0);

        // Weapon settings
        nav_elements::CheckboxComponent("Enable Weapons ESP", &Settings.Esp.Item.Weapon.Enable, ("Toggle weapons ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.ItemWeaponEnable, &Keys.ItemWeaponEnableMode);
        nav_elements::CheckboxComponent("Show Names", &Settings.Esp.Item.Weapon.Name, ("Display weapon names^" + std::string(ICON_MS_PERSON)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Distance", &Settings.Esp.Item.Weapon.Distance, ("Show distance to weapons^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Icons", &Settings.Esp.Item.Weapon.Icons, ("Display weapon icons^" + std::string(ICON_MS_IMAGE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

        nav_elements::SliderInt("Max Distance", &Settings.Esp.Item.Weapon.MaxDistance, 1, 300, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
        nav_elements::SliderFloat("Font Size", &Settings.Esp.Item.Weapon.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
        nav_elements::SliderFloat("Icon Size", &Settings.Esp.Item.Weapon.IconSize, 5.0f, 40.0f, ("Adjust icon size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");

        // Game mode selection
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Game Mode", 0, 0);

        const char* gameModes[] = { "Remix Battle Royale", "Other Modes" };
        nav_elements::Combo("Game Mode", &Settings.Config.ConfigMenu.GameMode, gameModes, IM_ARRAYSIZE(gameModes), ("Select game mode^" + std::string(ICON_MS_SPORTS_ESPORTS)).c_str());

        // Weapon display selection
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Weapons to Display", 0, 0);

        if (Settings.Config.ConfigMenu.GameMode == 0) { // Remix Battle Royale
            nav_elements::CheckboxComponent("New Pump Shotgun", &Settings.Esp.Item.Weapon.NewPump_Shotgun, ("Show pump shotgun^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("OG Pump Shotgun", &Settings.Esp.Item.Weapon.OGPump_Shotgun, ("Show OG pump shotgun^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Submachine Gun", &Settings.Esp.Item.Weapon.SubmachineGun, ("Show submachine gun^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Dual Pistols", &Settings.Esp.Item.Weapon.DualPistols, ("Show dual pistols^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Rapid Fire SMG", &Settings.Esp.Item.Weapon.RapidFireSMG, ("Show rapid fire SMG^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Suppressed SMG", &Settings.Esp.Item.Weapon.SuppressedSMG, ("Show suppressed SMG^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Suppressed Assault Rifle", &Settings.Esp.Item.Weapon.SuppressedAssaultRifle, ("Show suppressed AR^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Heavy Sniper Rifle", &Settings.Esp.Item.Weapon.HeavySniperRifle, ("Show heavy sniper^" + std::string(ICON_MS_CROSSHAIRS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Semi-Auto Sniper Rifle", &Settings.Esp.Item.Weapon.SemiAutomaticSniperRifle, ("Show semi-auto sniper^" + std::string(ICON_MS_CROSSHAIRS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Grenade Launcher", &Settings.Esp.Item.Weapon.GrenadeLauncher, ("Show grenade launcher^" + std::string(ICON_MS_ROCKET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Remote Explosives", &Settings.Esp.Item.Weapon.RemoteExplosives, ("Show remote explosives^" + std::string(ICON_MS_DANGEROUS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Regular Grenades", &Settings.Esp.Item.Weapon.RegularGrenades, ("Show grenades^" + std::string(ICON_MS_DANGEROUS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        } else { // Other Modes
            nav_elements::CheckboxComponent("Ranger Pistol", &Settings.Esp.Item.Weapon.RangerPistol, ("Show ranger pistol^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Harbinger SMG", &Settings.Esp.Item.Weapon.HarbingerSMG, ("Show harbinger SMG^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Thunder Burst SMG", &Settings.Esp.Item.Weapon.ThunderBurstSMG, ("Show thunder burst SMG^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Warforged Assault Rifle", &Settings.Esp.Item.Weapon.WarforgedAssaultRifle, ("Show warforged AR^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Tactical Assault Rifle", &Settings.Esp.Item.Weapon.TacticalAssaultRifle, ("Show tactical AR^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Combat Assault Rifle", &Settings.Esp.Item.Weapon.CombatAssaultRifle, ("Show combat AR^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Combat Shotgun", &Settings.Esp.Item.Weapon.CombatShotgun, ("Show combat shotgun^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Gatekeeper Shotgun", &Settings.Esp.Item.Weapon.GatekeeperShotgun, ("Show gatekeeper shotgun^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Hammer Pump Shotgun", &Settings.Esp.Item.Weapon.HammerPumpShotgun, ("Show hammer pump shotgun^" + std::string(ICON_MS_WEAPONS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Huntress DMR", &Settings.Esp.Item.Weapon.HuntressDMR, ("Show huntress DMR^" + std::string(ICON_MS_CROSSHAIRS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
            nav_elements::CheckboxComponent("Heavy Impact Sniper", &Settings.Esp.Item.Weapon.HeavyImpactSniperRifle, ("Show heavy impact sniper^" + std::string(ICON_MS_CROSSHAIRS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        }

        // Rarity filters
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Rarity Filter", 0, 0);

        const char* rarities[] = { "Common", "Uncommon", "Rare", "Epic", "Legendary", "Mythic" };
        for (int i = 0; i < 6; i++) {
        if (i > 0) ImGui::SameLine();
            nav_elements::CheckboxComponent(rarities[i], &Settings.Esp.Item.Weapon.Rarity[i], (std::string(rarities[i]) + "^" + std::string(ICON_MS_DIAMOND)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
    }
}

void CGui::RenderAmmoTab() {
        // Ammo section
        //RenderSectionHeader("Ammo", 0, 0);

        // Ammo settings
        nav_elements::CheckboxComponent("Enable Ammo ESP", &Settings.Esp.Item.Ammo.Enable, ("Toggle ammo ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), true, std::vector<nav_elements::ColorState>{}, &Keys.ItemAmmoEnable, &Keys.ItemAmmoEnableMode);
        nav_elements::CheckboxComponent("Show Names", &Settings.Esp.Item.Ammo.Name, ("Display ammo names^" + std::string(ICON_MS_PERSON)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Distance", &Settings.Esp.Item.Ammo.Distance, ("Show distance to ammo^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Icons", &Settings.Esp.Item.Ammo.Icons, ("Display ammo icons^" + std::string(ICON_MS_IMAGE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

        nav_elements::SliderInt("Max Distance", &Settings.Esp.Item.Ammo.MaxDistance, 1, 300, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
        nav_elements::SliderFloat("Font Size", &Settings.Esp.Item.Ammo.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
        nav_elements::SliderFloat("Icon Size", &Settings.Esp.Item.Ammo.IconSize, 5.0f, 40.0f, ("Adjust icon size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");

        // Ammo types
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Ammo Types to Display", 0, 0);

        nav_elements::CheckboxComponent("Light Ammo", &Settings.Esp.Item.Ammo.AmmoLight, ("Show light ammo^" + std::string(ICON_MS_BULLET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Medium Ammo", &Settings.Esp.Item.Ammo.AmmoMedium, ("Show medium ammo^" + std::string(ICON_MS_BULLET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Heavy Ammo", &Settings.Esp.Item.Ammo.AmmoHeavy, ("Show heavy ammo^" + std::string(ICON_MS_BULLET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Shells", &Settings.Esp.Item.Ammo.AmmoShells, ("Show shells^" + std::string(ICON_MS_BULLET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Rockets", &Settings.Esp.Item.Ammo.AmmoRockets, ("Show rockets^" + std::string(ICON_MS_ROCKET)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
}

void CGui::RenderOtherItemsTab() {
        // Other items section
        //RenderSectionHeader("Other Items", 0, 0);

        // Other items settings
        nav_elements::CheckboxComponent("Enable Other Items ESP", &Settings.Esp.Item.Other.Enable, ("Toggle other items ESP^" + std::string(ICON_MS_VISIBILITY)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Names", &Settings.Esp.Item.Other.Name, ("Display item names^" + std::string(ICON_MS_PERSON)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Distance", &Settings.Esp.Item.Other.Distance, ("Show distance to items^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Show Icons", &Settings.Esp.Item.Other.Icons, ("Display item icons^" + std::string(ICON_MS_IMAGE)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

        nav_elements::SliderInt("Max Distance", &Settings.Esp.Item.Other.MaxDistance, 1, 300, ("Maximum render distance^" + std::string(ICON_MS_STRAIGHTEN)).c_str(), "%d m");
        nav_elements::SliderFloat("Font Size", &Settings.Esp.Item.Other.FontSize, 10.0f, 25.0f, ("Adjust text size^" + std::string(ICON_MS_FORMAT_SIZE)).c_str(), "%.1f");
        nav_elements::SliderFloat("Icon Size", &Settings.Esp.Item.Other.IconSize, 5.0f, 40.0f, ("Adjust icon size^" + std::string(ICON_MS_ZOOM_IN)).c_str(), "%.1f");

        // Other item types
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
        RenderSectionHeader("Other Items to Display", 0, 0);

        nav_elements::CheckboxComponent("Chests", &Settings.Esp.Item.Other.Chest, ("Show chests^" + std::string(ICON_MS_INVENTORY)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Vehicles", &Settings.Esp.Item.Other.Vehicle, ("Show vehicles^" + std::string(ICON_MS_DIRECTIONS_CAR)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Llamas", &Settings.Esp.Item.Other.Llama, ("Show llamas^" + std::string(ICON_MS_PETS)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Supply Drops", &Settings.Esp.Item.Other.SupplyDrop, ("Show supply drops^" + std::string(ICON_MS_INVENTORY_2)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
}

void CGui::RenderRadarTab() {
    // Radar section
    //RenderSectionHeader("Radar", 0, 0);

    // Radar settings
        nav_elements::CheckboxComponent("Enable Radar", &Settings.Radar.RadarConfig.Enable, "Toggle radar^" ICON_MS_RADAR, true, GetRadarColorStates("Enable Radar"), &Keys.RadarEnable, &Keys.RadarEnableMode);
        nav_elements::CheckboxComponent("Show Distance", &Settings.Radar.RadarConfig.Distance, "Show distance to players^" ICON_MS_STRAIGHTEN, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

        // Radar type selection
        ImGui::Dummy(ImVec2(0, 5));
        const char* radarTypes[] = { "Circle", "Rectangle" };
        nav_elements::Combo("Radar Type", &Settings.Radar.RadarConfig.RadarType, radarTypes, IM_ARRAYSIZE(radarTypes), "Select radar type^" ICON_MS_RADAR);

        // Radar position
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Position", 0, 0);

        nav_elements::SliderInt("Position X", &Settings.Radar.RadarConfig.PositionX, 0, 1920, "Adjust radar horizontal position^" ICON_MS_TRENDING_FLAT, "%d px");
        nav_elements::SliderInt("Position Y", &Settings.Radar.RadarConfig.PositionY, 0, 1080, "Adjust radar vertical position^" ICON_MS_TRENDING_FLAT, "%d px");

        // Radar size
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Size", 0, 0);

        if (Settings.Radar.RadarConfig.RadarType == 0) { // Circle radar
            nav_elements::SliderInt("Circle Size", &Settings.Radar.RadarConfig.CirleSize, 50, 300, "Adjust radar circle size^" ICON_MS_ZOOM_IN, "%d px");
        } else { // Rectangle radar
            nav_elements::SliderInt("Rectangle Size", &Settings.Radar.RadarConfig.RectangleSize, 50, 400, "Adjust radar rectangle size^" ICON_MS_ZOOM_IN, "%d px");
    }

    // Other radar settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Settings", 0, 0);

        nav_elements::SliderInt("Max Distance", &Settings.Radar.RadarConfig.MaxDistance, 1, 100, "Maximum radar range^" ICON_MS_STRAIGHTEN, "%d m");
        nav_elements::SliderInt("Distance Font Size", &Settings.Radar.RadarConfig.DistanceFontSize, 8, 20, "Adjust distance text size^" ICON_MS_FORMAT_SIZE, "%d");

    // Player highlight options
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Player Highlight Options", 0, 0);

        nav_elements::CheckboxComponent("Highlight Visible Players", &Settings.Radar.RadarConfig.VisibleColor, "Highlight visible players^" ICON_MS_VISIBILITY, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Highlight Closest Player", &Settings.Radar.RadarConfig.ClosestColor, "Highlight closest player^" ICON_MS_PERSON, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent("Highlight Players Aiming At You", &Settings.Radar.RadarConfig.AimingAtMeColor, "Highlight players aiming at you^" ICON_MS_CROSSHAIRS, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

        // Radar colors
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Colors", 0, 0);

 /*   if (AnimateNextFeature())
        nav_elements::ColorEdit("Visible Players", Settings.Colors.PlayerColors.RadarVisible, "Visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Non-Visible Players", Settings.Colors.PlayerColors.RadarNonVisible, "Non-visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Knocked Visible Players", Settings.Colors.PlayerColors.KnockedRadarVisible, "Knocked visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Knocked Non-Visible Players", Settings.Colors.PlayerColors.KnockedRadarNonVisible, "Knocked non-visible players color^" ICON_COLOR_FILL);*/
}

void CGui::RenderSettingsTab() {
    // Settings section
    //RenderSectionHeader("Settings", 0, 0);
    // Notification Settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Notification Settings", 0, 0);

        nav_elements::CheckboxComponent("Enable Notifications", &g_NotificationSettings.Enable, "Show notifications for settings changes^" ICON_MS_NOTIFICATIONS, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

        nav_elements::SliderFloat("Notification Duration", &g_NotificationSettings.DisplayDuration, 1.0f, 20.0f, "How long notifications appear^" ICON_MS_TIMER, "%.1f s");

    // UI Settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("UI Settings", 0, 0);

    // Language selection
    const char* languages[] = { "English", "French", "German", "Russian", "Spanish", "Chinese" };
        nav_elements::Combo("Language", &Settings.Config.ConfigMenu.Language, languages, IM_ARRAYSIZE(languages), "Select language^" ICON_MS_TRANSLATE);

    // Feature definitions/tooltips
        nav_elements::CheckboxComponent("Show Feature Descriptions", &Settings.Config.ConfigMenu.FeaturesDefinition, "Show feature descriptions^" ICON_MS_INFO, false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

    // Animation settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Animation Settings", 0, 0);

        nav_elements::SliderFloat("Animation Speed", &animationSpeed, 0.01f, 0.3f, "Adjust animation speed^" ICON_MS_SPEED, "%.2f");

        // About section
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("About", 0, 0);

    ImGui::Text("BitCheats Fortnite v4.2.1");
    ImGui::Text("© 2025 BitCheats Team");
}

// Forward declare RenderConfigsTab function

void CGui::RenderConfigsTab() {
    // Match CheckboxComponent styling exactly - consistent 10px padding
    ImGui::Dummy(ImVec2(0, 10));

    //// Title section matching hotkey system UI design
    //ImGui::PushFont(iconsBig);
    //ImGui::Text(ICON_MS_FOLDER);
    //ImGui::PopFont();
    //ImGui::SameLine();
    //ImGui::Text("Configuration Management");

    //ImGui::Dummy(ImVec2(0, 8));

    //// Visual separator matching CheckboxComponent style
    //pGUi.RenderGradientSeparator(0, 0.75f);

    //// Description section with consistent spacing
    //ImGui::Dummy(ImVec2(0, 5));
    //ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Manage your game configurations - create, share, and load different settings");

    //ImGui::Dummy(ImVec2(0, 15)); // Double padding as requested

    // Search bar with CheckboxComponent styling
    static char searchBuffer[128] = "";
    ImGui::Dummy(ImVec2(0, 5));

    // Match CheckboxComponent frame padding
    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(10, 10)); // Consistent 10px padding

    // Get current window and draw list
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    ImDrawList* drawList = window->DrawList;

    // Set up search bar with CheckboxComponent width proportions
    float searchBarWidth = ImGui::GetContentRegionAvail().x * 0.35f;
    ImGui::SetNextItemWidth(searchBarWidth);

    // Get position and size for custom drawing matching CheckboxComponent
    ImVec2 pos = ImGui::GetCursorScreenPos();
    ImVec2 size = ImVec2(searchBarWidth, 42.0f); // Match CheckboxComponent height
    ImRect rect(pos, ImVec2(pos.x + size.x, pos.y + size.y));

    // Animation matching CheckboxComponent exactly
    struct SearchBarAnimation {
        float element_opacity = 0.0f;
        float line_opacity = 0.0f;
        ImColor border_color = ImColor(255, 255, 255, 10);
    };

    static std::map<ImGuiID, SearchBarAnimation> search_anim_map;
    ImGuiID id = window->GetID("##ConfigSearch");
    bool hovered = ImGui::IsMouseHoveringRect(rect.Min, rect.Max);
    bool active = ImGui::IsItemActive();

    auto it_anim = search_anim_map.find(id);
    if (it_anim == search_anim_map.end()) {
        search_anim_map.insert({id, {}});
        it_anim = search_anim_map.find(id);
    }

    // Animation speed matching CheckboxComponent
    const float anim_speed = 0.14f; // Match CheckboxComponent animation speed

    // Background animation matching CheckboxComponent
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity,
                                           (active ? 0.08f : hovered ? 0.04f : 0.02f),
                                           anim_speed * (1.0f - ImGui::GetIO().DeltaTime));

    // Line animation matching CheckboxComponent
    it_anim->second.line_opacity = ImLerp(it_anim->second.line_opacity,
                                        (active || hovered) ? 1.0f : 0.0f,
                                        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Border color animation matching CheckboxComponent
    ImVec4 current_color = ImGui::ColorConvertU32ToFloat4(it_anim->second.border_color);
    ImVec4 target_color = ImGui::ColorConvertU32ToFloat4(active || hovered ? gui.checkboxstrokeactive : gui.checkboxstroke);
    ImVec4 lerped_color = ImLerp(current_color, target_color, anim_speed * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.border_color = ImGui::ColorConvertFloat4ToU32(lerped_color);

    // Draw background matching CheckboxComponent exactly
    ImColor backgroundColor = func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f);
    drawList->AddRectFilled(rect.Min, rect.Max, backgroundColor, 8.0f); // 8-pixel rounded corners

    // Draw animated glow matching CheckboxComponent
    drawList->AddRectFilled(rect.Min, rect.Max, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 8.0f);

    // Draw border with glowing effect matching CheckboxComponent
    drawList->AddRect(rect.Min, rect.Max, it_anim->second.border_color, 8.0f, 0, 1.0f);

    // Draw bottom line with animation matching CheckboxComponent
    if (it_anim->second.line_opacity > 0.01f) {
        const float lineHeight = 2.0f;
        const float linePadding = 8.0f; // Match CheckboxComponent padding
        ImVec2 lineStart(rect.Min.x + linePadding, rect.Max.y - lineHeight - 2.0f);
        ImVec2 lineEnd(rect.Max.x - linePadding, lineStart.y + lineHeight);

        // Shadow opacity matching CheckboxComponent
        float shadowOpacity = active ? 1.0f : 0.6f;

        // Shadow for horizontal line matching CheckboxComponent
        drawList->AddShadowRect(
            lineStart,
            lineEnd,
            ImColor(accent_color[2], accent_color[1], accent_color[0], shadowOpacity * it_anim->second.line_opacity),
            15.f,
            ImVec2(0, 2),
            ImDrawFlags_RoundCornersAll,
            40.f
        );

        // Main glow line matching CheckboxComponent
        drawList->AddRectFilled(
            lineStart,
            lineEnd,
            ImColor(accent_color[2], accent_color[1], accent_color[0], it_anim->second.line_opacity),
            8.0f, // Match rounded corners
            ImDrawFlags_RoundCornersAll
        );
    }

    // Input text with transparent background
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::InputTextWithHint("##ConfigSearch", "Search by name or config ID", searchBuffer, IM_ARRAYSIZE(searchBuffer))) {
        g_ConfigSystem.searchQuery = searchBuffer;
    }

    ImGui::SameLine();

    // Search button with CheckboxComponent styling (42px height to match)
    if (nav_elements::IconButton("Search", ICON_MS_SEARCH, ImVec2(120, 42), ImColor(gui.main))) {
        // Search functionality is handled by input text already
    }

    // Position the Create New Config button with proper spacing
    float windowWidth = ImGui::GetContentRegionAvail().x;
    ImGui::SameLine(windowWidth - 180); // Consistent positioning

    // Create New Config button with robust popup handling
    if (nav_elements::IconButton("New Config", ICON_MS_ADD_CIRCLE, ImVec2(170, 42), ImColor(gui.main))) {
        // Initialize new config popup
        if (!gui.picker_active) {
            gui.picker_active = true;
            g_ConfigSystem.showSharePopup = true;
            g_ConfigSystem.shareConfigName = "";
            g_ConfigSystem.shareConfigType = "";
        }
    }

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();

    ImGui::Dummy(ImVec2(0, 10));

    // Refresh current configs
    static bool initialized = false;
    if (!initialized) {
        InitializeConfigSystem();
        initialized = true;
    }

    ImGui::Dummy(ImVec2(0, 5));

    // Config entry containers matching CheckboxComponent styling
    const float entryHeight = 75.0f; // Match CheckboxComponent proportions
    const float windowWidth2 = ImGui::GetContentRegionAvail().x;
    const float entryPadding = 10.0f; // Consistent 10px padding as requested

    // Empty state message with consistent styling
    if (g_ConfigSystem.configs.empty()) {
        ImGui::Dummy(ImVec2(0, 20)); // Double padding
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "No configurations found. Create one to get started.");
        return; // Early return to prevent further processing
    }

    // Animation timing matching CheckboxComponent
    static float animation_time = 0.0f;
    animation_time += ImGui::GetIO().DeltaTime;
    float pulse = (sinf(animation_time * 2.5f) * 0.5f + 0.5f) * 0.3f + 0.7f;

    // Display config entries
    for (int i = 0; i < g_ConfigSystem.configs.size(); i++) {
        const ConfigFile& config = g_ConfigSystem.configs[i];

        // Filter by search query if exists
        if (!g_ConfigSystem.searchQuery.empty()) {
            std::string lowerName = config.name;
            std::string lowerType = config.type;
            std::string lowerSearch = g_ConfigSystem.searchQuery;

            // Get config ID for search
            std::string configId = config.filename;
            size_t dotPos = configId.find_last_of('.');
            if (dotPos != std::string::npos) {
                configId = configId.substr(0, dotPos);
            }
            std::string lowerConfigId = configId;

            // Convert to lowercase for case-insensitive comparison
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
            std::transform(lowerType.begin(), lowerType.end(), lowerType.begin(), ::tolower);
            std::transform(lowerConfigId.begin(), lowerConfigId.end(), lowerConfigId.begin(), ::tolower);
            std::transform(lowerSearch.begin(), lowerSearch.end(), lowerSearch.begin(), ::tolower);

            if (lowerName.find(lowerSearch) == std::string::npos &&
                lowerType.find(lowerSearch) == std::string::npos &&
                lowerConfigId.find(lowerSearch) == std::string::npos) {
                continue; // Skip if not matching search
            }
        }

        // Create a unique ID for each config entry
        ImGui::PushID(i);

        // Config entry background
        ImVec2 entryPos = ImGui::GetCursorScreenPos();
        ImVec2 entrySize(windowWidth2, entryHeight);
        ImDrawList* drawList = ImGui::GetWindowDrawList();

        // Get if entry is hovered
        bool entryHovered = ImGui::IsMouseHoveringRect(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y)
        );

        // Animation structure matching CheckboxComponent exactly
        struct ConfigEntryAnimation {
            float element_opacity = 0.0f;
            float line_opacity = 0.0f;
            ImColor border_color = ImColor(255, 255, 255, 10);
        };

        static std::map<ImGuiID, ConfigEntryAnimation> config_anim_map;
        ImGuiID configId = window->GetID(("##ConfigEntry" + std::to_string(i)).c_str());

        auto config_anim = config_anim_map.find(configId);
        if (config_anim == config_anim_map.end()) {
            config_anim_map.insert({configId, {}});
            config_anim = config_anim_map.find(configId);
        }

        // Animation speed matching CheckboxComponent exactly
        const float anim_speed = 0.14f;

        // Background animation matching CheckboxComponent
        config_anim->second.element_opacity = ImLerp(config_anim->second.element_opacity,
                                                  (config.isSelected ? 0.08f : entryHovered ? 0.04f : 0.02f),
                                                  anim_speed * (1.0f - ImGui::GetIO().DeltaTime));

        // Line animation matching CheckboxComponent
        config_anim->second.line_opacity = ImLerp(config_anim->second.line_opacity,
                                               (config.isSelected || entryHovered) ? 1.0f : 0.0f,
                                               0.07f * (1.0f - ImGui::GetIO().DeltaTime));

        // Border color animation matching CheckboxComponent
        ImVec4 current_color = ImGui::ColorConvertU32ToFloat4(config_anim->second.border_color);
        ImVec4 target_color = ImGui::ColorConvertU32ToFloat4(entryHovered ? gui.checkboxstrokeactive : gui.checkboxstroke);
        ImVec4 lerped_color = ImLerp(current_color, target_color, anim_speed * (1.0f - ImGui::GetIO().DeltaTime));
        config_anim->second.border_color = ImGui::ColorConvertFloat4ToU32(lerped_color);

        // Draw background matching CheckboxComponent exactly
        ImColor backgroundColor = func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f);
        drawList->AddRectFilled(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y),
            backgroundColor,
            8.0f // 8-pixel rounded corners matching CheckboxComponent
        );

        // Draw animated glow matching CheckboxComponent
        drawList->AddRectFilled(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y),
            ImColor(1.0f, 1.0f, 1.0f, config_anim->second.element_opacity),
            8.0f
        );

        // Draw border matching CheckboxComponent
        ImColor borderColor = entryHovered ?
            gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f);
        drawList->AddRect(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y),
            borderColor,
            8.0f, 0, 1.0f
        );

        // Draw bottom line matching CheckboxComponent
        if (config_anim->second.line_opacity > 0.01f) {
            const float lineHeight = 2.0f;
            const float linePadding = 8.0f; // Match CheckboxComponent padding
            ImVec2 lineStart(entryPos.x + linePadding, entryPos.y + entrySize.y - lineHeight - 2.0f);
            ImVec2 lineEnd(entryPos.x + entrySize.x - linePadding, lineStart.y + lineHeight);

            // Shadow opacity matching CheckboxComponent
            float shadowOpacity = config.isSelected ? 1.0f : 0.6f;

            // Shadow for horizontal line matching CheckboxComponent
            drawList->AddShadowRect(
                lineStart,
                lineEnd,
                ImColor(accent_color[2], accent_color[1], accent_color[0], shadowOpacity * config_anim->second.line_opacity),
                15.f,
                ImVec2(0, 2),
                ImDrawFlags_RoundCornersAll,
                40.f
            );

            // Main glow line matching CheckboxComponent
            drawList->AddRectFilled(
                lineStart,
                lineEnd,
                ImColor(accent_color[2], accent_color[1], accent_color[0], config_anim->second.line_opacity),
                8.0f, // Match rounded corners
                ImDrawFlags_RoundCornersAll
            );
        }

        // Selected config accent line matching CheckboxComponent
        if (config.isSelected) {
            const float lineHeight = 2.0f;
            const float linePadding = 8.0f; // Match CheckboxComponent padding
            ImVec2 lineStart(entryPos.x + linePadding, entryPos.y + entrySize.y - lineHeight - 2.0f);
            ImVec2 lineEnd(entryPos.x + entrySize.x - linePadding, lineStart.y + lineHeight);

            // Blue glow effect matching CheckboxComponent
            ImColor lineColor(accent_color[2], accent_color[1], accent_color[0], 0.8f * pulse);

            // Shadow for horizontal line matching CheckboxComponent
            drawList->AddShadowRect(
                lineStart,
                lineEnd,
                lineColor,
                15.f,
                ImVec2(0, 2),
                ImDrawFlags_RoundCornersAll,
                40.f
            );

            // Main glow line matching CheckboxComponent
            drawList->AddRectFilled(
                lineStart,
                lineEnd,
                lineColor,
                8.0f, // Match rounded corners
                ImDrawFlags_RoundCornersAll
            );
        }

        // Responsive 3-column button grid layout
        float iconButtonSize = 36.0f;    // Square icon-only buttons
        float buttonHeight = 36.0f;
        float padding = 8.0f;             // Spacing between buttons
        float buttonY = entryPos.y + (entryHeight - buttonHeight) * 0.5f;

        // Layout with consistent 10px spacing matching CheckboxComponent
        float col1_x = entryPos.x + 15;   // Name column
        float col2_x = entryPos.x + 155;  // Type column
        float col3_x = entryPos.x + 235;  // ID column
        float col4_x = entryPos.x + 315;  // Creator column
        float col5_x = entryPos.x + 435;  // Modified At column

        // Column widths with proper spacing
        float col1_width = 135;  // Name column width
        float col2_width = 75;   // Type column width
        float col3_width = 75;   // ID column width
        float col4_width = 115;  // Creator column width
        float col5_width = 90;   // Modified At column width

        // Responsive button area - 3 equal columns
        float buttonAreaStart = col5_x + col5_width + 15; // Start after text columns
        float availableButtonWidth = entrySize.x - (buttonAreaStart - entryPos.x) - 15; // Available width for buttons
        float buttonColumnWidth = (availableButtonWidth - padding * 2) / 3.0f; // 3 equal columns with padding

        // Base Y positions for consistent alignment
        float baseY = entryPos.y + 12;
        float secondLineY = entryPos.y + 32;

        // === COLUMN 1: CONFIG NAME ===
        // Show label at top
        ImGui::SetCursorScreenPos(ImVec2(col1_x, baseY));
        ImGui::PushTextWrapPos(col1_x + col1_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Config Name:");
        ImGui::PopTextWrapPos();

        // Show config name at bottom with truncation (always visible, not just when selected)
        ImGui::SetCursorScreenPos(ImVec2(col1_x, secondLineY));
        ImGui::PushTextWrapPos(col1_x + col1_width); // Prevent text overflow

        // Truncate config name if longer than 12 characters
        std::string displayName = config.name;
        if (displayName.length() > 12) {
            displayName = displayName.substr(0, 12) + "...";
        }

        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "%s", displayName.c_str());
        ImGui::PopTextWrapPos();

        // === COLUMN 2: CONFIG TYPE ===
        ImGui::SetCursorScreenPos(ImVec2(col2_x, baseY));
        ImGui::PushTextWrapPos(col2_x + col2_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Type:");
        ImGui::PopTextWrapPos();
        ImGui::SetCursorScreenPos(ImVec2(col2_x, secondLineY));
        ImGui::PushTextWrapPos(col2_x + col2_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "%s", config.type.c_str());
        ImGui::PopTextWrapPos();

        // === COLUMN 3: CONFIG ID ===
        // Extract ID from filename (6-digit random ID for config identification)
        std::string configIdStr = config.filename;
        size_t dotPos = configIdStr.find_last_of('.');
        if (dotPos != std::string::npos && configIdStr.substr(dotPos) == ".json") {
            configIdStr = configIdStr.substr(0, dotPos);
        }

        // Truncate ID if longer than 12 characters (though typically 6-digit numbers)
        if (configIdStr.length() > 12) {
            configIdStr = configIdStr.substr(0, 12) + "...";
        }

        ImGui::SetCursorScreenPos(ImVec2(col3_x, baseY));
        ImGui::PushTextWrapPos(col3_x + col3_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "ID:");
        ImGui::PopTextWrapPos();
        ImGui::SetCursorScreenPos(ImVec2(col3_x, secondLineY));
        ImGui::PushTextWrapPos(col3_x + col3_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "%s", configIdStr.c_str());
        ImGui::PopTextWrapPos();

        // === COLUMN 4: CREATOR ===
        const char* creatorName = config.isDefault ? "BitCheats" : "Creator";
        ImGui::SetCursorScreenPos(ImVec2(col4_x, baseY));
        ImGui::PushTextWrapPos(col4_x + col4_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Creator:");
        ImGui::PopTextWrapPos();
        ImGui::SetCursorScreenPos(ImVec2(col4_x, secondLineY));
        ImGui::PushTextWrapPos(col4_x + col4_width); // Prevent text overflow

        // Truncate creator name if longer than 12 characters
        std::string displayCreator = creatorName;
        if (displayCreator.length() > 12) {
            displayCreator = displayCreator.substr(0, 12) + "...";
        }

        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "@%s", displayCreator.c_str());
        ImGui::PopTextWrapPos();

        // === COLUMN 5: MODIFIED DATE ===
        char timeBuffer[64];
        std::time_t time = std::chrono::system_clock::to_time_t(config.lastModified);
        std::tm tm;
        localtime_s(&tm, &time);
        std::strftime(timeBuffer, sizeof(timeBuffer), "%m/%d %H:%M", &tm);

        ImGui::SetCursorScreenPos(ImVec2(col5_x, baseY));
        ImGui::PushTextWrapPos(col5_x + col5_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Modified:");
        ImGui::PopTextWrapPos();
        ImGui::SetCursorScreenPos(ImVec2(col5_x, secondLineY));
        ImGui::PushTextWrapPos(col5_x + col5_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "%s", timeBuffer);
        ImGui::PopTextWrapPos();



        // Add clickable region for the left part of the entry only - not overlapping with buttons
        float clickableWidth = buttonAreaStart - entryPos.x - 10; // Up to button area start with margin

        // Save current cursor position
        ImVec2 originalCursorPos = ImGui::GetCursorScreenPos();

        // Set cursor to the beginning of the entry
        ImGui::SetCursorScreenPos(entryPos);

        // Create invisible button that only covers the left part of the entry
        ImGui::InvisibleButton("##ConfigEntryBackground", ImVec2(clickableWidth, entrySize.y));

        // Check if the entry is clicked to select the config
        if (ImGui::IsItemClicked()) {
            // Select this config
            SelectConfiguration(i);
        }

        // Restore cursor position for the buttons
        ImGui::SetCursorScreenPos(originalCursorPos);

        // Responsive 3-column button grid
        // Column 1: Delete button (leftmost)
        ImVec2 deleteButtonPos = ImVec2(buttonAreaStart, buttonY);
        ImGui::SetCursorScreenPos(deleteButtonPos);

        // Delete button with same design as share button but crimson hover
        static int configToDelete = -1;
        static bool showDeleteConfirmation = false;

        // Custom delete button with crimson hover (matching share button design)
        ImGui::SetCursorScreenPos(deleteButtonPos);
        ImGui::PushID(("delete_" + std::to_string(i)).c_str());

        // Create invisible button for interaction
        bool deletePressed = ImGui::InvisibleButton("##delete", ImVec2(buttonColumnWidth, buttonHeight));
        bool deleteHovered = ImGui::IsItemHovered();

        // Get button rect
        ImRect deleteRect = ImRect(deleteButtonPos, ImVec2(deleteButtonPos.x + buttonColumnWidth, deleteButtonPos.y + buttonHeight));

        // Animation state for delete button (matching IconOnlyButton design)
        static std::map<int, float> deleteElementOpacity;
        static std::map<int, ImVec4> deleteIconColor;

        if (deleteElementOpacity.find(i) == deleteElementOpacity.end()) {
            deleteElementOpacity[i] = 0.0f;
            deleteIconColor[i] = ImVec4(1.0f, 1.0f, 1.0f, 0.7f);
        }

        // Fast animation system for immediate response
        deleteElementOpacity[i] = ImLerp(deleteElementOpacity[i],
            deleteHovered ? 0.01f : 0.0f,
            0.5f); // Much faster animation

        // Immediate color change for crimson hover (no animation)
        ImVec4 targetIconColor = deleteHovered ?
            ImVec4(220.0f/255.0f, 20.0f/255.0f, 60.0f/255.0f, 1.0f) : // Crimson on hover
            ImVec4(1.0f, 1.0f, 1.0f, 0.7f); // Default color

        deleteIconColor[i] = targetIconColor; // Immediate color change, no animation

        // Draw background exactly like IconOnlyButton
        drawList->AddRectFilled(deleteRect.Min, deleteRect.Max,
            ImColor(1.0f, 1.0f, 1.0f, deleteElementOpacity[i]), 3.0f);

        // Draw border exactly like IconOnlyButton
        ImVec4 border_color = deleteHovered ?
            ImVec4(ImColor(255, 255, 255, 10)) :
            ImVec4(ImColor(255, 255, 255, 10));
        drawList->AddRect(deleteRect.Min, deleteRect.Max,
            ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);

        // Draw icon shadow exactly like IconOnlyButton
        ImVec2 deleteIconCenter = deleteRect.GetCenter();
        drawList->AddShadowCircle(
            deleteIconCenter,
            9.f,
            ImGui::ColorConvertFloat4ToU32(deleteIconColor[i]),
            40,
            ImVec2(0, 0),
            0,
            360
        );

        // Draw delete icon centered
        ImGui::PushFont(iconsBig);
        ImVec2 deleteIconSize = ImGui::CalcTextSize(ICON_MS_DELETE);
        const float delete_icon_scale = 0.8f; // Same scale as IconOnlyButton
        ImVec2 deleteIconPos = ImVec2(
            deleteIconCenter.x - (deleteIconSize.x * delete_icon_scale) * 0.5f,
            deleteIconCenter.y - (deleteIconSize.y * delete_icon_scale) * 0.5f
        );

        ImGui::SetWindowFontScale(delete_icon_scale);
        drawList->AddText(deleteIconPos, ImGui::ColorConvertFloat4ToU32(deleteIconColor[i]), ICON_MS_DELETE);
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopFont();

        ImGui::PopID();

        if (deletePressed && !config.isDefault) {
            configToDelete = i;
            showDeleteConfirmation = true;
        }

        // Column 2: Share button (middle) - with fast animation
        ImVec2 shareButtonPos = ImVec2(buttonAreaStart + buttonColumnWidth + padding, buttonY);
        ImGui::SetCursorScreenPos(shareButtonPos);
        ImGui::PushID(("share_" + std::to_string(i)).c_str());

        bool sharePressed = ImGui::InvisibleButton("##share", ImVec2(buttonColumnWidth, buttonHeight));
        bool shareHovered = ImGui::IsItemHovered();

        // Fast animation for share button
        static std::map<int, float> shareElementOpacity;
        static std::map<int, ImVec4> shareIconColor;

        if (shareElementOpacity.find(i) == shareElementOpacity.end()) {
            shareElementOpacity[i] = 0.0f;
            shareIconColor[i] = ImVec4(1.0f, 1.0f, 1.0f, 0.7f);
        }

        shareElementOpacity[i] = ImLerp(shareElementOpacity[i], shareHovered ? 0.01f : 0.0f, 0.5f);
        shareIconColor[i] = shareHovered ?
            ImVec4(0.9f, 0.2f, 0.2f, 1.0f) : // Red on hover
            ImVec4(1.0f, 1.0f, 1.0f, 0.7f); // Default color (immediate change)

        // Draw share button
        ImRect shareRect = ImRect(shareButtonPos, ImVec2(shareButtonPos.x + buttonColumnWidth, shareButtonPos.y + buttonHeight));
        drawList->AddRectFilled(shareRect.Min, shareRect.Max, ImColor(1.0f, 1.0f, 1.0f, shareElementOpacity[i]), 3.0f);
        drawList->AddRect(shareRect.Min, shareRect.Max, ImColor(255, 255, 255, 10), 3.0f, 0, 1.0f);

        ImVec2 shareIconCenter = shareRect.GetCenter();
        drawList->AddShadowCircle(shareIconCenter, 9.f, ImGui::ColorConvertFloat4ToU32(shareIconColor[i]), 40, ImVec2(0, 0), 0, 360);

        ImGui::PushFont(iconsBig);
        ImVec2 shareIconSize = ImGui::CalcTextSize(ICON_MS_SHARE);
        const float share_icon_scale = 0.8f;
        ImVec2 shareIconPos = ImVec2(
            shareIconCenter.x - (shareIconSize.x * share_icon_scale) * 0.5f,
            shareIconCenter.y - (shareIconSize.y * share_icon_scale) * 0.5f
        );
        ImGui::SetWindowFontScale(share_icon_scale);
        drawList->AddText(shareIconPos, ImGui::ColorConvertFloat4ToU32(shareIconColor[i]), ICON_MS_SHARE);
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopFont();
        ImGui::PopID();

        if (sharePressed) {
            g_ConfigSystem.showSharePopup = true;
            g_ConfigSystem.shareConfigName = config.name + "_Shared";
            g_ConfigSystem.shareConfigType = config.type;
        }

        // Column 3: Download/Load/Selected button (rightmost) - with fast animation
        ImVec2 actionButtonPos = ImVec2(buttonAreaStart + (buttonColumnWidth + padding) * 2, buttonY);
        ImGui::SetCursorScreenPos(actionButtonPos);
        ImGui::PushID(("action_" + std::to_string(i)).c_str());

        bool isCurrentlyLoaded = config.isSelected;
        const char* actionIcon;
        ImVec4 actionColor;

        if (isCurrentlyLoaded) {
            actionIcon = ICON_MS_CHECK_CIRCLE;
            actionColor = ImVec4(0.2f, 0.8f, 0.2f, 1.0f); // Green for selected
        } else if (config.isDownloaded) {
            actionIcon = ICON_MS_PLAY_ARROW;
            actionColor = ImVec4(0.2f, 0.8f, 0.2f, 1.0f); // Green for load
        } else {
            actionIcon = ICON_MS_DOWNLOAD;
            actionColor = ImVec4(0.2f, 0.6f, 1.0f, 1.0f); // Blue for download
        }

        bool actionPressed = ImGui::InvisibleButton("##action", ImVec2(buttonColumnWidth, buttonHeight));
        bool actionHovered = ImGui::IsItemHovered();

        // Fast animation for action button
        static std::map<int, float> actionElementOpacity;
        static std::map<int, ImVec4> actionIconColor;

        if (actionElementOpacity.find(i) == actionElementOpacity.end()) {
            actionElementOpacity[i] = 0.0f;
            actionIconColor[i] = ImVec4(1.0f, 1.0f, 1.0f, 0.7f);
        }

        actionElementOpacity[i] = ImLerp(actionElementOpacity[i], actionHovered ? 0.01f : 0.0f, 0.5f);
        actionIconColor[i] = actionHovered ? actionColor : ImVec4(1.0f, 1.0f, 1.0f, 0.7f); // Immediate change

        // Draw action button
        ImRect actionRect = ImRect(actionButtonPos, ImVec2(actionButtonPos.x + buttonColumnWidth, actionButtonPos.y + buttonHeight));
        drawList->AddRectFilled(actionRect.Min, actionRect.Max, ImColor(1.0f, 1.0f, 1.0f, actionElementOpacity[i]), 3.0f);
        drawList->AddRect(actionRect.Min, actionRect.Max, ImColor(255, 255, 255, 10), 3.0f, 0, 1.0f);

        ImVec2 actionIconCenter = actionRect.GetCenter();
        drawList->AddShadowCircle(actionIconCenter, 9.f, ImGui::ColorConvertFloat4ToU32(actionIconColor[i]), 40, ImVec2(0, 0), 0, 360);

        ImGui::PushFont(iconsBig);
        ImVec2 actionIconSize = ImGui::CalcTextSize(actionIcon);
        const float action_icon_scale = 0.8f;
        ImVec2 actionIconPos = ImVec2(
            actionIconCenter.x - (actionIconSize.x * action_icon_scale) * 0.5f,
            actionIconCenter.y - (actionIconSize.y * action_icon_scale) * 0.5f
        );
        ImGui::SetWindowFontScale(action_icon_scale);
        drawList->AddText(actionIconPos, ImGui::ColorConvertFloat4ToU32(actionIconColor[i]), actionIcon);
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopFont();
        ImGui::PopID();

        if (actionPressed && !isCurrentlyLoaded) {
            if (config.isDownloaded) {
                SelectConfiguration(i);
            } else {
                ConfigFile& nonConstConfig = g_ConfigSystem.configs[i];
                nonConstConfig.isDownloaded = true;
                g_NotificationManager.AddChange("Configurations", "Config Downloaded", false, true);
            }
        }

        // Delete confirmation popup
        if (showDeleteConfirmation && configToDelete == i) {
            ImGui::OpenPopup("##DeleteConfirmation");
        }

        // Center the delete confirmation popup
        ImVec2 center(gui.window_pos.x + gui.window_size.x * 0.5f, gui.window_pos.y + gui.window_size.y * 0.5f);
        ImGui::SetNextWindowPos(center, ImGuiCond_Always, ImVec2(0.5f, 0.5f));
        ImGui::SetNextWindowSize(ImVec2(0, 0), ImGuiCond_Always);

        // Removed full-screen overlay for cleaner UI

        if (ImGui::BeginPopup("##DeleteConfirmation", ImGuiWindowFlags_NoMove | ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoScrollbar)) {
            // Styling matching other popups
            ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
            ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(20, 20));
            ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
            ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(10, 10));

            // Warning header
            ImGui::PushFont(iconsBig);
            ImGui::TextColored(ImVec4(1.0f, 0.3f, 0.3f, 1.0f), ICON_MS_WARNING);
            ImGui::PopFont();
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(1.0f, 0.3f, 0.3f, 1.0f), "Delete Configuration");

            ImGui::Separator();
            ImGui::Spacing();

            // Warning message
            ImGui::Text("Are you sure you want to delete this configuration?");
            ImGui::Spacing();
            ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "Config: %s", g_ConfigSystem.configs[configToDelete].name.c_str());
            ImGui::TextColored(ImVec4(1.0f, 0.6f, 0.6f, 1.0f), "This action cannot be undone!");

            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Spacing();

            // Buttons
            float buttonWidth = 100.0f;
            float spacing = 20.0f;
            float totalWidth = buttonWidth * 2 + spacing;
            float buttonStartX = (ImGui::GetContentRegionAvail().x - totalWidth) * 0.5f;
            ImGui::SetCursorPosX(buttonStartX);

            // Delete button (crimson)
            if (nav_elements::IconButton("Delete", ICON_MS_DELETE, ImVec2(buttonWidth, 40), ImColor(220, 20, 60))) {
                DeleteConfiguration(g_ConfigSystem.configs[configToDelete].filename);
                showDeleteConfirmation = false;
                configToDelete = -1;
                ImGui::CloseCurrentPopup();
            }

            ImGui::SameLine();

            // Cancel button
            if (nav_elements::IconButton("Cancel", ICON_MS_CANCEL, ImVec2(buttonWidth, 40), ImColor(0.6f, 0.6f, 0.6f))) {
                showDeleteConfirmation = false;
                configToDelete = -1;
                ImGui::CloseCurrentPopup();
            }

            ImGui::PopStyleVar(3);
            ImGui::PopStyleColor();
            ImGui::EndPopup();
        } else if (showDeleteConfirmation && configToDelete == i) {
            // Reset if popup was closed externally
            showDeleteConfirmation = false;
            configToDelete = -1;
        }

        // Enhanced visual separator between config entries
        if (i < g_ConfigSystem.configs.size() - 1) { // Don't add separator after last entry
            ImVec2 separatorStart = ImVec2(entryPos.x + 15, entryPos.y + entryHeight + entryPadding);
            ImVec2 separatorEnd = ImVec2(entryPos.x + entrySize.x - 15, separatorStart.y + 1);

            // Draw gradient separator line for better visual separation
            drawList->AddRectFilledMultiColor(
                separatorStart,
                separatorEnd,
                ImColor(0.0f, 0.0f, 0.0f, 0.0f),     // Transparent left
                ImColor(0.4f, 0.4f, 0.4f, 0.3f),     // Visible center
                ImColor(0.4f, 0.4f, 0.4f, 0.3f),     // Visible center
                ImColor(0.0f, 0.0f, 0.0f, 0.0f)      // Transparent right
            );
        }

        // Move cursor for next entry with double padding
        ImGui::SetCursorScreenPos(ImVec2(entryPos.x, entryPos.y + entryHeight + entryPadding * 2));

        ImGui::PopID();
    }

    // Robust Config Popup System - Complete Rewrite
    CGui::RenderConfigPopup();



}






// Modern Config Popup System - Redesigned UI
void CGui::RenderConfigPopup() {
    // Enhanced popup state management
    struct ConfigPopupState {
        bool isInitialized = false;
        bool isOperationInProgress = false;
        char nameBuffer[128] = "";
        char typeBuffer[64] = "";
        bool hasError = false;
        std::string errorMessage = "";
        bool shouldClose = false;
        float lastInteractionTime = 0.0f;
        bool isShareMode = false; // Determines if this is create or share mode
    };

    static ConfigPopupState popupState;

    // Only proceed if popup should be shown
    if (!g_ConfigSystem.showSharePopup) {
        // Reset state when popup is not active
        if (popupState.isInitialized) {
            popupState = ConfigPopupState(); // Reset to default state
        }
        return;
    }

    // Initialize popup state on first show
    if (!popupState.isInitialized) {
        // Determine if this is share mode (pre-filled data) or create mode (empty)
        popupState.isShareMode = !g_ConfigSystem.shareConfigName.empty() &&
                                 g_ConfigSystem.shareConfigName.find("_Shared") != std::string::npos;

        // Pre-fill from system state
        if (!g_ConfigSystem.shareConfigName.empty()) {
            strncpy_s(popupState.nameBuffer, g_ConfigSystem.shareConfigName.c_str(), sizeof(popupState.nameBuffer) - 1);
            popupState.nameBuffer[sizeof(popupState.nameBuffer) - 1] = '\0';
        }
        if (!g_ConfigSystem.shareConfigType.empty()) {
            strncpy_s(popupState.typeBuffer, g_ConfigSystem.shareConfigType.c_str(), sizeof(popupState.typeBuffer) - 1);
            popupState.typeBuffer[sizeof(popupState.typeBuffer) - 1] = '\0';
        }

        popupState.isInitialized = true;
        popupState.hasError = false;
        popupState.errorMessage = "";
        popupState.lastInteractionTime = ImGui::GetTime();

        // Open the popup with unique ID
        ImGui::OpenPopup("##ModernConfigPopup");
    }

    // Center popup within main UI area
    ImVec2 center(gui.window_pos.x + gui.window_size.x * 0.5f, gui.window_pos.y + gui.window_size.y * 0.5f);
    ImGui::SetNextWindowPos(center, ImGuiCond_Always, ImVec2(0.5f, 0.5f));
    ImGui::SetNextWindowSize(ImVec2(0, 0), ImGuiCond_Always); // Auto-size

    // Removed full-screen overlay for cleaner UI

    // Begin popup with modern design
    if (ImGui::BeginPopup("##ModernConfigPopup", ImGuiWindowFlags_NoMove | ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoScrollbar)) {
        // Styling matching CheckboxComponent and hotkey system
        ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(20, 20));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f); // 8-pixel rounded corners
        ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(10, 10)); // Consistent 10px spacing

        // Update interaction time when user interacts with popup
        if (ImGui::IsWindowHovered() || ImGui::IsAnyItemActive() || ImGui::IsAnyItemFocused()) {
            popupState.lastInteractionTime = ImGui::GetTime();
        }

        // Handle escape key (only close if not actively typing)
        if (ImGui::IsKeyPressed(ImGuiKey_Escape) && !ImGui::IsAnyItemActive()) {
            popupState.shouldClose = true;
        }

        // Handle clicking outside (only if not interacting with popup content)
        bool clicked_outside = ImGui::IsMouseClicked(ImGuiMouseButton_Left) &&
                              !ImGui::IsWindowHovered(ImGuiHoveredFlags_AnyWindow | ImGuiHoveredFlags_AllowWhenBlockedByPopup) &&
                              !ImGui::IsAnyItemHovered() && !ImGui::IsAnyItemActive();

        // Only close on outside click if user hasn't interacted recently (prevents accidental closure)
        if (clicked_outside && (ImGui::GetTime() - popupState.lastInteractionTime) > 0.5f) {
            popupState.shouldClose = true;
        }

        // Modern header with title and close button
        const char* title = popupState.isShareMode ? "Share Configuration" : "Create Configuration";
        const char* icon = popupState.isShareMode ? ICON_MS_SHARE : ICON_MS_ADD_CIRCLE;

        // Calculate header layout
        float headerHeight = 40.0f;
        float closeButtonSize = 24.0f;
        ImVec2 headerStart = ImGui::GetCursorPos();

        // Title with icon - left aligned with padding
        ImGui::SetCursorPos(ImVec2(headerStart.x + 10, headerStart.y + 8));
        ImGui::PushFont(iconsBig);
        ImGui::Text(icon);
        ImGui::PopFont();
        ImGui::SameLine();
        ImGui::Text(title);

        // Close button - top-right corner
        ImGui::SetCursorPos(ImVec2(headerStart.x + ImGui::GetContentRegionAvail().x - closeButtonSize - 10, headerStart.y + 8));

        // Custom close button with hover effect
        ImVec2 closeButtonPos = ImGui::GetCursorScreenPos();
        ImRect closeButtonRect(closeButtonPos, ImVec2(closeButtonPos.x + closeButtonSize, closeButtonPos.y + closeButtonSize));

        bool closeButtonHovered = ImGui::IsMouseHoveringRect(closeButtonRect.Min, closeButtonRect.Max);
        bool closeButtonClicked = closeButtonHovered && ImGui::IsMouseClicked(ImGuiMouseButton_Left);

        // Draw close button background
        ImU32 closeButtonColor = closeButtonHovered ? ImColor(255, 60, 60, 180) : ImColor(120, 120, 120, 100);
        ImGui::GetWindowDrawList()->AddRectFilled(
            closeButtonRect.Min, closeButtonRect.Max,
            closeButtonColor, 4.0f
        );

        // Draw X icon
        ImGui::PushFont(iconsBig);
        ImVec2 closeIconSize = ImGui::CalcTextSize(ICON_MS_CLOSE);
        ImVec2 iconPos = ImVec2(
            closeButtonRect.Min.x + (closeButtonSize - closeIconSize.x) * 0.5f,
            closeButtonRect.Min.y + (closeButtonSize - closeIconSize.y) * 0.5f
        );
        ImGui::GetWindowDrawList()->AddText(iconPos, ImColor(255, 255, 255, 255), ICON_MS_CLOSE);
        ImGui::PopFont();

        if (closeButtonClicked) {
            popupState.shouldClose = true;
        }

        // Move cursor past header
        ImGui::SetCursorPos(ImVec2(headerStart.x, headerStart.y + headerHeight));
        ImGui::Separator();
        ImGui::Spacing();

        // Show error message if any
        if (popupState.hasError) {
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.3f, 1.0f));
            ImGui::Text("Error: %s", popupState.errorMessage.c_str());
            ImGui::PopStyleColor();
            ImGui::Spacing();
        }

        // Modern input fields with identical styling
        const float inputWidth = 400.0f; // Fixed width for consistency
        const float inputHeight = 45.0f; // Slightly taller for modern look
        const float labelSpacing = 8.0f;
        const float fieldSpacing = 20.0f; // Space between fields

        // Helper function to render modern input field
        auto RenderModernInputField = [&](const char* label, const char* placeholder, char* buffer, size_t bufferSize, bool& changed) {
            // Label
            ImGui::Text(label);
            ImGui::Dummy(ImVec2(0, labelSpacing));

            // Center the input field
            float inputStartX = (ImGui::GetContentRegionAvail().x - inputWidth) * 0.5f;
            ImGui::SetCursorPosX(inputStartX);

            // Input field background and styling
            ImVec2 inputPos = ImGui::GetCursorScreenPos();
            ImVec2 inputSize = ImVec2(inputWidth, inputHeight);
            ImRect inputRect(inputPos, ImVec2(inputPos.x + inputSize.x, inputPos.y + inputSize.y));

            // Background matching current menu design
            ImGui::GetWindowDrawList()->AddRectFilled(
                inputRect.Min, inputRect.Max,
                func.GetColorWithAlpha(gui.background, 0.8f), // Match menu background
                8.0f
            );

            // Border matching current menu design
            bool isActive = ImGui::IsItemActive();
            bool isHovered = ImGui::IsItemHovered();
            ImU32 borderColor;

            if (isActive) {
                borderColor = gui.main; // Use accent color when active
            } else if (isHovered) {
                borderColor = func.GetColorWithAlpha(gui.main, 0.6f); // Dimmed accent on hover
            } else {
                borderColor = gui.stroke; // Default stroke color
            }

            ImGui::GetWindowDrawList()->AddRect(
                inputRect.Min, inputRect.Max,
                borderColor, 8.0f, 0, isActive ? 2.0f : 1.0f
            );

            // Input field styling matching current menu design
            ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f)); // Transparent (we draw custom background)
            ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
            ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
            ImGui::PushStyleColor(ImGuiCol_Text, func.ImColorToImVec4(gui.text[0])); // Use menu text color
            ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(15, 12)); // Consistent padding
            ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 8.0f); // Match menu rounding

            ImGui::SetNextItemWidth(inputSize.x);
            changed = ImGui::InputTextWithHint(("##" + std::string(label)).c_str(), placeholder, buffer, bufferSize);

            ImGui::PopStyleVar(2);
            ImGui::PopStyleColor(4); // Updated to match the 4 style colors we pushed

            ImGui::Dummy(ImVec2(0, fieldSpacing));
        };

        // Configuration Name Field
        bool nameChanged = false;
        RenderModernInputField("Configuration Name", "Enter a name...",
                              popupState.nameBuffer, IM_ARRAYSIZE(popupState.nameBuffer), nameChanged);

        // Configuration Type Field
        bool typeChanged = false;
        RenderModernInputField("Configuration Type", "e.g., Rage, Legit, Balanced...",
                              popupState.typeBuffer, IM_ARRAYSIZE(popupState.typeBuffer), typeChanged);

        // Validate input in real-time
        if (nameChanged || typeChanged) {
            popupState.hasError = false;
            popupState.errorMessage = "";
            popupState.lastInteractionTime = ImGui::GetTime();

            if (strlen(popupState.nameBuffer) == 0) {
                popupState.hasError = true;
                popupState.errorMessage = "Configuration name cannot be empty";
            } else {
                std::string name = popupState.nameBuffer;
                if (name.find_first_of("<>:\"/\\|?*") != std::string::npos) {
                    popupState.hasError = true;
                    popupState.errorMessage = "Configuration name contains invalid characters";
                }
            }
        }

        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        // Modern action button - single centered button
        const char* buttonText = popupState.isShareMode ? "Share Config" : "Save Config";
        const char* buttonIcon = popupState.isShareMode ? ICON_MS_SHARE : ICON_MS_SAVE;
        const float buttonWidth = 180.0f;
        const float buttonHeight = 50.0f;

        // Center the button
        float buttonStartX = (ImGui::GetContentRegionAvail().x - buttonWidth) * 0.5f;
        ImGui::SetCursorPosX(buttonStartX);

        bool canCreate = !popupState.hasError && strlen(popupState.nameBuffer) > 0 && !popupState.isOperationInProgress;

        // Custom modern button with perfect centering
        ImVec2 buttonPos = ImGui::GetCursorScreenPos();
        ImRect buttonRect(buttonPos, ImVec2(buttonPos.x + buttonWidth, buttonPos.y + buttonHeight));

        bool buttonHovered = ImGui::IsMouseHoveringRect(buttonRect.Min, buttonRect.Max) && canCreate;
        bool buttonClicked = buttonHovered && ImGui::IsMouseClicked(ImGuiMouseButton_Left);

        // Button background with modern gradient
        ImU32 buttonColor = canCreate ?
            (buttonHovered ? ImColor(80, 150, 255, 255) : ImColor(70, 130, 255, 255)) :
            ImColor(60, 60, 70, 255);

        ImGui::GetWindowDrawList()->AddRectFilled(
            buttonRect.Min, buttonRect.Max,
            buttonColor, 8.0f
        );

        // Button border
        ImGui::GetWindowDrawList()->AddRect(
            buttonRect.Min, buttonRect.Max,
            ImColor(90, 160, 255, 255), 8.0f, 0, 1.0f
        );

        // Calculate perfect centering for icon and text
        ImGui::PushFont(iconsBig);
        ImVec2 buttonIconSize = ImGui::CalcTextSize(buttonIcon);
        ImGui::PopFont();
        ImVec2 textSize = ImGui::CalcTextSize(buttonText);

        float totalContentWidth = buttonIconSize.x + 8.0f + textSize.x; // Icon + spacing + text
        float contentStartX = buttonRect.Min.x + (buttonWidth - totalContentWidth) * 0.5f;
        float contentY = buttonRect.Min.y + (buttonHeight - ImMax(buttonIconSize.y, textSize.y)) * 0.5f;

        // Draw icon
        ImGui::PushFont(iconsBig);
        ImGui::GetWindowDrawList()->AddText(
            ImVec2(contentStartX, contentY),
            canCreate ? ImColor(255, 255, 255, 255) : ImColor(150, 150, 150, 255),
            buttonIcon
        );
        ImGui::PopFont();

        // Draw text
        ImGui::GetWindowDrawList()->AddText(
            ImVec2(contentStartX + buttonIconSize.x + 8.0f, contentY),
            canCreate ? ImColor(255, 255, 255, 255) : ImColor(150, 150, 150, 255),
            buttonText
        );

        // Handle button click
        if (buttonClicked && canCreate) {
            popupState.isOperationInProgress = true;
            popupState.lastInteractionTime = ImGui::GetTime();

            try {
                g_ConfigSystem.shareConfigName = popupState.nameBuffer;
                g_ConfigSystem.shareConfigType = strlen(popupState.typeBuffer) > 0 ? popupState.typeBuffer : "Custom";

                bool success = false;
                if (popupState.isShareMode) {
                    success = ShareConfiguration(g_ConfigSystem.shareConfigName, g_ConfigSystem.shareConfigType);
                    if (success) {
                        g_NotificationManager.AddChange("Configurations", "Config Shared", false, true);
                    }
                } else {
                    success = CreateConfiguration(g_ConfigSystem.shareConfigName, g_ConfigSystem.shareConfigType);
                    if (success) {
                        g_NotificationManager.AddChange("Configurations", "Config Created", false, true);
                    }
                }

                if (success) {
                    popupState.shouldClose = true;
                } else {
                    popupState.hasError = true;
                    bool isDuplicate = false;
                    for (const auto& config : g_ConfigSystem.configs) {
                        if (config.name == g_ConfigSystem.shareConfigName) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    popupState.errorMessage = isDuplicate ?
                        "Configuration with this name already exists" :
                        (popupState.isShareMode ? "Failed to share configuration" : "Failed to create configuration file");
                }
            } catch (...) {
                popupState.hasError = true;
                popupState.errorMessage = popupState.isShareMode ?
                    "Unexpected error occurred while sharing configuration" :
                    "Unexpected error occurred while creating configuration";
            }

            popupState.isOperationInProgress = false;
        }

        // Move cursor past button
        ImGui::SetCursorScreenPos(ImVec2(buttonPos.x, buttonPos.y + buttonHeight + 10));

        // Handle popup closure
        if (popupState.shouldClose) {
            gui.picker_active = false;
            g_ConfigSystem.showSharePopup = false;
            popupState = ConfigPopupState(); // Reset state
            ImGui::CloseCurrentPopup();
        }

        ImGui::PopStyleVar(3);
        ImGui::PopStyleColor();
        ImGui::EndPopup();
    } else {
        // Popup was closed externally - reset state
        if (popupState.isInitialized) {
            gui.picker_active = false;
            g_ConfigSystem.showSharePopup = false;
            popupState = ConfigPopupState();
        }
    }
}

void CGui::RenderProfileWindow() {
    // Always visible - completely isolated from menu animations
    const float profileAlpha = 1.0f; // Always fully visible regardless of menu state
    
    // Store current ImGui context state to isolate from animations
    ImGuiContext* ctx = ImGui::GetCurrentContext();
    float originalGlobalAlpha = ctx->Style.Alpha;
    
    // Save current style stack depth to detect any leaked style changes
    int originalStyleVarStackSize = ctx->StyleVarStack.Size;

    // Constants for styling - matching CheckboxComponent exactly
    const float component_height = 60.0f; // Same as CheckboxComponent
    const float brand_width = 80.0f;      // Width for brand image area
    const float content_padding = 15.0f;  // Internal padding
    const float element_spacing = 10.0f;  // Space between elements
    const float corner_radius = 3.0f;     // Same as CheckboxComponent

    // Calculate content dimensions
    const float username_width = 120.0f;
    const float expiry_width = 100.0f;
    const float fps_width = 80.0f;
    
    // Total window width = brand + content sections + padding
    const float total_width = brand_width + username_width + expiry_width + fps_width + (content_padding * 2) + (element_spacing * 3);

    // Position in top-right corner with proper margin from screen edge
    const float margin = 20.0f;
    ImVec2 screenSize = ImGui::GetIO().DisplaySize;
    ImVec2 windowPos = ImVec2(screenSize.x - total_width - margin, margin);

    // Set window properties - always on top and fixed position
    ImGui::SetNextWindowPos(windowPos, ImGuiCond_Always);
    ImGui::SetNextWindowSize(ImVec2(total_width, component_height), ImGuiCond_Always);

    // Window flags matching main menu style but always visible and non-interactive
    ImGuiWindowFlags window_flags = 
        ImGuiWindowFlags_NoTitleBar |
        ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoMove |
        ImGuiWindowFlags_NoScrollbar |
        ImGuiWindowFlags_NoScrollWithMouse |
        ImGuiWindowFlags_NoCollapse |
        ImGuiWindowFlags_NoSavedSettings |
        ImGuiWindowFlags_NoInputs |
        ImGuiWindowFlags_NoFocusOnAppearing |
        ImGuiWindowFlags_NoBringToFrontOnFocus;

    // Force isolated styling - completely ignore any global animation states
    ImGui::SetNextWindowBgAlpha(0.95f);
    
    // Temporarily override any global alpha changes from animations
    ctx->Style.Alpha = 1.0f;
    ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 1.0f);

    if (ImGui::Begin("##ProfileWindow", nullptr, window_flags)) {
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImVec2 windowContentPos = ImGui::GetWindowPos();
        ImVec2 windowSize = ImGui::GetWindowSize();

        // --- Brand/Logo Section - EXACTLY matching CheckboxComponent style ---
        // Animation state for accent colors only
        struct ProfileAnimState {
            ImVec4 rect_color = ImVec4(0, 0, 0, 0);
            ImVec4 icon_color = ImVec4(0, 0, 0, 0);
        };
        static ProfileAnimState profile_anim;
        const float anim_speed = GetAnimSpeed();
        
        // Use exact same accent colors as CheckboxComponent
        profile_anim.rect_color = ImLerp(profile_anim.rect_color,
            func.GetColorWithAlpha(gui.main, 0.8f), anim_speed);
        profile_anim.icon_color = ImLerp(profile_anim.icon_color,
            gui.main, anim_speed);

        // --- Brand/Logo Section (Left side) ---
        ImVec2 brandStart = windowContentPos;
        ImVec2 brandEnd = ImVec2(brandStart.x + brand_width, brandStart.y + component_height);

        // Brand background with shadow - same as CheckboxComponent left section
        //drawList->AddRectFilled(brandStart, brandEnd,
        //    ImGui::ColorConvertFloat4ToU32(profile_anim.rect_color), corner_radius, ImDrawFlags_RoundCornersLeft);

        //// Brand shadow effect - same as CheckboxComponent
        //drawList->AddShadowRect(brandStart,
        //    ImVec2(brandEnd.x - 5, brandEnd.y),
        //    ImGui::ColorConvertFloat4ToU32(profile_anim.rect_color), 20.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersLeft, corner_radius);

        // Brand logo/icon in center of brand section
        ImGui::PushFont(iconsBig);
        const char* brandIcon = ICON_MS_SHIELD; // Or use a custom brand icon
        ImVec2 iconSize = ImGui::CalcTextSize(brandIcon);
        ImVec2 brandCenter = ImVec2(brandStart.x + brand_width / 2, brandStart.y + component_height / 2);
        ImVec2 iconPos = ImVec2(brandCenter.x - iconSize.x / 2, brandCenter.y - iconSize.y / 2);
        
        // Icon shadow - same as CheckboxComponent
        drawList->AddShadowCircle(brandCenter, 9.0f, ImGui::ColorConvertFloat4ToU32(profile_anim.icon_color), 40.0f, ImVec2(0, 0), 0, 360);
        
        // Draw icon
        drawList->AddText(iconPos, ImGui::ColorConvertFloat4ToU32(profile_anim.icon_color), brandIcon);
        ImGui::PopFont();

        // --- Content Sections ---
        float currentX = brandEnd.x + content_padding;
        const float textY = brandCenter.y - ImGui::CalcTextSize("A").y / 2; // Center text vertically

        // Helper to draw text section
        auto DrawTextSection = [&](const char* label, const char* value, float width, ImColor labelColor, ImColor valueColor) {
            ImVec2 sectionStart = ImVec2(currentX, windowContentPos.y);
            ImVec2 sectionEnd = ImVec2(currentX + width, windowContentPos.y + component_height);

            // Calculate text positions for perfect vertical centering
            ImVec2 labelSize = ImGui::CalcTextSize(label);
            ImVec2 valueSize = ImGui::CalcTextSize(value);
            
            float labelY = brandCenter.y - (labelSize.y + valueSize.y + 2) / 2; // 2px spacing between lines
            float valueY = labelY + labelSize.y + 2;

            // Center text horizontally in section
            float labelX = currentX + (width - labelSize.x) / 2;
            float valueX = currentX + (width - valueSize.x) / 2;

            // Draw label (smaller text)
            drawList->AddText(ImVec2(labelX, labelY), ImGui::ColorConvertFloat4ToU32(labelColor), label);
            
            // Draw value (main text)
            drawList->AddText(ImVec2(valueX, valueY), ImGui::ColorConvertFloat4ToU32(valueColor), value);

            currentX += width + element_spacing;
        };

        // --- Username Section ---
        // Get actual username (replace with real data source)
        const char* username = "User123"; // TODO: Replace with actual username from settings/config
        DrawTextSection("User", username, username_width, gui.text[1], gui.text[0]);

        // --- Expiration Section ---
        // Get actual expiration date (replace with real data source)
        const char* expiryDate = "30 Days"; // TODO: Replace with actual expiry calculation
        DrawTextSection("License", expiryDate, expiry_width, gui.text[1], gui.text[0]);

        // --- FPS Counter Section ---
        // Get actual FPS (replace with real FPS counter)
        static char fpsText[16];
        float currentFPS = ImGui::GetIO().Framerate;
        sprintf_s(fpsText, "%.0f", currentFPS);
        
        // Color FPS based on performance
        ImColor fpsColor = gui.text[0]; // Default
        if (currentFPS >= 60) fpsColor = ImColor(0, 255, 0, 255);      // Green for good FPS
        else if (currentFPS >= 30) fpsColor = ImColor(0, 255, 255, 255); // Yellow for medium FPS
        else fpsColor = ImColor(0, 0, 255, 255);                        // Red for low FPS

        DrawTextSection("FPS", fpsText, fps_width, gui.text[1], fpsColor);

        // --- Optional: Additional visual enhancements ---
        // Add subtle separator lines between sections (very faint)
        float separatorX = brandEnd.x + content_padding + username_width + element_spacing / 2;
        for (int i = 0; i < 2; i++) {
            ImVec2 lineStart = ImVec2(separatorX, windowContentPos.y + 15);
            ImVec2 lineEnd = ImVec2(separatorX, windowContentPos.y + component_height - 15);
            drawList->AddLine(lineStart, lineEnd, ImGui::ColorConvertFloat4ToU32(ImVec4(1.0f, 1.0f, 1.0f, 0.12f)), 1.0f);
            
            separatorX += (i == 0 ? expiry_width : fps_width) + element_spacing;
        }
    }
    ImGui::End();
    ImGui::PopStyleVar(1);  // Pop 1 style var: Alpha
    
    // Restore original global alpha to not affect other windows
    ctx->Style.Alpha = originalGlobalAlpha;
    
    // Check for leaked style variables from other systems and fix them
    if (ctx->StyleVarStack.Size != originalStyleVarStackSize) {
        // Other systems leaked style variables - clean them up
        while (ctx->StyleVarStack.Size > originalStyleVarStackSize) {
            ImGui::PopStyleVar();
        }
    }
}

//void CGui::RenderHeaderWindow() {
//    // Header window properties - same width as main menu
//    const float headerHeight = 60.0f; // Same as CheckboxComponent height
//    const float menuWidth = 1020.0f; // Same as main menu width
//    const float margin = 0.0f; // No margin to align with main menu
//    
//    // Position directly above the main menu window
//    ImVec2 headerPos = ImVec2(gui.window_pos.x, gui.window_pos.y - headerHeight - 5); // 5px gap
//    
//    // Set window properties
//    ImGui::SetNextWindowPos(headerPos, ImGuiCond_Always);
//    ImGui::SetNextWindowSize(ImVec2(menuWidth, headerHeight), ImGuiCond_Always);
//
//    // Window flags - similar to main menu but fixed
//    ImGuiWindowFlags window_flags = 
//        ImGuiWindowFlags_NoTitleBar |
//        ImGuiWindowFlags_NoResize |
//        ImGuiWindowFlags_NoMove |
//        ImGuiWindowFlags_NoScrollbar |
//        ImGuiWindowFlags_NoScrollWithMouse |
//        ImGuiWindowFlags_NoCollapse |
//        ImGuiWindowFlags_NoSavedSettings |
//        ImGuiWindowFlags_NoBringToFrontOnFocus;
//
//    // Apply same transparency and animation state as main menu
//#ifndef DISABLE_ANIMATIONS
//    auto& headerBgState = AnimationSystem::g_AnimationManager.GetWindowState();
//    if (headerBgState.isAnimating) {
//        ImGui::SetNextWindowBgAlpha(headerBgState.alpha * 0.95f);
//    } else {
//        ImGui::SetNextWindowBgAlpha(0.95f);
//    }
//#else
//    ImGui::SetNextWindowBgAlpha(0.95f);
//#endif
//    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
//    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
//
//    if (ImGui::Begin("##HeaderWindow", nullptr, window_flags)) {
//        // Apply content alpha animation only within this window context
//#ifndef DISABLE_ANIMATIONS
//        auto& headerWindowState = AnimationSystem::g_AnimationManager.GetWindowState();
//        if (headerWindowState.isAnimating) {
//            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, headerWindowState.alpha);
//        }
//#endif
//        
//        ImDrawList* drawList = ImGui::GetWindowDrawList();
//        ImVec2 windowPos = ImGui::GetWindowPos();
//        ImVec2 windowSize = ImGui::GetWindowSize();
//
//        // Section widths
//        const float leftSectionWidth = 250.0f;  // User section
//        const float rightSectionWidth = 250.0f; // BITCHEATS section
//        const float centerSectionWidth = windowSize.x - leftSectionWidth - rightSectionWidth;
//
//        // Colors
//        ImVec4 accentColor = ImVec4(0.0f, 0.58f, 1.0f, 1.0f);
//        ImColor textColor = gui.text[0];
//        ImColor subtleTextColor = gui.text[1];
//
//        // === LEFT SECTION: User Logo and Name ===
//        ImVec2 leftStart = ImVec2(windowPos.x + 15, windowPos.y + 10);
//        
//        // User logo (circle)
//        float logoSize = 30.0f;
//        ImVec2 logoCenter = ImVec2(leftStart.x + logoSize, leftStart.y + logoSize / 2 + 5);
//        
//        // Draw user circle with gradient
//        drawList->AddCircleFilled(logoCenter, logoSize / 2, ImGui::ColorConvertFloat4ToU32(accentColor), 32);
//        
//        // Add user icon inside circle
//        ImGui::PushFont(iconsBig);
//        const char* userIcon = ICON_MS_PERSON;
//        ImVec2 userIconSize = ImGui::CalcTextSize(userIcon);
//        ImVec2 userIconPos = ImVec2(
//            logoCenter.x - userIconSize.x / 2,
//            logoCenter.y - userIconSize.y / 2
//        );
//        drawList->AddText(userIconPos, ImColor(255, 255, 255, 255), userIcon);
//        ImGui::PopFont();
//        
//        // Username text next to logo
//        const char* username = "Player"; // TODO: Replace with actual username
//        ImVec2 usernamePos = ImVec2(leftStart.x + logoSize * 2 + 10, logoCenter.y - ImGui::CalcTextSize(username).y / 2);
//        drawList->AddText(usernamePos, ImGui::ColorConvertFloat4ToU32(textColor), username);
//
//        // === CENTER SECTION: Game Name ===
//        const char* gameName = "Fortnite";
//        ImGui::PushFont(iconsBig);
//        ImVec2 gameNameSize = ImGui::CalcTextSize(gameName);
//        ImGui::PopFont();
//        
//        ImVec2 gameNamePos = ImVec2(
//            windowPos.x + leftSectionWidth + (centerSectionWidth - gameNameSize.x) / 2,
//            windowPos.y + (headerHeight - gameNameSize.y) / 2
//        );
//        
//        // Draw game name with glow effect
//        ImGui::PushFont(iconsBig);
//        drawList->AddText(gameNamePos, ImGui::ColorConvertFloat4ToU32(textColor), gameName);
//        ImGui::PopFont();
//
//        // === RIGHT SECTION: GIF Logo ===
//        ImVec2 rightStart = ImVec2(windowPos.x + windowSize.x - rightSectionWidth, windowPos.y);
//        float maxLogoWidth = rightSectionWidth - 20;
//        float maxLogoHeight = headerHeight - 20;
//        ImVec2 displaySize(maxLogoWidth, maxLogoHeight);
//        try {
//            ImVec2 logoPos = ImVec2(
//                rightStart.x + (rightSectionWidth - displaySize.x) / 2,
//                rightStart.y + (headerHeight - displaySize.y) / 2
//            );
//            ImGui::SetCursorScreenPos(logoPos);
//            if (g_GifLoaded && g_GifImage) {
//                HImageManager::Image_gif("C://Untitled-1.gif", displaySize, 1000.0f);
//            } else {
//                Logger::LogWarning("GIF not loaded or image pointer is null in RenderHeaderWindow");
//                const char* brandText = "BITCHEATS";
//                ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
//                ImVec2 brandTextPos = ImVec2(
//                    rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
//                    rightStart.y + (headerHeight - brandTextSize.y) / 2
//                );
//                drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
//            }
//        } catch (const std::exception& e) {
//            Logger::LogError(std::string("Exception in RenderHeaderWindow: ") + e.what());
//            const char* brandText = "BITCHEATS";
//            ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
//            ImVec2 brandTextPos = ImVec2(
//                rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
//                rightStart.y + (headerHeight - brandTextSize.y) / 2
//            );
//            drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
//        } catch (...) {
//            Logger::LogError("Unknown exception in RenderHeaderWindow");
//            const char* brandText = "BITCHEATS";
//            ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
//            ImVec2 brandTextPos = ImVec2(
//                rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
//                rightStart.y + (headerHeight - brandTextSize.y) / 2
//            );
//            drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
//        }
//
//        // Update HImageManager
//        HImageManager::updata(ImGui::GetIO().DeltaTime);
//
//        // Optional: Add subtle separators between sections
//        // Left separator
//        ImVec2 leftSepStart = ImVec2(windowPos.x + leftSectionWidth, windowPos.y + 15);
//        ImVec2 leftSepEnd = ImVec2(leftSepStart.x, windowPos.y + headerHeight - 15);
//        drawList->AddLine(leftSepStart, leftSepEnd, ImColor(255, 255, 255, 30), 1.0f);
//        
//        // Right separator
//        ImVec2 rightSepStart = ImVec2(windowPos.x + windowSize.x - rightSectionWidth, windowPos.y + 15);
//        ImVec2 rightSepEnd = ImVec2(rightSepStart.x, windowPos.y + headerHeight - 15);
//        drawList->AddLine(rightSepStart, rightSepEnd, ImColor(255, 255, 255, 30), 1.0f);
//        
//        // Pop content alpha animation if it was applied
//#ifndef DISABLE_ANIMATIONS
//        auto& headerContentState = AnimationSystem::g_AnimationManager.GetWindowState();
//        if (headerContentState.isAnimating) {
//            ImGui::PopStyleVar(); // Pop content alpha
//        }
//#endif
//    }
//    ImGui::End();
//    ImGui::PopStyleVar(2); // Pop 2 style vars: WindowRounding, WindowPadding
//}
void CGui::RenderHeaderWindow() {
    // Header window properties - same width as main menu
    const float headerHeight = 60.0f; // Same as CheckboxComponent height
    const float menuWidth = 1020.0f; // Same as main menu width

    // Position directly above the main menu window
    ImVec2 headerPos = ImVec2(gui.window_pos.x, gui.window_pos.y - headerHeight - 5); // 5px gap

    // Set window properties
    ImGui::SetNextWindowPos(headerPos, ImGuiCond_Always);
    ImGui::SetNextWindowSize(ImVec2(menuWidth, headerHeight), ImGuiCond_Always);

    // Window flags - similar to main menu but fixed
    ImGuiWindowFlags window_flags =
        ImGuiWindowFlags_NoTitleBar |
        ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoMove |
        ImGuiWindowFlags_NoScrollbar |
        ImGuiWindowFlags_NoScrollWithMouse |
        ImGuiWindowFlags_NoCollapse |
        ImGuiWindowFlags_NoSavedSettings |
        ImGuiWindowFlags_NoBringToFrontOnFocus;

    // Apply same transparency and animation state as main menu
#ifndef DISABLE_ANIMATIONS
    auto& headerBgState = AnimationSystem::g_AnimationManager.GetWindowState();
    if (headerBgState.isAnimating) {
        ImGui::SetNextWindowBgAlpha(headerBgState.alpha * 0.95f);
    }
    else {
        ImGui::SetNextWindowBgAlpha(0.95f);
    }
#else
    ImGui::SetNextWindowBgAlpha(0.95f);
#endif
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));

    if (ImGui::Begin("##HeaderWindow", nullptr, window_flags)) {
        // Apply content alpha animation only within this window context
#ifndef DISABLE_ANIMATIONS
        auto& headerWindowState = AnimationSystem::g_AnimationManager.GetWindowState();
        if (headerWindowState.isAnimating) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, headerWindowState.alpha);
        }
#endif

        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImVec2 windowPos = ImGui::GetWindowPos();
        ImVec2 windowSize = ImGui::GetWindowSize();

        // Section widths
        const float leftSectionWidth = 250.0f;  // User section
        const float rightSectionWidth = 250.0f; // GIF logo section
        const float centerSectionWidth = windowSize.x - leftSectionWidth - rightSectionWidth;

        // Colors
        ImVec4 accentColor = ImVec4(0.0f, 0.58f, 1.0f, 1.0f);
        ImColor textColor = gui.text[0];
        ImColor subtleTextColor = gui.text[1];

        // === LEFT SECTION: User Logo and Name ===
        ImVec2 leftStart = ImVec2(windowPos.x + 15, windowPos.y + 10);

        // User logo (circle)
        float logoSize = 30.0f;
        ImVec2 logoCenter = ImVec2(leftStart.x + logoSize, leftStart.y + logoSize / 2 + 5);

        // Draw user circle with gradient
        drawList->AddCircleFilled(logoCenter, logoSize / 2, ImGui::ColorConvertFloat4ToU32(accentColor), 32);

        // Add user icon inside circle
        ImGui::PushFont(iconsBig);
        const char* userIcon = ICON_MS_PERSON;
        ImVec2 userIconSize = ImGui::CalcTextSize(userIcon);
        ImVec2 userIconPos = ImVec2(
            logoCenter.x - userIconSize.x / 2,
            logoCenter.y - userIconSize.y / 2
        );
        drawList->AddText(userIconPos, ImColor(255, 255, 255, 255), userIcon);
        ImGui::PopFont();

        // Username text next to logo
        const char* username = "Player"; // TODO: Replace with actual username
        ImVec2 usernamePos = ImVec2(leftStart.x + logoSize * 2 + 10, logoCenter.y - ImGui::CalcTextSize(username).y / 2);
        drawList->AddText(usernamePos, ImGui::ColorConvertFloat4ToU32(textColor), username);

        // === CENTER SECTION: Game Name ===
        const char* gameName = "Fortnite";
        ImGui::PushFont(iconsBig);
        ImVec2 gameNameSize = ImGui::CalcTextSize(gameName);
        ImGui::PopFont();

        ImVec2 gameNamePos = ImVec2(
            windowPos.x + leftSectionWidth + (centerSectionWidth - gameNameSize.x) / 2,
            windowPos.y + (headerHeight - gameNameSize.y) / 2
        );

        // Draw game name with glow effect
        ImGui::PushFont(iconsBig);
        drawList->AddText(gameNamePos, ImGui::ColorConvertFloat4ToU32(textColor), gameName);
        ImGui::PopFont();

        // === RIGHT SECTION: GIF Logo ===
        ImVec2 rightStart = ImVec2(windowPos.x + windowSize.x - rightSectionWidth, windowPos.y);
        float maxLogoWidth = rightSectionWidth - 20;
        float maxLogoHeight = headerHeight - 20;

        // Calculate position to center the GIF
        ImVec2 logoPos = ImVec2(
            rightStart.x + (rightSectionWidth - maxLogoWidth) / 2,
            rightStart.y + (headerHeight - maxLogoHeight) / 2
        );

        // Render animated GIF logo
        extern std::shared_ptr<HImage> g_GifImage;
        extern bool g_GifLoaded;
        extern std::string GIF_PATH;
        
        // Render GIF with crash protection
        try {
            Logger::Log("[GUI] About to render header GIF");
            
            if (g_GifLoaded && g_GifImage && g_GifImage->IsValid()) {
                Logger::Log("[GUI] GIF is loaded and valid, calculating size");
                
                // Calculate size maintaining aspect ratio
                float gifWidth = static_cast<float>(g_GifImage->GetWidth());
                float gifHeight = static_cast<float>(g_GifImage->GetHeight());
                
                Logger::Log("[GUI] GIF dimensions: " + std::to_string(gifWidth) + "x" + std::to_string(gifHeight));
                
                if (gifWidth > 0 && gifHeight > 0) {
                    float aspectRatio = gifWidth / gifHeight;
                    
                    ImVec2 renderSize;
                    if (aspectRatio > 1.0f) {
                        // Wide image
                        renderSize.x = (std::min)(maxLogoWidth, gifWidth * 0.5f); // Scale down for header
                        renderSize.y = renderSize.x / aspectRatio;
                    } else {
                        // Tall image
                        renderSize.y = (std::min)(maxLogoHeight, gifHeight * 0.5f); // Scale down for header
                        renderSize.x = renderSize.y * aspectRatio;
                    }
                    
                    Logger::Log("[GUI] Calculated render size: " + std::to_string(renderSize.x) + "x" + std::to_string(renderSize.y));
                    
                    // Ensure minimum size
                    if (renderSize.x < 1.0f || renderSize.y < 1.0f) {
                        Logger::LogWarning("[GUI] Render size too small, using default");
                        renderSize = ImVec2(40.0f, 20.0f); // Default size
                    }
                    
                    // Center the GIF
                    ImVec2 centeredPos = ImVec2(
                        rightStart.x + (rightSectionWidth - renderSize.x) / 2,
                        rightStart.y + (headerHeight - renderSize.y) / 2
                    );
                    
                    Logger::Log("[GUI] Centered position: " + std::to_string(centeredPos.x) + "," + std::to_string(centeredPos.y));
                    
                    // Set cursor position for ImGui rendering
                    ImGui::SetCursorScreenPos(centeredPos);
                    
                    Logger::Log("[GUI] About to get current texture");
                    
                    // Render the animated GIF
                    auto texture = g_GifImage->GetCurrentTexture();
                    Logger::Log("[GUI] Retrieved texture pointer: " + std::to_string(reinterpret_cast<uintptr_t>(texture)));
                    
                    if (texture) {
                        Logger::Log("[GUI] About to call ImGui::Image");
                        ImGui::Image(reinterpret_cast<ImTextureID>(texture), renderSize);
                        Logger::Log("[GUI] ImGui::Image call completed successfully");
                    } else {
                        Logger::LogWarning("[GUI] Texture is null, showing fallback");
                        // Fallback if texture is not ready
                        const char* brandText = "LOADING...";
                        ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
                        ImVec2 brandTextPos = ImVec2(
                            rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
                            rightStart.y + (headerHeight - brandTextSize.y) / 2
                        );
                        drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
                    }
                } else {
                    // Invalid dimensions fallback
                    const char* brandText = "BITCHEATS";
                    ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
                    ImVec2 brandTextPos = ImVec2(
                        rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
                        rightStart.y + (headerHeight - brandTextSize.y) / 2
                    );
                    drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
                }
            } else {
                // Display fallback when GIF is not loaded
                const char* brandText = "BITCHEATS";
                ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
                ImVec2 brandTextPos = ImVec2(
                    rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
                    rightStart.y + (headerHeight - brandTextSize.y) / 2
                );
                drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
                }
            } else {
                Logger::LogWarning("[GUI] GIF not loaded or invalid, showing fallback");
            }
            
            Logger::Log("[GUI] Header GIF rendering completed successfully");
        }
        catch (const std::exception& e) {
            Logger::LogError("[GUI] Exception in GIF rendering: " + std::string(e.what()));
            // Emergency fallback in case of any crashes
            const char* brandText = "BITCHEATS";
            ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
            ImVec2 brandTextPos = ImVec2(
                rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
                rightStart.y + (headerHeight - brandTextSize.y) / 2
            );
            drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
        }
        catch (...) {
            Logger::LogError("[GUI] Unknown exception in GIF rendering");
            // Emergency fallback in case of any crashes
            const char* brandText = "BITCHEATS";
            ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
            ImVec2 brandTextPos = ImVec2(
                rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
                rightStart.y + (headerHeight - brandTextSize.y) / 2
            );
            drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
        }

        // Optional: Add subtle separators between sections
        // Left separator
        ImVec2 leftSepStart = ImVec2(windowPos.x + leftSectionWidth, windowPos.y + 15);
        ImVec2 leftSepEnd = ImVec2(leftSepStart.x, windowPos.y + headerHeight - 15);
        drawList->AddLine(leftSepStart, leftSepEnd, ImColor(255, 255, 255, 30), 1.0f);

        // Right separator
        ImVec2 rightSepStart = ImVec2(windowPos.x + windowSize.x - rightSectionWidth, windowPos.y + 15);
        ImVec2 rightSepEnd = ImVec2(rightSepStart.x, windowPos.y + headerHeight - 15);
        drawList->AddLine(rightSepStart, rightSepEnd, ImColor(255, 255, 255, 30), 1.0f);

        // Pop content alpha animation if it was applied
#ifndef DISABLE_ANIMATIONS
        auto& headerContentState = AnimationSystem::g_AnimationManager.GetWindowState();
        if (headerContentState.isAnimating) {
            ImGui::PopStyleVar(); // Pop content alpha
        }
#endif
    }
    ImGui::End();
    ImGui::PopStyleVar(2); // Pop 2 style vars: WindowRounding, WindowPadding
}

// CGui Animation Integration Methods Implementation

void CGui::InitializeAnimations() {
    if (!animationsInitialized) {
        AnimationSystem::g_AnimationManager.Initialize();
        animationsInitialized = true;
    }
}

void CGui::UpdateAnimations() {
    if (animationsInitialized) {
        AnimationSystem::g_AnimationManager.Update();
    }
}

void CGui::StartMenuShowAnimation() {
    isMenuAnimating = true;
    // Use smooth fade animation instead of bouncy scale for iOS-like feel
    AnimationSystem::g_AnimationManager.ShowWindow(0.2f, AnimationSystem::AnimationType::FadeIn);
    
    // Also start content fade in after a short delay
    StartContentFadeIn();
}

void CGui::StartMenuHideAnimation() {
    isMenuAnimating = true;
    // Use smooth fade out animation for iOS-like feel
    AnimationSystem::g_AnimationManager.HideWindow(0.15f, AnimationSystem::AnimationType::FadeOut);
}

void CGui::StartTabSwitchAnimation(int fromTab, int toTab) {
    isTabSwitching = true;
    AnimationSystem::g_AnimationManager.StartTabTransition(fromTab, toTab, 0.3f, AnimationSystem::AnimationType::SlideLeft);
    
    // Reset content animation for new tab
    AnimationSystem::g_AnimationManager.ResetContentAnimation();
    StartContentFadeIn();
}

void CGui::StartContentFadeIn() {
    // Estimate number of content elements (this will be refined per tab)
    contentElementCount = 15; // Average number of UI elements per tab
    AnimationSystem::g_AnimationManager.StartContentAnimation(contentElementCount, 0.05f, 0.6f);
}

void CGui::RenderAnimatedElement(int elementIndex, std::function<void()> renderFunction) {
    if (ShouldSkipElement(elementIndex)) {
        return;
    }
    
    ApplyElementAnimation(elementIndex);
    renderFunction();
}

void CGui::ShowAnimatedPopup(const char* popupId) {
    AnimationSystem::g_AnimationManager.ShowPopup(0.25f, AnimationSystem::AnimationType::ScaleIn);
    ImGui::OpenPopup(popupId);
}

void CGui::HideAnimatedPopup(const char* popupId) {
    AnimationSystem::g_AnimationManager.HidePopup(0.2f, AnimationSystem::AnimationType::ScaleOut);
}

bool CGui::BeginAnimatedWindow(const char* name, bool* p_open, ImGuiWindowFlags flags) {
    auto& animatedState = AnimationSystem::g_AnimationManager.GetWindowState();
    
    // Apply window-level alpha animation only to this specific window
    // Don't use PushStyleVar as it affects all subsequent windows
    if (animatedState.alpha < 1.0f) {
        ImGui::SetNextWindowBgAlpha(animatedState.alpha * 0.95f); // Apply to background
        // Note: We'll handle content alpha inside the window context
    } else {
        ImGui::SetNextWindowBgAlpha(0.95f); // Default transparency
    }
    
    return ImGui::Begin(name, p_open, flags);
}

void CGui::EndAnimatedWindow() {
    // No need to pop style vars since we're using SetNextWindowBgAlpha instead
    ImGui::End();
}

void CGui::BeginAnimatedContent() {
    // Reset element index for this frame
    featureIndex = 0;
}

void CGui::EndAnimatedContent() {
    // Content animation frame complete
}

bool CGui::ShouldSkipElement(int elementIndex) {
    // Always render during transitions to avoid flicker
    auto& tabState = AnimationSystem::g_AnimationManager.GetTabState();
    if (tabState.isTransitioning) {
        return false;
    }
    
    // Check if element should be rendered based on animation state
    return !AnimationSystem::g_AnimationManager.ShouldRenderElement(elementIndex);
}

void CGui::ApplyElementAnimation(int elementIndex) {
    float alpha = AnimationSystem::g_AnimationManager.GetElementAlpha(elementIndex);
    float slideOffset = AnimationSystem::g_AnimationManager.GetElementSlideOffset(elementIndex);
    float scale = AnimationSystem::g_AnimationManager.GetElementScale(elementIndex);
    
    // Note: Individual element alpha should be handled by the elements themselves
    // to avoid affecting other UI components. For now, we'll only handle slide offset.
    
    if (slideOffset != 0.0f) {
        ImVec2 currentPos = ImGui::GetCursorPos();
        ImGui::SetCursorPos(ImVec2(currentPos.x, currentPos.y + slideOffset));
    }
    
    // Scale can be applied to individual elements if needed
    // For now, we'll just use slide offset to avoid global state pollution
}
