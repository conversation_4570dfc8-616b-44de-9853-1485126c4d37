#include <map>
#include <string>
#include <Windows.h>
#include <vector>
#include <algorithm>

namespace InputManager {
    // Key name mapping for all possible keys
    static std::map<int, std::string> KeyNames = {
        {0, "None"},

        // Mouse buttons
        {V<PERSON>_LBUTTON, "Left Mouse"},
        {V<PERSON>_RBUTTON, "Right Mouse"},
        {VK_MBUTTON, "Middle Mouse"},
        {VK_XBUTTON1, "Mouse 4"},
        {VK_XBUTTON2, "Mouse 5"},

        // Standard keys
        {VK_BACK, "Backspace"},
        {VK_TAB, "Tab"},
        {VK_CLEAR, "Clear"},
        {VK_RETURN, "Enter"},
        {VK_SHIFT, "Shift"},
        {VK_CONTROL, "Ctrl"},
        {VK_MENU, "Alt"},
        {VK_PAUSE, "Pause"},
        {VK_CAPITAL, "Caps Lock"},
        {VK_ESCAPE, "Escape"},
        {VK_SPACE, "Space"},
        {VK_PRIOR, "Page Up"},
        {V<PERSON>_NEXT, "Page Down"},
        {VK_END, "End"},
        {VK_HOME, "Home"},
        {VK_LEFT, "Left Arrow"},
        {VK_UP, "Up Arrow"},
        {VK_RIGHT, "Right Arrow"},
        {VK_DOWN, "Down Arrow"},
        {VK_SELECT, "Select"},
        {VK_PRINT, "Print"},
        {VK_EXECUTE, "Execute"},
        {VK_SNAPSHOT, "Print Screen"},
        {VK_INSERT, "Insert"},
        {VK_DELETE, "Delete"},
        {VK_HELP, "Help"},

        // Numeric keys
        {'0', "0"}, {'1', "1"}, {'2', "2"}, {'3', "3"}, {'4', "4"},
        {'5', "5"}, {'6', "6"}, {'7', "7"}, {'8', "8"}, {'9', "9"},

        // Alphabet keys
        {'A', "A"}, {'B', "B"}, {'C', "C"}, {'D', "D"}, {'E', "E"},
        {'F', "F"}, {'G', "G"}, {'H', "H"}, {'I', "I"}, {'J', "J"},
        {'K', "K"}, {'L', "L"}, {'M', "M"}, {'N', "N"}, {'O', "O"},
        {'P', "P"}, {'Q', "Q"}, {'R', "R"}, {'S', "S"}, {'T', "T"},
        {'U', "U"}, {'V', "V"}, {'W', "W"}, {'X', "X"}, {'Y', "Y"},
        {'Z', "Z"},

        // Numpad keys
        {VK_NUMPAD0, "Num 0"}, {VK_NUMPAD1, "Num 1"}, {VK_NUMPAD2, "Num 2"},
        {VK_NUMPAD3, "Num 3"}, {VK_NUMPAD4, "Num 4"}, {VK_NUMPAD5, "Num 5"},
        {VK_NUMPAD6, "Num 6"}, {VK_NUMPAD7, "Num 7"}, {VK_NUMPAD8, "Num 8"},
        {VK_NUMPAD9, "Num 9"},
        {VK_MULTIPLY, "Num *"}, {VK_ADD, "Num +"}, {VK_SEPARATOR, "Num Separator"},
        {VK_SUBTRACT, "Num -"}, {VK_DECIMAL, "Num ."}, {VK_DIVIDE, "Num /"},

        // Function keys
        {VK_F1, "F1"}, {VK_F2, "F2"}, {VK_F3, "F3"}, {VK_F4, "F4"},
        {VK_F5, "F5"}, {VK_F6, "F6"}, {VK_F7, "F7"}, {VK_F8, "F8"},
        {VK_F9, "F9"}, {VK_F10, "F10"}, {VK_F11, "F11"}, {VK_F12, "F12"},
        {VK_F13, "F13"}, {VK_F14, "F14"}, {VK_F15, "F15"}, {VK_F16, "F16"},
        {VK_F17, "F17"}, {VK_F18, "F18"}, {VK_F19, "F19"}, {VK_F20, "F20"},
        {VK_F21, "F21"}, {VK_F22, "F22"}, {VK_F23, "F23"}, {VK_F24, "F24"},

        // Special keys
        {VK_NUMLOCK, "Num Lock"}, {VK_SCROLL, "Scroll Lock"},
        {VK_LSHIFT, "Left Shift"}, {VK_RSHIFT, "Right Shift"},
        {VK_LCONTROL, "Left Ctrl"}, {VK_RCONTROL, "Right Ctrl"},
        {VK_LMENU, "Left Alt"}, {VK_RMENU, "Right Alt"},
        {VK_OEM_1, ";"}, {VK_OEM_PLUS, "+"}, {VK_OEM_COMMA, ","},
        {VK_OEM_MINUS, "-"}, {VK_OEM_PERIOD, "."}, {VK_OEM_2, "/"},
        {VK_OEM_3, "`"}, {VK_OEM_4, "["}, {VK_OEM_5, "\\"},
        {VK_OEM_6, "]"}, {VK_OEM_7, "'"}
    };

    inline std::string GetKeyName(int key) {
        auto it = KeyNames.find(key);
        if (it != KeyNames.end()) {
            return it->second;
        }
        return "Unknown";
    }

    // Current key states
    static std::vector<int> pressedKeys;
    static std::vector<int> justPressedKeys;
    static std::vector<int> justReleasedKeys;

    inline void UpdateKeyStates() {
        justPressedKeys.clear();
        justReleasedKeys.clear();

        // Check all possible keys (0-254)
        for (int i = 0; i < 256; i++) {
            bool isPressed = (GetAsyncKeyState(i) & 0x8000) != 0;
            bool wasPressed = std::find(pressedKeys.begin(), pressedKeys.end(), i) != pressedKeys.end();

            if (isPressed && !wasPressed) {
                justPressedKeys.push_back(i);
            }
            else if (!isPressed && wasPressed) {
                justReleasedKeys.push_back(i);
            }
        }

        pressedKeys.clear();
        for (int i = 0; i < 256; i++) {
            if ((GetAsyncKeyState(i) & 0x8000) != 0) {
                pressedKeys.push_back(i);
            }
        }
    }

    inline bool IsKeyDown(int key) {
        return std::find(pressedKeys.begin(), pressedKeys.end(), key) != pressedKeys.end();
    }

    inline bool IsKeyPressed(int key) {
        return std::find(justPressedKeys.begin(), justPressedKeys.end(), key) != justPressedKeys.end();
    }

    inline bool IsKeyReleased(int key) {
        return std::find(justReleasedKeys.begin(), justReleasedKeys.end(), key) != justReleasedKeys.end();
    }

    inline int GetPressedKey() {
        UpdateKeyStates();
        if (!justPressedKeys.empty()) {
            return justPressedKeys[0];
        }
        return 0;
    }
}