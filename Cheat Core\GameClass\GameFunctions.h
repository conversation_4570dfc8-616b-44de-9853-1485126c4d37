#pragma once
#include <windows.h>
#include "../Framwork/Vectors.h"
#include "../Kernel Driver/Driver/Driver.h"
#include "Offsets.h"
#include "../Framwork/Math.h"
#include "../Framwork/HexArray.h"

// External global variables
inline uintptr_t g_CurrentTarget = NULL;
inline float g_ClosestDistance = FLT_MAX;
inline uintptr_t g_Targeted_fort_pawn = NULL;
inline uintptr_t g_AcknowlegedPawn = NULL;

inline DWORD astime_ = 0;
inline DWORD astime_Delay = 0;
inline bool IsPressed = false;
inline int selectedRandomHitbox = -1; // Initialize with an invalid value
inline bool hasTarget = false;

// Namespace for game-specific functions
namespace GameFunctions {
    inline bool isOnScreen(const Vector2& pos) {
        return pos.x >= 0 && pos.x <= OverlayWidth && pos.y >= 0 && pos.y <= OverlayHeight;
    }
    // Get bone position from mesh
    inline  FTransform GetBoneIndex(DWORD_PTR mesh, int index)
    {
        DWORD_PTR bonearray = UseDriver::read<DWORD_PTR>(mesh + Offsets::BoneArray);

        if (bonearray == NULL) {
            bonearray = UseDriver::read<DWORD_PTR>(mesh + Offsets::BoneArray + 0x10);
        }
        FTransform boneTransform = UseDriver::read<FTransform>(bonearray + (index * 0x60));
        return boneTransform;
    }

    inline Vector3 GetBoneWithRotation(DWORD_PTR mesh, int id) {
        FTransform bone = GetBoneIndex(mesh, id);
        FTransform ComponentToWorld = UseDriver::read<FTransform>(mesh + Offsets::ComponentToWorld);
        D3DMATRIX Matrix = Math::MatrixMultiplication(bone.ToMatrixWithScale(), ComponentToWorld.ToMatrixWithScale());
        return Vector3(Matrix._41, Matrix._42, Matrix._43);
    }
    inline Vector2 ProjectWorldToScreen(Vector3 WorldLocation)
    {
        struct Rotation {
            double a; //0x0000
            char pad_0008[24]; //0x0008
            double b; //0x0020
            char pad_0028[424]; //0x0028
            double c; //0x01D0
        };

        // Retrieve camera description
        uintptr_t locationPointer = UseDriver::read<uintptr_t>(Results::Uworld + Offsets::CameraLocation);
        uintptr_t rotationPointer = UseDriver::read<uintptr_t>(Results::Uworld + Offsets::CameraRotation);

        Rotation rotation = UseDriver::read<Rotation>(rotationPointer);

        vCamera.Location = UseDriver::read<Vector3>(locationPointer);
        vCamera.Rotation.x = asin(rotation.c) * (180.0 / M_PI);
        vCamera.Rotation.y = atan2(-rotation.a, rotation.b) * (180.0 / M_PI);
        vCamera.Rotation.x = (vCamera.Rotation.x > 89.0) ? 89.0 : ((vCamera.Rotation.x < -89.0) ? -89.0 : vCamera.Rotation.x);
        vCamera.Rotation.y = fmod(vCamera.Rotation.y, 360.0);
        if (vCamera.Rotation.y < 0.0) vCamera.Rotation.y += 360.0;
        vCamera.FieldOfView = UseDriver::read<float>(Results::PlayerController + 0x3AC) * 90.f;
        // Calculate transformation matrices
        D3DMATRIX tempMatrix = Math::Matrix(vCamera.Rotation);
        Vector3 vAxisX = Vector3(tempMatrix.m[0][0], tempMatrix.m[0][1], tempMatrix.m[0][2]);
        Vector3 vAxisY = Vector3(tempMatrix.m[1][0], tempMatrix.m[1][1], tempMatrix.m[1][2]);
        Vector3 vAxisZ = Vector3(tempMatrix.m[2][0], tempMatrix.m[2][1], tempMatrix.m[2][2]);

        // Calculate delta and transformed vectors
        Vector3 vDelta = WorldLocation - vCamera.Location;
        Vector3 vTransformed = Vector3(vDelta.Dot(vAxisY), vDelta.Dot(vAxisZ), vDelta.Dot(vAxisX));
        if (vTransformed.z < 1.f)
            vTransformed.z = 1.f;

        float fovFactor = (OverlayWidth / 2.0f) / tanf(vCamera.FieldOfView * (float)M_PI / 360.f);
        return Vector2((OverlayWidth / 2.0f) + vTransformed.x * fovFactor / vTransformed.z, (OverlayHeight / 2.0f) - vTransformed.y * fovFactor / vTransformed.z);

    }

    inline std::string GetPlayerName(uintptr_t player_state, uintptr_t pawn_private)
    {
        bool isScriptedBot = (UseDriver::read<uint8_t>(pawn_private + Offsets::bIsScriptedBot) & 1) != 0;
        bool isAI = (UseDriver::read<char>(player_state + Offsets::bIsABot) & 0x08) != 0;


        int pNameLength; // rsi
        _WORD* pNameBufferPointer;
        int i; // ecx
        char v25; // al
        int v26; // er8
        int v29; // eax

        uintptr_t pNameStructure = UseDriver::read<uintptr_t>(player_state + 0xb08);
        pNameLength = UseDriver::read<int>(pNameStructure + 0x10);
        if (pNameLength <= 0)
        {
            if (isScriptedBot) {
                return "Store NPC";
            }
            else if (isAI) {
                return "Player AI";
            }
        }


        wchar_t* pNameBuffer = new wchar_t[pNameLength];
        uintptr_t pNameEncryptedBuffer = UseDriver::read<uintptr_t>(pNameStructure + 0x8);
        UseDriver::ReadString(pNameEncryptedBuffer, pNameBuffer, pNameLength * sizeof(wchar_t));

        v25 = pNameLength - 1;
        v26 = 0;
        pNameBufferPointer = (_WORD*)pNameBuffer;

        for (i = (v25) & 3; ; *pNameBufferPointer++ += i & 7)
        {
            v29 = pNameLength - 1;
            if (!(_DWORD)pNameLength)
                v29 = 0;

            if (v26 >= v29)
                break;

            i += 3;
            ++v26;
        }

        std::wstring temp_wstring(pNameBuffer);
        return std::string(temp_wstring.begin(), temp_wstring.end());
    }

    __forceinline auto GetPlayerPlatform(uintptr_t PlayerState) -> std::string
    {
        uintptr_t test_platform = UseDriver::read<uintptr_t>(PlayerState + 0x430);
        //std::cout << "[LOG] Platform: " << test_platform << std::endl;

        wchar_t platform[64]{ 0 };
        UseDriver::ReadString((test_platform), reinterpret_cast<uint8_t*>(platform), sizeof(platform));
        std::wstring platform_wstr(platform);
        std::string platform_str(platform_wstr.begin(), platform_wstr.end());

        if (platform_str.find(("XBL")) != std::string::npos || platform_str.find(("XSX")) != std::string::npos) {
            platform_str = "XBOX";
        }
        else if (platform_str.find(("PSN")) != std::string::npos)
        {
            platform_str = "PS4";
        }
        else if (platform_str.find(("PS5")) != std::string::npos) {
            platform_str = "PS5";
        }
        else if (platform_str.find(("WIN")) != std::string::npos) {
            platform_str = "PC";
        }
        else if (platform_str.find(("SWT")) != std::string::npos) {
            platform_str = "SWT";
        }
        else if (platform_str.find(("AND")) != std::string::npos) {
            platform_str = "AND";

        }
        else if (platform_str.find(("MAC")) != std::string::npos) {
            platform_str = "MAC";

        }
        else if (platform_str.find(("LNX")) != std::string::npos) {
            platform_str = "Linux";

        }
        else if (platform_str.find(("IOS")) != std::string::npos) {
            platform_str = "IOS";
        }

        return platform_str;
    }

    inline static auto GetWeaponName(uintptr_t PawnPrivate) -> std::string
    {
        std::string WeaponName;

        uint64_t CurrentWeapon = UseDriver::read<uint64_t>(PawnPrivate + Offsets::CurrentWeapon);
        uint64_t WeaponData = UseDriver::read<uint64_t>(CurrentWeapon + Offsets::WeaponData);
        uint64_t ItemName = UseDriver::read<uint64_t>(WeaponData + Offsets::ItemName); // UFortWeaponItemDefinition
        //if (!ItemName)
        //	return std::string("Building Plan");

        uint64_t FData = UseDriver::read<uint64_t>(ItemName + Offsets::FData);
        int FLength = UseDriver::read<int>(ItemName + Offsets::FLength);

        if (FLength > 0 && FLength < 50)
        {
            wchar_t* WeaponBuffer = new wchar_t[FLength];
            UseDriver::ReadString(FData, (PVOID)WeaponBuffer, FLength * sizeof(wchar_t));
            std::wstring wNameWeapon(WeaponBuffer);
            WeaponName = std::string(wNameWeapon.begin(), wNameWeapon.end());
            delete[] WeaponBuffer;
        }

        return WeaponName;
    }

    inline int GetPlayerKills(uintptr_t PlayerState) {
        int kills = UseDriver::read<int>(PlayerState + Offsets::KillScore);
        return kills;
    }

    // Function to get player's rank
    inline  int GetPlayerLevel(uintptr_t PlayerState) {
        int level = UseDriver::read<int>(PlayerState + Offsets::SeasonLevelUIDisplay);
        return level;
    }

    inline float GetHealth(uintptr_t ActorPawn) {
        uint64_t healthSetAddress = UseDriver::read<uint64_t>(ActorPawn + Offsets::HealthSet);
        if (healthSetAddress) {
            return UseDriver::read<float>(healthSetAddress + Offsets::Health);
        }
        return 0.0f;
    }

    inline bool IsVisible(uintptr_t skeletal_mesh) {
        double Seconds = UseDriver::read<double>(Results::Uworld + Offsets::ServerWorldTimeSecondsDelta);
        float LastRenderTime = UseDriver::read<float>(skeletal_mesh + Offsets::LastRenderTime);
        return Seconds - LastRenderTime <= 0.06f;
    }

    inline Vector3 PredictLocation(Vector3 target, Vector3 targetVelocity, float projectileSpeed, float projectileGravityScale, float distance)
    {
        float horizontalTime = distance / projectileSpeed;
        float verticalTime = distance / projectileSpeed;

        target.x += targetVelocity.x * horizontalTime;
        target.y += targetVelocity.y * horizontalTime;
        target.z += targetVelocity.z * verticalTime +
            abs(-980.f * projectileGravityScale) * 0.5f * (verticalTime * verticalTime);

        return target;
    }


}
