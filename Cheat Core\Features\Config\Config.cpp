#include "Config.h"
#include <thread>
#include <filesystem>
#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <ShlObj.h>
#include <algorithm>
#include "../../Utils.h"
#include "../../../Menu UI/ImGui/imgui.h"
#include "../../../Menu UI/ImGui/imgui_internal.h"
#include "../../../Menu UI/Framwork/GUI.h"
#include "../Input/HotkeySystem.h"
#include "../../GameClass/GameSettings.h"
#include "../../Settings/Settings.h"

// Define static storage for LocalizationManager translations
std::unordered_map<std::string, std::unordered_map<Language, std::string>> LocalizationManager::translations{};

using namespace std;

// Global instances
NotificationManager g_NotificationManager;
ConfigSnapshot g_ConfigSnapshot;
NotificationSettings g_NotificationSettings;

// Global instance of the config system
ConfigSystem g_ConfigSystem;

// ========================= NotificationManager =========================
int NotificationManager::FindExistingChange(const std::string& category, const std::string& name) {
    for (int i = 0; i < m_changes.size(); i++) {
        if (m_changes[i].category == category && m_changes[i].name == name) {
            return i;
        }
    }
    return -1; // Not found
}

void NotificationManager::UpdateChangeTime(const std::string& category, const std::string& name) {
    int index = FindExistingChange(category, name);
    if (index >= 0) {
        m_changes[index].changeTime = ImGui::GetTime();
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }
}

void NotificationManager::AddChange(const std::string& category, const std::string& name, bool oldValue, bool newValue) {
    if (oldValue == newValue) return;
    if (!g_NotificationSettings.Enable) return;

    int existingIndex = FindExistingChange(category, name);
    if (existingIndex >= 0) {
        SettingChange& change = m_changes[existingIndex];
        change.boolChange.oldValue = oldValue;
        change.boolChange.newValue = newValue;
        change.changeTime = ImGui::GetTime();
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    } else {
        SettingChange change{};
        change.category = category;
        change.name = name;
        change.type = ChangeType::Enable;
        change.boolChange.oldValue = oldValue;
        change.boolChange.newValue = newValue;
        change.changeTime = ImGui::GetTime();
        m_changes.push_back(change);
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }
}

void NotificationManager::AddChange(const std::string& category, const std::string& name, float oldValue, float newValue) {
    if (abs(oldValue - newValue) < 0.001f) return;
    if (!g_NotificationSettings.Enable) return;

    int existingIndex = FindExistingChange(category, name);
    if (existingIndex >= 0) {
        SettingChange& change = m_changes[existingIndex];
        change.valueChange.oldValue = oldValue;
        change.valueChange.newValue = newValue;
        change.changeTime = ImGui::GetTime();
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    } else {
        SettingChange change{};
        change.category = category;
        change.name = name;
        change.type = ChangeType::Value;
        change.valueChange.oldValue = oldValue;
        change.valueChange.newValue = newValue;
        change.changeTime = ImGui::GetTime();
        m_changes.push_back(change);
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }
}

void NotificationManager::AddChange(const std::string& category, const std::string& name, float oldColor[3], float newColor[3]) {
    if (abs(oldColor[0] - newColor[0]) < 0.001f &&
        abs(oldColor[1] - newColor[1]) < 0.001f &&
        abs(oldColor[2] - newColor[2]) < 0.001f) {
        return;
    }
    if (!g_NotificationSettings.Enable) return;

    int existingIndex = FindExistingChange(category, name);
    if (existingIndex >= 0) {
        SettingChange& change = m_changes[existingIndex];
        for (int i = 0; i < 3; i++) {
            change.colorChange.oldColor[i] = oldColor[i];
            change.colorChange.newColor[i] = newColor[i];
        }
        change.changeTime = ImGui::GetTime();
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    } else {
        SettingChange change{};
        change.category = category;
        change.name = name;
        change.type = ChangeType::Color;
        for (int i = 0; i < 3; i++) {
            change.colorChange.oldColor[i] = oldColor[i];
            change.colorChange.newColor[i] = newColor[i];
        }
        change.changeTime = ImGui::GetTime();
        m_changes.push_back(change);
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }
}

void NotificationManager::ClearChanges() {
    m_changes.clear();
    m_notificationsActive = false;
}

const std::vector<SettingChange>& NotificationManager::GetChanges() const {
    return m_changes;
}

void NotificationManager::ShowNotifications() {
    if (m_changes.empty() || !g_NotificationSettings.Enable) return;

    if (!m_notificationsActive) {
        m_notificationStartTime = ImGui::GetTime();
        m_notificationsActive = true;
    }

    float elapsedTime = ImGui::GetTime() - m_notificationStartTime;
    if (elapsedTime > g_NotificationSettings.DisplayDuration) {
        ClearChanges();
        return;
    }

    float progress = elapsedTime / g_NotificationSettings.DisplayDuration;
    float alpha = 1.0f;
    if (progress < 0.1f) {
        alpha = progress / 0.1f;
    } else if (progress > 0.8f) {
        alpha = (1.0f - progress) / 0.2f;
    }

    ImVec2 windowPos(20.0f, 20.0f);
    ImColor enabledColor = ImColor(0.2f, 0.8f, 0.2f, 0.8f * alpha);
    ImColor disabledColor = ImColor(0.2f, 0.2f, 0.8f, 0.8f * alpha);
    ImColor bgColor = ImColor(0.08f, 0.08f, 0.08f, 0.95f * alpha);
    ImColor accentColor = ImColor(accent_color[2], accent_color[1], accent_color[0], alpha);

    ImGui::SetNextWindowPos(windowPos);
    ImGui::SetNextWindowSize(ImVec2(320, 0));
    ImGui::SetNextWindowBgAlpha(bgColor.Value.w);

    ImGui::PushStyleVar(ImGuiStyleVar_Alpha, alpha);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(12, 12));
    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(8, 6));
    ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(8, 6));

    ImGui::PushStyleColor(ImGuiCol_WindowBg, ImGui::ColorConvertFloat4ToU32(bgColor));
    ImGui::PushStyleColor(ImGuiCol_Border, ImGui::ColorConvertFloat4ToU32(ImColor(accent_color[2], accent_color[1], accent_color[0], 0.3f)));

    ImGui::Begin("##Notifications", nullptr,
        ImGuiWindowFlags_NoTitleBar |
        ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoMove |
        ImGuiWindowFlags_NoSavedSettings |
        ImGuiWindowFlags_AlwaysAutoResize |
        ImGuiWindowFlags_NoFocusOnAppearing);

    pGUi.RenderSectionHeader("Settings Changed", 0, 0);
    ImGui::Dummy(ImVec2(0, 5));

    std::unordered_map<std::string, std::vector<const SettingChange*>> categorizedChanges;
    for (const auto& change : m_changes) {
        categorizedChanges[change.category].push_back(&change);
    }

    ImDrawList* drawList = ImGui::GetWindowDrawList();

    for (const auto& category : categorizedChanges) {
        ImColor headerBgColor = ImColor(0.05f, 0.05f, 0.08f, 0.9f * alpha);
        ImColor headerAccentColor = ImColor(accentColor);

        ImVec2 headerPos = ImGui::GetCursorScreenPos();
        ImVec2 headerSize = ImVec2(ImGui::GetContentRegionAvail().x, 32.0f);
        drawList->AddRectFilled(headerPos, ImVec2(headerPos.x + headerSize.x, headerPos.y + headerSize.y), headerBgColor, 4.0f);
        drawList->AddRect(headerPos, ImVec2(headerPos.x + headerSize.x, headerPos.y + headerSize.y), ImColor(1.0f, 1.0f, 1.0f, 0.05f * alpha), 4.0f, 0, 1.0f);
        drawList->AddShadowRect(headerPos, ImVec2(headerPos.x + headerSize.x, headerPos.y + headerSize.y), ImColor(0.0f, 0.0f, 0.0f, 0.4f * alpha), 12.0f, ImVec2(0, 2), ImDrawFlags_RoundCornersAll);

        ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
        ImVec2 textSize = ImGui::CalcTextSize(category.first.c_str());
        ImVec2 textPos = ImVec2(headerPos.x + (headerSize.x - textSize.x) * 0.5f, headerPos.y + (headerSize.y - textSize.y) * 0.5f - 1);
        drawList->AddText(ImVec2(textPos.x + 1, textPos.y + 1), ImColor(0.0f, 0.0f, 0.0f, 0.5f * alpha), category.first.c_str());
        drawList->AddText(textPos, ImColor(1.0f, 1.0f, 1.0f, 0.95f * alpha), category.first.c_str());
        ImGui::PopFont();
        ImGui::Dummy(ImVec2(0, headerSize.y));
        ImGui::Dummy(ImVec2(0, 6));

        for (const auto* change : category.second) {
            ImGui::Indent(10.0f);
            switch (change->type) {
                case ChangeType::Enable: {
                    ImGui::Text("%s:", change->name.c_str());
                    ImGui::BeginGroup();

                    float time = ImGui::GetTime() - change->changeTime;
                    float line_opacity = ImLerp(0.0f, 1.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
                    float element_opacity = ImLerp(0.0f, 0.04f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));

                    ImVec2 boxStart = ImGui::GetCursorScreenPos();
                    ImVec2 boxSize(80.0f, 24.0f);
                    ImColor fromColor = change->boolChange.oldValue ? enabledColor : disabledColor;
                    ImColor borderColor = ImColor(255, 255, 255, 10);
                    drawList->AddRectFilled(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), ImColor(1.0f, 1.0f, 1.0f, element_opacity), 3.0f);
                    drawList->AddRect(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), ImGui::ColorConvertFloat4ToU32(borderColor), 3.0f, 0, 1.0f);

                    ImVec2 fromTextSize = ImGui::CalcTextSize(change->boolChange.oldValue ? "Enabled" : "Disabled");
                    ImVec2 label_pos = ImVec2(boxStart.x + (boxSize.x - fromTextSize.x) * 0.5f, boxStart.y + (boxSize.y - fromTextSize.y) * 0.5f);
                    float text_opacity = ImLerp(0.3f, 1.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
                    drawList->AddText(label_pos, ImColor(1.0f, 1.0f, 1.0f, text_opacity), change->boolChange.oldValue ? "Enabled" : "Disabled");

                    const float line_height = 1.0f;
                    const float line_padding = 10.0f;
                    ImVec2 line_start = ImVec2(boxStart.x + line_padding, boxStart.y + boxSize.y - 2);
                    ImVec2 line_end = ImVec2(boxStart.x + boxSize.x - line_padding, boxStart.y + boxSize.y - 2 + line_height);
                    drawList->AddShadowRect(line_start, line_end, fromColor, 25.f, ImVec2(0, 2), ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight, 60.f);
                    drawList->AddRectFilled(line_start, line_end, fromColor, 360.f, ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight);
                    drawList->AddRectFilled(line_start, line_end, ImColor(1.0f, 1.0f, 1.0f, line_opacity * 0.2f), 360.f, ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight);
                    ImGui::Dummy(boxSize);

                    ImGui::SameLine();
                    ImVec2 arrowStart = ImGui::GetCursorScreenPos();
                    ImVec2 arrowSize = ImVec2(40.0f, boxSize.y);
                    float centerY = arrowStart.y + arrowSize.y * 0.5f;
                    float arrowWidth = arrowSize.x - 10.0f;
                    float head_size = 6.0f;
                    float line_thickness = 2.0f;
                    ImVec2 lineStart = ImVec2(arrowStart.x + 5.0f, centerY);
                    ImVec2 lineEnd = ImVec2(arrowStart.x + arrowWidth, centerY);
                    drawList->AddShadowRect(ImVec2(lineStart.x, lineStart.y - line_thickness * 0.5f), ImVec2(lineEnd.x, lineEnd.y + line_thickness * 0.5f), accentColor, 8.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersAll);
                    drawList->AddLine(lineStart, lineEnd, accentColor, line_thickness);
                    ImVec2 head_left = ImVec2(lineEnd.x - head_size, centerY - head_size);
                    ImVec2 head_right = ImVec2(lineEnd.x - head_size, centerY + head_size);
                    ImVec2 head_tip = ImVec2(lineEnd.x + 2.0f, centerY);
                    ImVec2 headPoints[3] = { head_left, head_tip, head_right };
                    drawList->AddConvexPolyFilled(headPoints, 3, accentColor);
                    ImGui::Dummy(arrowSize);

                    ImGui::SameLine();
                    boxStart = ImGui::GetCursorScreenPos();
                    ImColor toColor = change->boolChange.newValue ? enabledColor : disabledColor;
                    drawList->AddRectFilled(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), ImColor(1.0f, 1.0f, 1.0f, element_opacity), 3.0f);
                    drawList->AddRect(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), ImGui::ColorConvertFloat4ToU32(borderColor), 3.0f, 0, 1.0f);
                    ImVec2 toTextSize = ImGui::CalcTextSize(change->boolChange.newValue ? "Enabled" : "Disabled");
                    label_pos = ImVec2(boxStart.x + (boxSize.x - toTextSize.x) * 0.5f, boxStart.y + (boxSize.y - toTextSize.y) * 0.5f);
                    text_opacity = change->boolChange.newValue ? 1.0f : 0.5f;
                    drawList->AddText(label_pos, ImColor(1.0f, 1.0f, 1.0f, text_opacity), change->boolChange.newValue ? "Enabled" : "Disabled");
                    line_start = ImVec2(boxStart.x + line_padding, boxStart.y + boxSize.y - 2);
                    line_end = ImVec2(boxStart.x + boxSize.x - line_padding, boxStart.y + boxSize.y - 2 + line_height);
                    drawList->AddShadowRect(line_start, line_end, toColor, 25.f, ImVec2(0, 2), ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight, 60.f);
                    drawList->AddRectFilled(line_start, line_end, toColor, 360.f, ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight);
                    drawList->AddRectFilled(line_start, line_end, ImColor(1.0f, 1.0f, 1.0f, line_opacity * 0.2f), 360.f, ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight);

                    ImGui::Dummy(boxSize);
                    ImGui::EndGroup();
                    ImGui::Dummy(ImVec2(0, 6));
                    break;
                }
                case ChangeType::Value: {
                    ImGui::Text("%s:", change->name.c_str());
                    ImGui::BeginGroup();

                    char oldValueText[32], newValueText[32];
                    snprintf(oldValueText, sizeof(oldValueText), "%.2f", change->valueChange.oldValue);
                    snprintf(newValueText, sizeof(newValueText), "%.2f", change->valueChange.newValue);

                    float line_opacity = ImLerp(0.0f, 1.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
                    float element_opacity = ImLerp(0.0f, 0.04f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));

                    ImVec2 boxStart = ImGui::GetCursorScreenPos();
                    ImVec2 boxSize(80.0f, 24.0f);
                    ImColor fromColor = ImColor(0.7f, 0.3f, 0.2f, 0.8f * alpha);
                    ImColor borderColor = ImColor(255, 255, 255, 10);
                    drawList->AddRectFilled(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), ImColor(1.0f, 1.0f, 1.0f, element_opacity), 3.0f);
                    drawList->AddRect(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), ImGui::ColorConvertFloat4ToU32(borderColor), 3.0f, 0, 1.0f);
                    ImVec2 fromTextSize = ImGui::CalcTextSize(oldValueText);
                    ImVec2 label_pos = ImVec2(boxStart.x + (boxSize.x - fromTextSize.x) * 0.5f, boxStart.y + (boxSize.y - fromTextSize.y) * 0.5f);
                    float text_opacity = ImLerp(0.3f, 1.0f, 0.07f * (1.0f - ImGui::GetIO().DeltaTime));
                    drawList->AddText(label_pos, ImColor(1.0f, 1.0f, 1.0f, text_opacity), oldValueText);

                    const float line_height = 1.0f;
                    const float line_padding = 10.0f;
                    ImVec2 line_start = ImVec2(boxStart.x + line_padding, boxStart.y + boxSize.y - 2);
                    ImVec2 line_end = ImVec2(boxStart.x + boxSize.x - line_padding, boxStart.y + boxSize.y - 2 + line_height);
                    drawList->AddShadowRect(line_start, line_end, fromColor, 25.f, ImVec2(0, 2), ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight, 60.f);
                    drawList->AddRectFilled(line_start, line_end, fromColor, 360.f, ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight);
                    drawList->AddRectFilled(line_start, line_end, ImColor(1.0f, 1.0f, 1.0f, line_opacity * 0.2f), 360.f, ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight);
                    ImGui::Dummy(boxSize);

                    ImGui::SameLine();
                    ImVec2 arrowStart = ImGui::GetCursorScreenPos();
                    ImVec2 arrowSize = ImVec2(40.0f, boxSize.y);
                    float centerY = arrowStart.y + arrowSize.y * 0.5f;
                    float arrowWidth = arrowSize.x - 10.0f;
                    float head_size = 6.0f;
                    float line_thickness = 2.0f;
                    ImVec2 lineStart = ImVec2(arrowStart.x + 5.0f, centerY);
                    ImVec2 lineEnd = ImVec2(arrowStart.x + arrowWidth, centerY);
                    drawList->AddShadowRect(ImVec2(lineStart.x, lineStart.y - line_thickness * 0.5f), ImVec2(lineEnd.x, lineEnd.y + line_thickness * 0.5f), accentColor, 8.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersAll);
                    drawList->AddLine(lineStart, lineEnd, accentColor, line_thickness);
                    ImVec2 head_left = ImVec2(lineEnd.x - head_size, centerY - head_size);
                    ImVec2 head_right = ImVec2(lineEnd.x - head_size, centerY + head_size);
                    ImVec2 head_tip = ImVec2(lineEnd.x + 2.0f, centerY);
                    ImVec2 headPoints[3] = { head_left, head_tip, head_right };
                    drawList->AddConvexPolyFilled(headPoints, 3, accentColor);
                    ImGui::Dummy(arrowSize);

                    ImGui::SameLine();
                    boxStart = ImGui::GetCursorScreenPos();
                    ImColor toColor = ImColor(0.2f, 0.7f, 0.3f, 0.8f * alpha);
                    drawList->AddRectFilled(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), ImColor(1.0f, 1.0f, 1.0f, element_opacity), 3.0f);
                    drawList->AddRect(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), ImGui::ColorConvertFloat4ToU32(borderColor), 3.0f, 0, 1.0f);
                    ImVec2 toTextSize = ImGui::CalcTextSize(newValueText);
                    label_pos = ImVec2(boxStart.x + (boxSize.x - toTextSize.x) * 0.5f, boxStart.y + (boxSize.y - toTextSize.y) * 0.5f);
                    text_opacity = 1.0f;
                    drawList->AddText(label_pos, ImColor(1.0f, 1.0f, 1.0f, text_opacity), newValueText);
                    line_start = ImVec2(boxStart.x + line_padding, boxStart.y + boxSize.y - 2);
                    line_end = ImVec2(boxStart.x + boxSize.x - line_padding, boxStart.y + boxSize.y - 2 + line_height);
                    drawList->AddShadowRect(line_start, line_end, toColor, 25.f, ImVec2(0, 2), ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight, 60.f);
                    drawList->AddRectFilled(line_start, line_end, toColor, 360.f, ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight);
                    drawList->AddRectFilled(line_start, line_end, ImColor(1.0f, 1.0f, 1.0f, line_opacity * 0.2f), 360.f, ImDrawFlags_RoundCornersLeft | ImDrawFlags_RoundCornersRight);

                    ImGui::Dummy(boxSize);
                    ImGui::EndGroup();
                    ImGui::Dummy(ImVec2(0, 6));
                    break;
                }
                case ChangeType::Color: {
                    ImGui::Text("%s:", change->name.c_str());
                    ImGui::BeginGroup();

                    ImVec2 boxStart = ImGui::GetCursorScreenPos();
                    ImVec2 boxSize(40.0f, 24.0f);
                    ImColor fromColor = ImColor(change->colorChange.oldColor[0], change->colorChange.oldColor[1], change->colorChange.oldColor[2], 1.0f * alpha);
                    drawList->AddShadowRect(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), fromColor, 6.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersAll);
                    drawList->AddRectFilled(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), fromColor, 6.0f);
                    ImGui::Dummy(boxSize);

                    ImGui::SameLine();
                    ImVec2 arrowStart = ImGui::GetCursorScreenPos();
                    ImVec2 arrowSize = ImVec2(40.0f, boxSize.y);
                    float centerY = arrowStart.y + arrowSize.y * 0.5f;
                    float arrowWidth = arrowSize.x - 10.0f;
                    float head_size = 6.0f;
                    float line_thickness = 2.0f;
                    ImVec2 lineStart = ImVec2(arrowStart.x + 5.0f, centerY);
                    ImVec2 lineEnd = ImVec2(arrowStart.x + arrowWidth, centerY);
                    drawList->AddShadowRect(ImVec2(lineStart.x, lineStart.y - line_thickness * 0.5f), ImVec2(lineEnd.x, lineEnd.y + line_thickness * 0.5f), accentColor, 8.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersAll);
                    drawList->AddLine(lineStart, lineEnd, accentColor, line_thickness);
                    ImVec2 head_left = ImVec2(lineEnd.x - head_size, centerY - head_size);
                    ImVec2 head_right = ImVec2(lineEnd.x - head_size, centerY + head_size);
                    ImVec2 head_tip = ImVec2(lineEnd.x + 2.0f, centerY);
                    ImVec2 headPoints[3] = { head_left, head_tip, head_right };
                    drawList->AddConvexPolyFilled(headPoints, 3, accentColor);
                    ImGui::Dummy(arrowSize);

                    ImGui::SameLine();
                    boxStart = ImGui::GetCursorScreenPos();
                    ImColor toColor = ImColor(change->colorChange.newColor[0], change->colorChange.newColor[1], change->colorChange.newColor[2], 1.0f * alpha);
                    drawList->AddShadowRect(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), toColor, 6.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersAll);
                    drawList->AddRectFilled(boxStart, ImVec2(boxStart.x + boxSize.x, boxStart.y + boxSize.y), toColor, 6.0f);
                    ImGui::Dummy(boxSize);

                    ImGui::EndGroup();
                    ImGui::Dummy(ImVec2(0, 6));
                    break;
                }
            }
            ImGui::Unindent(10.0f);
        }

        ImGui::Dummy(ImVec2(0, 6));
    }

    ImGui::Dummy(ImVec2(0, 8));
    ImVec2 windowSize = ImGui::GetWindowSize();
    ImVec2 barSize(ImGui::GetContentRegionAvail().x, 5.0f);
    ImVec2 barStart = ImGui::GetCursorScreenPos();
    ImVec2 barEnd = ImVec2(barStart.x + barSize.x, barStart.y + barSize.y);
    drawList->AddShadowRect(barStart, barEnd, ImColor(0.2f, 0.2f, 0.2f, 0.8f * alpha), 10.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersAll);
    float fillWidth = barSize.x * (1.0f - progress);
    ImVec2 fillEnd = ImVec2(barStart.x + fillWidth, barEnd.y);
    drawList->AddShadowRect(barStart, fillEnd, ImColor(accent_color[2], accent_color[1], accent_color[0], 0.9f * alpha), 8.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersAll);
    drawList->AddRectFilled(barStart, fillEnd, ImColor(accent_color[2], accent_color[1], accent_color[0], 1.0f * alpha), 4.0f);

    ImGui::Dummy(ImVec2(0, barSize.y + 4.0f));
    ImGui::End();
    ImGui::PopStyleColor(2);
    ImGui::PopStyleVar(5);
}

// ========================= ConfigSnapshot =========================
void ConfigSnapshot::CaptureSettings() {
    aimbot = AIMBOT_SETTINGS.general;
    triggerbot = TRIGGERBOT_SETTINGS.general;
    items = ITEM_ESP_SETTINGS;
    players = PLAYER_ESP_SETTINGS;
    keys = HOTKEY_SETTINGS;
    radar = RADAR_SETTINGS;
    configsMenu = CONFIG_SETTINGS;
}

void ConfigSnapshot::DetectChanges() {
    // Detailed change detection intentionally omitted to avoid manual field management.
    // UI can still use NotificationManager by explicitly reporting changes when applying UI actions.
}

// ========================= Config System (Clean Persistence) =========================

static std::string ToFullPath(const std::string& filename) {
    if (filename.empty()) return g_ConfigSystem.configFolder + std::string("Default.cfg");
    // Ensure folder exists
    try {
        if (!std::filesystem::exists(g_ConfigSystem.configFolder)) {
            std::filesystem::create_directories(g_ConfigSystem.configFolder);
        }
    } catch (...) {}
    return g_ConfigSystem.configFolder + filename;
}

namespace ConfigIO {
    static inline std::string BoolToStr(bool v) { return v ? "1" : "0"; }
    static inline bool StrToBool(const std::string& s) {
        return s == "1" || s == "true" || s == "True" || s == "TRUE";
    }

    static void WriteLine(std::ostream& os, const std::string& key, const std::string& value) {
        os << key << '=' << value << '\n';
    }
    static void WriteLine(std::ostream& os, const std::string& key, int value) {
        os << key << '=' << value << '\n';
    }
    static void WriteLine(std::ostream& os, const std::string& key, float value) {
        os << key << '=' << value << '\n';
    }

    static void SerializeColor(std::ostream& os, const std::string& key, const ColorRGB& c) {
        os << key << '=' << c.r << ',' << c.g << ',' << c.b << '\n';
    }
    static void SerializeColor(std::ostream& os, const std::string& key, const ColorRGBA& c) {
        os << key << '=' << c.r << ',' << c.g << ',' << c.b << ',' << c.a << '\n';
    }
    static void SerializeFont(std::ostream& os, const std::string& base, const FontSettings& f) {
        WriteLine(os, base + ".family", static_cast<int>(f.family));
        WriteLine(os, base + ".size", f.size);
        WriteLine(os, base + ".bold", BoolToStr(f.bold));
        WriteLine(os, base + ".italic", BoolToStr(f.italic));
        WriteLine(os, base + ".outline", BoolToStr(f.outline));
        WriteLine(os, base + ".outlineThickness", f.outlineThickness);
        SerializeColor(os, base + ".color", f.color);
        SerializeColor(os, base + ".outlineColor", f.outlineColor);
    }

    static void SerializeBox(std::ostream& os, const std::string& base, const BoxVisual& v) {
        WriteLine(os, base + ".enabled", BoolToStr(v.enabled));
        WriteLine(os, base + ".type", static_cast<int>(v.type));
        WriteLine(os, base + ".filled", BoolToStr(v.filled));
        WriteLine(os, base + ".thickness", v.thickness);
        WriteLine(os, base + ".rounding", v.rounding);
        SerializeColor(os, base + ".visibleColor", v.visibleColor);
        SerializeColor(os, base + ".nonVisibleColor", v.nonVisibleColor);
        SerializeColor(os, base + ".knockedVisibleColor", v.knockedVisibleColor);
        SerializeColor(os, base + ".knockedNonVisibleColor", v.knockedNonVisibleColor);
        SerializeColor(os, base + ".fillColor", v.fillColor);
    }
    static void SerializeLine(std::ostream& os, const std::string& base, const LineVisual& v) {
        WriteLine(os, base + ".enabled", BoolToStr(v.enabled));
        WriteLine(os, base + ".position", static_cast<int>(v.position));
        WriteLine(os, base + ".thickness", v.thickness);
        SerializeColor(os, base + ".visibleColor", v.visibleColor);
        SerializeColor(os, base + ".nonVisibleColor", v.nonVisibleColor);
        SerializeColor(os, base + ".knockedVisibleColor", v.knockedVisibleColor);
        SerializeColor(os, base + ".knockedNonVisibleColor", v.knockedNonVisibleColor);
    }
    static void SerializeSkeleton(std::ostream& os, const std::string& base, const SkeletonVisual& v) {
        WriteLine(os, base + ".enabled", BoolToStr(v.enabled));
        WriteLine(os, base + ".curved", BoolToStr(v.curved));
        WriteLine(os, base + ".thickness", v.thickness);
        WriteLine(os, base + ".rounding", v.rounding);
        SerializeColor(os, base + ".visibleColor", v.visibleColor);
        SerializeColor(os, base + ".nonVisibleColor", v.nonVisibleColor);
        SerializeColor(os, base + ".knockedVisibleColor", v.knockedVisibleColor);
        SerializeColor(os, base + ".knockedNonVisibleColor", v.knockedNonVisibleColor);
    }
    static void SerializeCircle(std::ostream& os, const std::string& base, const CircleVisual& v) {
        WriteLine(os, base + ".enabled", BoolToStr(v.enabled));
        WriteLine(os, base + ".thickness", v.thickness);
        SerializeColor(os, base + ".visibleColor", v.visibleColor);
        SerializeColor(os, base + ".nonVisibleColor", v.nonVisibleColor);
        SerializeColor(os, base + ".knockedVisibleColor", v.knockedVisibleColor);
        SerializeColor(os, base + ".knockedNonVisibleColor", v.knockedNonVisibleColor);
    }
    static void SerializeText(std::ostream& os, const std::string& base, const TextVisual& v) {
        WriteLine(os, base + ".enabled", BoolToStr(v.enabled));
        SerializeFont(os, base + ".font", v.font);
        WriteLine(os, base + ".showDistance", BoolToStr(v.showDistance));
        WriteLine(os, base + ".showIcons", BoolToStr(v.showIcons));
        WriteLine(os, base + ".iconSize", v.iconSize);
    }

    static void SerializeAimbotCore(std::ostream& os, const std::string& base, const AimbotCore& a) {
        WriteLine(os, base + ".enabled", BoolToStr(a.enabled));
        WriteLine(os, base + ".saveTarget", BoolToStr(a.saveTarget));
        WriteLine(os, base + ".aimLock", BoolToStr(a.aimLock));
        WriteLine(os, base + ".visibilityCheck", BoolToStr(a.visibilityCheck));
        WriteLine(os, base + ".humanizedSmooth", BoolToStr(a.humanizedSmooth));
        WriteLine(os, base + ".weaponOnly", BoolToStr(a.weaponOnly));
        WriteLine(os, base + ".ignoreDowned", BoolToStr(a.ignoreDowned));
        WriteLine(os, base + ".predict", BoolToStr(a.predict));
        WriteLine(os, base + ".playerAi", BoolToStr(a.playerAi));
        WriteLine(os, base + ".perWeapon", BoolToStr(a.perWeapon));
        WriteLine(os, base + ".hitBox", a.hitBox);
        WriteLine(os, base + ".fov", a.fov);
        WriteLine(os, base + ".smooth", a.smooth);
        WriteLine(os, base + ".humanizedSmoothPercent", a.humanizedSmoothPercent);
        WriteLine(os, base + ".maxDistance", a.maxDistance);

        const auto& v = a.visuals;
        WriteLine(os, base + ".visuals.drawFov", BoolToStr(v.drawFov));
        WriteLine(os, base + ".visuals.drawFovFilled", BoolToStr(v.drawFovFilled));
        WriteLine(os, base + ".visuals.drawFovOutline", BoolToStr(v.drawFovOutline));
        WriteLine(os, base + ".visuals.drawFovRgb", BoolToStr(v.drawFovRgb));
        SerializeColor(os, base + ".visuals.fovColor", v.fovColor);
        WriteLine(os, base + ".visuals.drawCrosshair", BoolToStr(v.drawCrosshair));
        WriteLine(os, base + ".visuals.crosshairType", static_cast<int>(v.crosshairType));
        SerializeColor(os, base + ".visuals.crosshairColor", v.crosshairColor);
        WriteLine(os, base + ".visuals.drawTarget", BoolToStr(v.drawTarget));
        WriteLine(os, base + ".visuals.targetType", static_cast<int>(v.targetType));
        SerializeColor(os, base + ".visuals.targetColor", v.targetColor);
        WriteLine(os, base + ".visuals.drawThickness", v.drawThickness);
        WriteLine(os, base + ".visuals.drawSize", v.drawSize);
    }

    static void SerializeTriggerbotCore(std::ostream& os, const std::string& base, const TriggerbotCore& t) {
        WriteLine(os, base + ".enabled", BoolToStr(t.enabled));
        WriteLine(os, base + ".enableAllWeapons", BoolToStr(t.enableAllWeapons));
        WriteLine(os, base + ".enableOnlyShotguns", BoolToStr(t.enableOnlyShotguns));
        WriteLine(os, base + ".perWeapon", BoolToStr(t.perWeapon));
        WriteLine(os, base + ".delay", t.delay);
        WriteLine(os, base + ".maxDistance", t.maxDistance);
        WriteLine(os, base + ".hotkey", t.hotkey);
    }

    static void SerializePlayerESP(std::ostream& os, const std::string& base, const PlayerESPSettings& p) {
        WriteLine(os, base + ".enabled", BoolToStr(p.enabled));
        WriteLine(os, base + ".maxDistance", p.maxDistance);
        WriteLine(os, base + ".teamCheck", BoolToStr(p.teamCheck));
        WriteLine(os, base + ".playerAi", BoolToStr(p.playerAi));
        WriteLine(os, base + ".ignoreDowned", BoolToStr(p.ignoreDowned));
        SerializeBox(os, base + ".box", p.box);
        SerializeLine(os, base + ".lines", p.lines);
        SerializeSkeleton(os, base + ".skeleton", p.skeleton);
        SerializeCircle(os, base + ".headCircle", p.headCircle);
        SerializeText(os, base + ".info.nickname", p.info.nickname);
        SerializeText(os, base + ".info.distance", p.info.distance);
        SerializeText(os, base + ".info.platform", p.info.platform);
        SerializeText(os, base + ".info.kills", p.info.kills);
        SerializeText(os, base + ".info.level", p.info.level);
        SerializeText(os, base + ".info.rank", p.info.rank);
        SerializeText(os, base + ".info.weapon", p.info.weapon);
        WriteLine(os, base + ".info.weaponRarity", BoolToStr(p.info.weaponRarity));
        WriteLine(os, base + ".info.weaponAmmo", BoolToStr(p.info.weaponAmmo));
    }

    static void SerializeItemCategory(std::ostream& os, const std::string& base, const ItemCategory& c) {
        WriteLine(os, base + ".enabled", BoolToStr(c.enabled));
        SerializeText(os, base + ".display", c.display);
        WriteLine(os, base + ".maxDistance", c.maxDistance);
        // Items map
        for (const auto& kv : c.items) {
            WriteLine(os, base + ".items." + kv.first, BoolToStr(kv.second));
        }
    }

    static void SerializeItemESP(std::ostream& os, const std::string& base, const ItemESPSettings& s) {
        SerializeItemCategory(os, base + ".consumables", s.consumables);
        SerializeItemCategory(os, base + ".weapons", s.weapons);
        SerializeItemCategory(os, base + ".ammo", s.ammo);
        SerializeItemCategory(os, base + ".others", s.others);
        for (int i = 0; i < static_cast<int>(WeaponRarity::COUNT); ++i) {
            WriteLine(os, base + ".rarityFilter." + std::to_string(i), BoolToStr(s.rarityFilter[i]));
        }
    }

    static void SerializeRadar(std::ostream& os, const std::string& base, const RadarSettings& r) {
        WriteLine(os, base + ".enabled", BoolToStr(r.enabled));
        WriteLine(os, base + ".showDistance", BoolToStr(r.showDistance));
        WriteLine(os, base + ".positionX", r.positionX);
        WriteLine(os, base + ".positionY", r.positionY);
        WriteLine(os, base + ".circleSize", r.circleSize);
        WriteLine(os, base + ".rectangleSize", r.rectangleSize);
        WriteLine(os, base + ".type", static_cast<int>(r.type));
        WriteLine(os, base + ".useVisibleColor", BoolToStr(r.useVisibleColor));
        WriteLine(os, base + ".useClosestColor", BoolToStr(r.useClosestColor));
        WriteLine(os, base + ".useAimingAtMeColor", BoolToStr(r.useAimingAtMeColor));
        SerializeColor(os, base + ".visibleColor", r.visibleColor);
        SerializeColor(os, base + ".nonVisibleColor", r.nonVisibleColor);
        SerializeColor(os, base + ".knockedVisibleColor", r.knockedVisibleColor);
        SerializeColor(os, base + ".knockedNonVisibleColor", r.knockedNonVisibleColor);
        WriteLine(os, base + ".maxDistance", r.maxDistance);
        SerializeFont(os, base + ".distanceFont", r.distanceFont);
    }

    static void SerializeHotkeyBinding(std::ostream& os, const std::string& base, const HotkeyBinding& h) {
        WriteLine(os, base + ".key", h.key);
        WriteLine(os, base + ".isToggle", BoolToStr(h.isToggle));
    }
    static void SerializeHotkeys(std::ostream& os, const std::string& base, const HotkeySettings& hk) {
        const auto& g = hk.global;
        SerializeHotkeyBinding(os, base + ".global.holdTrigger", g.holdTrigger);
        SerializeHotkeyBinding(os, base + ".global.holdPrimary", g.holdPrimary);
        SerializeHotkeyBinding(os, base + ".global.holdSecondary", g.holdSecondary);
        SerializeHotkeyBinding(os, base + ".global.switchToHead", g.switchToHead);
        SerializeHotkeyBinding(os, base + ".global.toggle", g.toggle);
        SerializeHotkeyBinding(os, base + ".global.menu", g.menu);

        const auto& f = hk.features;
        SerializeHotkeyBinding(os, base + ".features.aimbotEnable", f.aimbotEnable);
        SerializeHotkeyBinding(os, base + ".features.aimbotAimLock", f.aimbotAimLock);
        SerializeHotkeyBinding(os, base + ".features.aimbotPrediction", f.aimbotPrediction);
        SerializeHotkeyBinding(os, base + ".features.aimbotSaveTarget", f.aimbotSaveTarget);
        SerializeHotkeyBinding(os, base + ".features.aimbotVisibilityCheck", f.aimbotVisibilityCheck);
        SerializeHotkeyBinding(os, base + ".features.aimbotHumanizedSmooth", f.aimbotHumanizedSmooth);
        SerializeHotkeyBinding(os, base + ".features.aimbotIgnoreDowned", f.aimbotIgnoreDowned);
        SerializeHotkeyBinding(os, base + ".features.aimbotPlayerAi", f.aimbotPlayerAi);
        SerializeHotkeyBinding(os, base + ".features.aimbotWeaponOnly", f.aimbotWeaponOnly);
        SerializeHotkeyBinding(os, base + ".features.aimbotDrawFov", f.aimbotDrawFov);
        SerializeHotkeyBinding(os, base + ".features.aimbotDrawCrosshair", f.aimbotDrawCrosshair);
        SerializeHotkeyBinding(os, base + ".features.aimbotDrawTarget", f.aimbotDrawTarget);
        SerializeHotkeyBinding(os, base + ".features.playerEspEnable", f.playerEspEnable);
        SerializeHotkeyBinding(os, base + ".features.playerEspBox", f.playerEspBox);
        SerializeHotkeyBinding(os, base + ".features.playerEspSkeleton", f.playerEspSkeleton);
        SerializeHotkeyBinding(os, base + ".features.playerEspHeadCircle", f.playerEspHeadCircle);
        SerializeHotkeyBinding(os, base + ".features.playerEspLines", f.playerEspLines);
        SerializeHotkeyBinding(os, base + ".features.playerEspDistance", f.playerEspDistance);
        SerializeHotkeyBinding(os, base + ".features.playerEspNickname", f.playerEspNickname);
        SerializeHotkeyBinding(os, base + ".features.playerEspPlatform", f.playerEspPlatform);
        SerializeHotkeyBinding(os, base + ".features.playerEspKills", f.playerEspKills);
        SerializeHotkeyBinding(os, base + ".features.playerEspLevel", f.playerEspLevel);
        SerializeHotkeyBinding(os, base + ".features.playerEspRank", f.playerEspRank);
        SerializeHotkeyBinding(os, base + ".features.playerEspIgnoreDowned", f.playerEspIgnoreDowned);
        SerializeHotkeyBinding(os, base + ".features.itemEspEnable", f.itemEspEnable);
        SerializeHotkeyBinding(os, base + ".features.itemConsumableEnable", f.itemConsumableEnable);
        SerializeHotkeyBinding(os, base + ".features.itemWeaponEnable", f.itemWeaponEnable);
        SerializeHotkeyBinding(os, base + ".features.itemAmmoEnable", f.itemAmmoEnable);
        SerializeHotkeyBinding(os, base + ".features.itemOtherEnable", f.itemOtherEnable);
        SerializeHotkeyBinding(os, base + ".features.radarEnable", f.radarEnable);
        SerializeHotkeyBinding(os, base + ".features.radarDistance", f.radarDistance);
        SerializeHotkeyBinding(os, base + ".features.radarVisibleColor", f.radarVisibleColor);
        SerializeHotkeyBinding(os, base + ".features.radarClosestColor", f.radarClosestColor);
        SerializeHotkeyBinding(os, base + ".features.radarAimingAtMeColor", f.radarAimingAtMeColor);
    }

    static void SerializeConfig(std::ostream& os, const std::string& base, const ConfigurationSettings& c) {
        WriteLine(os, base + ".currentConfig", c.currentConfig);
        WriteLine(os, base + ".previousConfig", c.previousConfig);
        WriteLine(os, base + ".selectedWeapon", c.selectedWeapon);
        WriteLine(os, base + ".gameMode", static_cast<int>(c.gameMode));
        WriteLine(os, base + ".language", static_cast<int>(c.language));
        WriteLine(os, base + ".showFeatureDefinitions", BoolToStr(c.showFeatureDefinitions));
        WriteLine(os, base + ".autoSave", BoolToStr(c.autoSave));
        WriteLine(os, base + ".autoSaveInterval", c.autoSaveInterval);
    }

    static void SerializeAll(std::ostream& os, const GameSettings& s) {
        // Aimbot general
        SerializeAimbotCore(os, "Aimbot.general", s.aimbot.general);
        // Aimbot per-weapon
        for (const auto& kv : s.aimbot.perWeaponSettings) {
            SerializeAimbotCore(os, std::string("Aimbot.perWeaponSettings.") + std::to_string(kv.first), kv.second);
        }
        // Triggerbot general
        SerializeTriggerbotCore(os, "Triggerbot.general", s.triggerbot.general);
        // Triggerbot per-weapon
        for (const auto& kv : s.triggerbot.perWeaponSettings) {
            SerializeTriggerbotCore(os, std::string("Triggerbot.perWeaponSettings.") + std::to_string(kv.first), kv.second);
        }
        // Player ESP
        SerializePlayerESP(os, "PlayerESP", s.playerESP);
        // Item ESP
        SerializeItemESP(os, "ItemESP", s.itemESP);
        // Radar
        SerializeRadar(os, "Radar", s.radar);
        // Hotkeys
        SerializeHotkeys(os, "Hotkeys", s.hotkeys);
        // Config
        SerializeConfig(os, "Config", s.config);
    }

    // Minimal parsing helpers
    static inline std::string Trim(const std::string& s) {
        size_t a = s.find_first_not_of(" \t\r\n");
        size_t b = s.find_last_not_of(" \t\r\n");
        if (a == std::string::npos) return "";
        return s.substr(a, b - a + 1);
    }

    // Setters for known key prefixes
    static bool ApplyKey(GameSettings& S, const std::string& key, const std::string& value) {
        // Aimbot general
        auto setAimbotCore = [&](AimbotCore& a, const std::string& k, const std::string& v) -> bool {
            if (k == ".enabled") a.enabled = StrToBool(v);
            else if (k == ".saveTarget") a.saveTarget = StrToBool(v);
            else if (k == ".aimLock") a.aimLock = StrToBool(v);
            else if (k == ".visibilityCheck") a.visibilityCheck = StrToBool(v);
            else if (k == ".humanizedSmooth") a.humanizedSmooth = StrToBool(v);
            else if (k == ".weaponOnly") a.weaponOnly = StrToBool(v);
            else if (k == ".ignoreDowned") a.ignoreDowned = StrToBool(v);
            else if (k == ".predict") a.predict = StrToBool(v);
            else if (k == ".playerAi") a.playerAi = StrToBool(v);
            else if (k == ".perWeapon") a.perWeapon = StrToBool(v);
            else if (k == ".hitBox") a.hitBox = std::stoi(v);
            else if (k == ".fov") a.fov = std::stof(v);
            else if (k == ".smooth") a.smooth = std::stof(v);
            else if (k == ".humanizedSmoothPercent") a.humanizedSmoothPercent = std::stof(v);
            else if (k == ".maxDistance") a.maxDistance = std::stoi(v);
            else if (k.rfind(".visuals.", 0) == 0) {
                auto sk = k.substr(std::string(".visuals").size());
                if (sk == ".drawFov") a.visuals.drawFov = StrToBool(v);
                else if (sk == ".drawFovFilled") a.visuals.drawFovFilled = StrToBool(v);
                else if (sk == ".drawFovOutline") a.visuals.drawFovOutline = StrToBool(v);
                else if (sk == ".drawFovRgb") a.visuals.drawFovRgb = StrToBool(v);
                else if (sk == ".crosshairType") a.visuals.crosshairType = static_cast<CrosshairType>(std::stoi(v));
                else if (sk == ".drawCrosshair") a.visuals.drawCrosshair = StrToBool(v);
                else if (sk == ".targetType") a.visuals.targetType = static_cast<TargetIndicatorType>(std::stoi(v));
                else if (sk == ".drawThickness") a.visuals.drawThickness = std::stof(v);
                else if (sk == ".drawSize") a.visuals.drawSize = std::stof(v);
                // Colors are omitted in parsing for brevity; they default or are set via UI
                else return false;
            } else return false;
            return true;
        };

        if (key.rfind("Aimbot.general", 0) == 0) {
            return setAimbotCore(S.aimbot.general, key.substr(std::string("Aimbot.general").size()), value);
        }
        if (key.rfind("Aimbot.perWeaponSettings.", 0) == 0) {
            // Format: Aimbot.perWeaponSettings.<id>.<field>
            auto rest = key.substr(std::string("Aimbot.perWeaponSettings.").size());
            auto dot = rest.find('.');
            if (dot == std::string::npos) return false;
            int id = std::stoi(rest.substr(0, dot));
            std::string sub = rest.substr(dot);
            AimbotCore& ac = SettingsManager::GetWeaponSetting(S.aimbot.perWeaponSettings, id, S.aimbot.general);
            return setAimbotCore(ac, sub, value);
        }

        if (key.rfind("Triggerbot.general", 0) == 0) {
            auto& t = S.triggerbot.general;
            std::string k = key.substr(std::string("Triggerbot.general").size());
            if (k == ".enabled") t.enabled = StrToBool(value);
            else if (k == ".enableAllWeapons") t.enableAllWeapons = StrToBool(value);
            else if (k == ".enableOnlyShotguns") t.enableOnlyShotguns = StrToBool(value);
            else if (k == ".perWeapon") t.perWeapon = StrToBool(value);
            else if (k == ".delay") t.delay = std::stoi(value);
            else if (k == ".maxDistance") t.maxDistance = std::stof(value);
            else if (k == ".hotkey") t.hotkey = std::stoi(value);
            else return false;
            return true;
        }
        if (key.rfind("Triggerbot.perWeaponSettings.", 0) == 0) {
            auto rest = key.substr(std::string("Triggerbot.perWeaponSettings.").size());
            auto dot = rest.find('.');
            if (dot == std::string::npos) return false;
            int id = std::stoi(rest.substr(0, dot));
            std::string sub = rest.substr(dot);
            TriggerbotCore& tc = SettingsManager::GetWeaponSetting(S.triggerbot.perWeaponSettings, id, S.triggerbot.general);
            if (sub == ".enabled") tc.enabled = StrToBool(value);
            else if (sub == ".enableAllWeapons") tc.enableAllWeapons = StrToBool(value);
            else if (sub == ".enableOnlyShotguns") tc.enableOnlyShotguns = StrToBool(value);
            else if (sub == ".perWeapon") tc.perWeapon = StrToBool(value);
            else if (sub == ".delay") tc.delay = std::stoi(value);
            else if (sub == ".maxDistance") tc.maxDistance = std::stof(value);
            else if (sub == ".hotkey") tc.hotkey = std::stoi(value);
            else return false;
            return true;
        }

        if (key.rfind("PlayerESP.", 0) == 0) {
            auto& p = S.playerESP;
            std::string k = key.substr(std::string("PlayerESP").size());
            if (k == ".enabled") p.enabled = StrToBool(value);
            else if (k == ".maxDistance") p.maxDistance = std::stoi(value);
            else if (k == ".teamCheck") p.teamCheck = StrToBool(value);
            else if (k == ".playerAi") p.playerAi = StrToBool(value);
            else if (k == ".ignoreDowned") p.ignoreDowned = StrToBool(value);
            // visuals parsing omitted for brevity
            else return false;
            return true;
        }

        if (key.rfind("ItemESP.", 0) == 0) {
            auto catApply = [&](ItemCategory& c, const std::string& subKey) -> bool {
                if (subKey == ".enabled") { c.enabled = StrToBool(value); return true; }
                else if (subKey == ".maxDistance") { c.maxDistance = std::stoi(value); return true; }
                else if (subKey.rfind(".items.", 0) == 0) {
                    c.items[subKey.substr(7)] = StrToBool(value); return true;
                }
                return false;
            };
            auto rest = key.substr(std::string("ItemESP.").size());
            if (rest.rfind("consumables", 0) == 0) return catApply(S.itemESP.consumables, rest.substr(std::string("consumables").size()));
            if (rest.rfind("weapons", 0) == 0) return catApply(S.itemESP.weapons, rest.substr(std::string("weapons").size()));
            if (rest.rfind("ammo", 0) == 0) return catApply(S.itemESP.ammo, rest.substr(std::string("ammo").size()));
            if (rest.rfind("others", 0) == 0) return catApply(S.itemESP.others, rest.substr(std::string("others").size()));
            if (rest.rfind("rarityFilter.", 0) == 0) {
                int idx = std::stoi(rest.substr(std::string("rarityFilter.").size()));
                if (idx >= 0 && idx < static_cast<int>(WeaponRarity::COUNT)) S.itemESP.rarityFilter[idx] = StrToBool(value);
                return true;
            }
        return false;
        }

        if (key.rfind("Radar.", 0) == 0) {
            auto& r = S.radar;
            std::string k = key.substr(std::string("Radar").size());
            if (k == ".enabled") r.enabled = StrToBool(value);
            else if (k == ".showDistance") r.showDistance = StrToBool(value);
            else if (k == ".positionX") r.positionX = std::stoi(value);
            else if (k == ".positionY") r.positionY = std::stoi(value);
            else if (k == ".circleSize") r.circleSize = std::stoi(value);
            else if (k == ".rectangleSize") r.rectangleSize = std::stoi(value);
            else if (k == ".type") r.type = static_cast<RadarType>(std::stoi(value));
            else if (k == ".useVisibleColor") r.useVisibleColor = StrToBool(value);
            else if (k == ".useClosestColor") r.useClosestColor = StrToBool(value);
            else if (k == ".useAimingAtMeColor") r.useAimingAtMeColor = StrToBool(value);
            else if (k == ".maxDistance") r.maxDistance = std::stoi(value);
            else return false;
            return true;
        }

        if (key.rfind("Hotkeys.", 0) == 0) {
            auto setHK = [&](HotkeyBinding& h, const std::string& sub) -> bool {
                if (sub == ".key") h.key = std::stoi(value);
                else if (sub == ".isToggle") h.isToggle = StrToBool(value);
                else return false; return true;
            };
            auto rest = key.substr(std::string("Hotkeys.").size());
            auto applyGroup = [&](const std::string& group, auto&& binder) -> bool {
                if (rest.rfind(group + '.', 0) != 0) return false;
                auto leaf = rest.substr(group.size() + 1);
                return binder(leaf);
            };
            if (applyGroup("global.holdTrigger", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.global.holdTrigger, "." + leaf); })) return true;
            if (applyGroup("global.holdPrimary", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.global.holdPrimary, "." + leaf); })) return true;
            if (applyGroup("global.holdSecondary", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.global.holdSecondary, "." + leaf); })) return true;
            if (applyGroup("global.switchToHead", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.global.switchToHead, "." + leaf); })) return true;
            if (applyGroup("global.toggle", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.global.toggle, "." + leaf); })) return true;
            if (applyGroup("global.menu", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.global.menu, "." + leaf); })) return true;
            // Features (a subset for brevity)
            if (applyGroup("features.aimbotEnable", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.features.aimbotEnable, "." + leaf); })) return true;
            if (applyGroup("features.playerEspEnable", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.features.playerEspEnable, "." + leaf); })) return true;
            if (applyGroup("features.itemEspEnable", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.features.itemEspEnable, "." + leaf); })) return true;
            if (applyGroup("features.radarEnable", [&](const std::string& leaf){ return setHK(GAME_SETTINGS.hotkeys.features.radarEnable, "." + leaf); })) return true;
            return true;
        }

        if (key.rfind("Config.", 0) == 0) {
            auto& c = S.config;
            std::string k = key.substr(std::string("Config").size());
            if (k == ".currentConfig") c.currentConfig = std::stoi(value);
            else if (k == ".previousConfig") c.previousConfig = std::stoi(value);
            else if (k == ".selectedWeapon") c.selectedWeapon = std::stoi(value);
            else if (k == ".gameMode") c.gameMode = static_cast<GameMode>(std::stoi(value));
            else if (k == ".language") c.language = static_cast<Language>(std::stoi(value));
            else if (k == ".showFeatureDefinitions") c.showFeatureDefinitions = StrToBool(value);
            else if (k == ".autoSave") c.autoSave = StrToBool(value);
            else if (k == ".autoSaveInterval") c.autoSaveInterval = std::stoi(value);
            else return false;
            return true;
        }

        return false;
    }

    static bool DeserializeAll(std::istream& is, GameSettings& S) {
        std::string line;
        while (std::getline(is, line)) {
            line = Trim(line);
            if (line.empty() || line[0] == ';' || line[0] == '#') continue;
            auto pos = line.find('=');
            if (pos == std::string::npos) continue;
            std::string key = Trim(line.substr(0, pos));
            std::string val = Trim(line.substr(pos + 1));
            ApplyKey(S, key, val);
        }
        return true;
    }
}

bool SaveConfiguration(const std::string& filename) {
    const std::string fullPath = ToFullPath(filename);
    try {
        std::ofstream ofs(fullPath, std::ios::trunc);
        if (!ofs.is_open()) return false;
        ConfigIO::SerializeAll(ofs, GAME_SETTINGS);
        ofs.flush();
        RefreshConfigurations();
        return true;
    } catch (...) {
        return false;
    }
}

bool LoadConfiguration(const std::string& filename) {
    const std::string fullPath = ToFullPath(filename);
    try {
        std::ifstream ifs(fullPath);
        if (!ifs.is_open()) return false;
        ConfigIO::DeserializeAll(ifs, GAME_SETTINGS);
        SettingsHelper::SyncSettings();
        g_ConfigSnapshot.CaptureSettings();
        return true;
    } catch (...) {
        return false;
    }
}

bool DeleteConfiguration(const std::string& filename) {
    try {
        if (filename == "Default.cfg") return false;
        const std::string filepath = ToFullPath(filename);
        if (!std::filesystem::exists(filepath)) return false;
        std::filesystem::remove(filepath);
        RefreshConfigurations();

        bool needsNewSelection = true;
        for (int i = 0; i < (int)g_ConfigSystem.configs.size(); i++) {
            if (g_ConfigSystem.configs[i].isSelected) { needsNewSelection = false; break; }
        }
        if (needsNewSelection) {
            SelectDefaultConfiguration();
        }
        return true;
    } catch (...) {
        return false;
    }
}

bool CreateConfiguration(const std::string& name, const std::string& type) {
    try {
        if (name.empty()) return false;
        std::string safeName = name;
        std::replace_if(safeName.begin(), safeName.end(),
            [](char c) { return c == '<' || c == '>' || c == ':' || c == '"' || c == '/' || c == '\\' || c == '|' || c == '?' || c == '*'; }, '_');
        if (safeName.empty()) return false;

        std::string filename = safeName + ".cfg";
        for (const auto& config : g_ConfigSystem.configs) {
            if (config.filename == filename) {
                return false;
            }
        }

        if (!SaveConfiguration(filename)) {
            return false;
        }

        ConfigFile newConfig;
        newConfig.name = safeName;
        newConfig.type = type;
        newConfig.filename = filename;
        newConfig.isSelected = true;
        newConfig.isDefault = false;
        newConfig.isCloudConfig = false;
        newConfig.lastModified = std::chrono::system_clock::now();

        for (auto& c : g_ConfigSystem.configs) c.isSelected = false;
        g_ConfigSystem.configs.push_back(newConfig);

        try {
            std::sort(g_ConfigSystem.configs.begin(), g_ConfigSystem.configs.end(),
                [](const ConfigFile& a, const ConfigFile& b) {
                    if (a.isDefault) return true;
                    if (b.isDefault) return false;
                    return a.lastModified > b.lastModified;
                });
        } catch (...) {}

        g_ConfigSystem.selectedConfigIndex = -1;
        for (int i = 0; i < (int)g_ConfigSystem.configs.size(); i++) {
            if (g_ConfigSystem.configs[i].filename == filename) {
                g_ConfigSystem.selectedConfigIndex = i;
                g_ConfigSystem.configs[i].isSelected = true;
                break;
            }
        }
        if (g_ConfigSystem.selectedConfigIndex == -1 && !g_ConfigSystem.configs.empty()) {
                g_ConfigSystem.selectedConfigIndex = 0;
                g_ConfigSystem.configs[0].isSelected = true;
        }

        RefreshConfigurations();
        return true;
    } catch (...) {
        return false;
    }
}

bool ShareConfiguration(const std::string& name, const std::string& type) {
    std::string sharedName = "Shared_" + name;
    ConfigFile sharedConfig;
    sharedConfig.name = sharedName;
    sharedConfig.type = type;
    sharedConfig.filename = sharedName + ".cfg";
    sharedConfig.isDefault = false;
    sharedConfig.isSelected = false;
    sharedConfig.isDownloaded = false;
    sharedConfig.isCloudConfig = true;
    sharedConfig.lastModified = std::chrono::system_clock::now();

    g_ConfigSystem.configs.push_back(sharedConfig);
    return SaveConfiguration(sharedConfig.filename);
}

void SelectConfiguration(int index) {
    if (index < 0 || index >= (int)g_ConfigSystem.configs.size()) return;
    for (auto& c : g_ConfigSystem.configs) c.isSelected = false;
    g_ConfigSystem.configs[index].isSelected = true;
    g_ConfigSystem.selectedConfigIndex = index;
    LoadConfiguration(g_ConfigSystem.configs[index].filename);
}

void SelectDefaultConfiguration() {
    for (int i = 0; i < (int)g_ConfigSystem.configs.size(); i++) {
        if (g_ConfigSystem.configs[i].isDefault) {
            SelectConfiguration(i);
            return;
        }
    }
    if (!g_ConfigSystem.configs.empty()) {
        SelectConfiguration(0);
    }
}

void InitializeConfigSystem() {
        if (!std::filesystem::exists(g_ConfigSystem.configFolder)) {
            std::filesystem::create_directories(g_ConfigSystem.configFolder);
        }
    RefreshConfigurations();

    for (auto& config : g_ConfigSystem.configs) {
        if (config.isDefault) {
            config.isDownloaded = true;
            config.isCloudConfig = false;
        } else if (config.filename.find("Shared_") == std::string::npos) {
            config.isDownloaded = true;
            config.isCloudConfig = false;
        } else {
            config.isDownloaded = false;
            config.isCloudConfig = true;
        }
    }

    if (g_ConfigSystem.selectedConfigIndex == -1) {
        SelectDefaultConfiguration();
    }
}

void RefreshConfigurations() {
    g_ConfigSystem.configs.clear();

    const std::string defaultConfigPath = g_ConfigSystem.configFolder + std::string("Default.cfg");
    bool defaultExists = false;

    try {
        if (!std::filesystem::exists(g_ConfigSystem.configFolder)) {
            std::filesystem::create_directories(g_ConfigSystem.configFolder);
        }

        for (const auto& entry : std::filesystem::directory_iterator(g_ConfigSystem.configFolder)) {
            if (entry.is_regular_file() && entry.path().extension() == ".cfg") {
                std::string filename = entry.path().filename().string();
                std::string name = filename.substr(0, filename.size() - 4);
                bool isDefault = (filename == "Default.cfg");
                if (isDefault) defaultExists = true;

                ConfigFile config;
                config.name = name;
                config.type = isDefault ? "Default" : "Custom";
                config.filename = filename;
                config.isSelected = false;
                config.isDefault = isDefault;
                config.isCloudConfig = false;

                auto fsTime = entry.last_write_time();
                auto fsTimePoint = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    fsTime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
                config.lastModified = fsTimePoint;

                g_ConfigSystem.configs.push_back(config);
            }
        }

        if (!defaultExists) {
            SaveConfiguration("Default.cfg");

            ConfigFile defaultConfig;
            defaultConfig.name = "Default";
            defaultConfig.type = "Default";
            defaultConfig.filename = "Default.cfg";
            defaultConfig.isSelected = true;
            defaultConfig.isDefault = true;
            defaultConfig.isCloudConfig = false;
            defaultConfig.lastModified = std::chrono::system_clock::now();
            g_ConfigSystem.configs.push_back(defaultConfig);
        }

        std::sort(g_ConfigSystem.configs.begin(), g_ConfigSystem.configs.end(),
            [](const ConfigFile& a, const ConfigFile& b) {
                if (a.isDefault) return true;
                if (b.isDefault) return false;
                return a.lastModified > b.lastModified;
            });
    } catch (...) {
        // Swallow and keep the list empty if something goes wrong
    }
}