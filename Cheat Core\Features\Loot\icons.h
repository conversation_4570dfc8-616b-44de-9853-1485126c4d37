#ifndef ICONS_H
#define ICONS_H

#include <d3d11.h>
#include <string>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../../Menu UI/ImGui/imgui.h"
#include "../../../Menu UI/ImGui/imgui_internal.h"
#include "../../Framwork/Vectors.h"
//Consumable


extern ID3D11ShaderResourceView* chestTexture;
extern ID3D11ShaderResourceView* sobigshieldTexture;
extern ID3D11ShaderResourceView* ChugSplashTexture;
extern ID3D11ShaderResourceView* medkitTexture;
extern ID3D11ShaderResourceView* FlowBerry_FizzTexture;
extern ID3D11ShaderResourceView* Small_ShieldTexture;
extern ID3D11ShaderResourceView* Nitro_SplashTexture;
extern ID3D11ShaderResourceView* BandagesTexture;
extern ID3D11ShaderResourceView* NukaColaTexture;

//Consumable

extern ID3D11ShaderResourceView* Light_BulletsTexture;
extern ID3D11ShaderResourceView* Medium_BulletsTexture;
extern ID3D11ShaderResourceView* Heavy_BulletsTexture;
extern ID3D11ShaderResourceView* ShellsTexture;
extern ID3D11ShaderResourceView* RocketsTexture;
//Ammo

//Weapons

extern ID3D11ShaderResourceView* Boom_BoltTexture;
extern ID3D11ShaderResourceView* Combat_Assault_RifleTexture;
extern ID3D11ShaderResourceView* Combat_ShotgunTexture;
extern ID3D11ShaderResourceView* Enforcer_ARTexture;
extern ID3D11ShaderResourceView* Frenzy_Auto_ShotgunTexture;
extern ID3D11ShaderResourceView* Hand_CannonTexture;
extern ID3D11ShaderResourceView* Hammer_Pump_ShotgunTexture;
extern ID3D11ShaderResourceView* Gatekeeper_ShotgunTexture;
extern ID3D11ShaderResourceView* Nitro_FistsTexture;
extern ID3D11ShaderResourceView* Huntress_DMRTexture;
extern ID3D11ShaderResourceView* Harbinger_SMGTexture;
extern ID3D11ShaderResourceView* Tactical_Assault_RifleTexture;
extern ID3D11ShaderResourceView* Shockwave_grenadeTexture;
extern ID3D11ShaderResourceView* Ranger_PistolTexture;
extern ID3D11ShaderResourceView* Warforged_Assault_RifleTexture;
extern ID3D11ShaderResourceView* Thunder_Burst_SMGTexture;
extern ID3D11ShaderResourceView* HeavyImpactSniperRifleTexture;

//RELOAD
extern ID3D11ShaderResourceView* MKSevenAssaultRifleTexture;
extern ID3D11ShaderResourceView* OGPump_ShotgunTexture;
extern ID3D11ShaderResourceView* NewPump_ShotgunTexture;
extern ID3D11ShaderResourceView* ChugJugTexture;
extern ID3D11ShaderResourceView* BurstARTexture;
extern ID3D11ShaderResourceView* MidasDrumGunTexture;
extern ID3D11ShaderResourceView* SkyesARTexture;
extern ID3D11ShaderResourceView* SlonesBurstARTexture ;
extern ID3D11ShaderResourceView* HeistedBreacherShotgunTexture ;
extern ID3D11ShaderResourceView* HeistedAccelerantShotgunTexture ;
extern ID3D11ShaderResourceView* HeistedExplosiveARTexture ;
extern ID3D11ShaderResourceView* HeistedBlinkMagSMGTexture ;
extern ID3D11ShaderResourceView* HeistedRunGunSMGTexture ;
extern ID3D11ShaderResourceView* TacticalShotgunTexture ;
extern ID3D11ShaderResourceView* LeverActionShotgunTexture ;
extern ID3D11ShaderResourceView* HeavyShotgunTexture ;
extern ID3D11ShaderResourceView* RangerShotgunTexture ;
extern ID3D11ShaderResourceView* AssaultRifleTexture ;
extern ID3D11ShaderResourceView* ScarAssaultRifleTexture ;
extern ID3D11ShaderResourceView* HammerAssaultRifleTexture ;
extern ID3D11ShaderResourceView* HeavyAssaultRifleTexture ;
extern ID3D11ShaderResourceView* InfantryRifleTexture ;
extern ID3D11ShaderResourceView* SubmachineGunTexture ;
extern ID3D11ShaderResourceView* TacticalSubmachineGunTexture ;
extern ID3D11ShaderResourceView* StingerSMGTexture ;
extern ID3D11ShaderResourceView* BoltActionSniperRifleTexture ;
extern ID3D11ShaderResourceView* HuntingRifleTexture ;
extern ID3D11ShaderResourceView* PistolTexture ;
extern ID3D11ShaderResourceView* RevolverTexture ;
extern ID3D11ShaderResourceView* GrapplerTexture ;
extern ID3D11ShaderResourceView* RocketLauncherTexture ;
extern ID3D11ShaderResourceView* CrashPadTexture ;

extern ID3D11ShaderResourceView* Striker_Burst_RifleTexture ;
extern ID3D11ShaderResourceView* Striker_ARTexture ;
extern ID3D11ShaderResourceView* Sovereign_ShotgunTexture ;
extern ID3D11ShaderResourceView* Monarch_PistolTexture ;
extern ID3D11ShaderResourceView* Hyper_SMGTexture ;
extern ID3D11ShaderResourceView* Firefly_JarTexture ;
extern ID3D11ShaderResourceView* Dual_Micro_SMGsTexture ;
extern ID3D11ShaderResourceView* Captain_AmericaShieldWeaponTexture ;


extern ID3D11ShaderResourceView* DualPistolsTexture  ;
extern ID3D11ShaderResourceView* RapidFireSMGTexture  ;
extern ID3D11ShaderResourceView* SuppressedSMGTexture  ;
extern ID3D11ShaderResourceView* SuppressedAssaultRifleTexture  ;
extern ID3D11ShaderResourceView* HeavySniperRifleTexture  ;
extern ID3D11ShaderResourceView* SemiAutomaticSniperRifleTexture  ;
extern ID3D11ShaderResourceView* GrenadeLauncherTexture  ;
extern ID3D11ShaderResourceView* RemoteExplosivesTexture  ;
extern ID3D11ShaderResourceView* RegularGrenadesTexture  ;
extern ID3D11ShaderResourceView* StinkBombTexture  ;
extern ID3D11ShaderResourceView* BandageBazookaTexture  ;
extern ID3D11ShaderResourceView* BoogieBombTexture  ;
extern ID3D11ShaderResourceView* ClingersTexture;

extern ID3D11ShaderResourceView* HoloTwisterAssaultRifleTexture;
extern ID3D11ShaderResourceView* FuryAssaultRifleTexture;
extern ID3D11ShaderResourceView* VeiledPrecisionSMGTexture;
extern ID3D11ShaderResourceView* SentinelPumpTexture;
extern ID3D11ShaderResourceView* TwinfireAutoShotgunTexture;
extern ID3D11ShaderResourceView* OniShotgunTexture;
extern ID3D11ShaderResourceView* SurgefireSMGTexture;

extern ID3D11ShaderResourceView* iosIconTexture;
extern ID3D11ShaderResourceView* linuxIconTexture;
extern ID3D11ShaderResourceView* androidIconTexture;
extern ID3D11ShaderResourceView* switchIconTexture;
extern ID3D11ShaderResourceView* pcIconTexture;
extern ID3D11ShaderResourceView* playstationIconTexture;
extern ID3D11ShaderResourceView* xboxIconTexture;


extern ID3D11ShaderResourceView* BronzeIIconTexture;
extern ID3D11ShaderResourceView* BronzeIIIconTexture;
extern ID3D11ShaderResourceView* BronzeIIIIconTexture;
extern ID3D11ShaderResourceView* SilverIIconTexture;
extern ID3D11ShaderResourceView* SilverIIIpcIconTexture;
extern ID3D11ShaderResourceView* SilverIIIIIconTexture;
extern ID3D11ShaderResourceView* GoldIIconTexture;
extern ID3D11ShaderResourceView* GoldIIIconTexture;
extern ID3D11ShaderResourceView* GoldIIIIconTexture;
extern ID3D11ShaderResourceView* PlatinumIIconTexture;
extern ID3D11ShaderResourceView* PlatinumIIIconTexture;
extern ID3D11ShaderResourceView* PlatinumIIIIconTexture;

extern ID3D11ShaderResourceView* DiamondIIconTexture;
extern ID3D11ShaderResourceView* DiamondIIIconTexture;
extern ID3D11ShaderResourceView* DiamondIIIIconTexture;

extern ID3D11ShaderResourceView* EliteIconTexture;
extern ID3D11ShaderResourceView* ChampionIconTexture;
extern ID3D11ShaderResourceView* UnrealIconTexture;

extern ID3D11ShaderResourceView* UnRankedIconTexture;

extern ID3D11ShaderResourceView* KillsIconTexture;
extern ID3D11ShaderResourceView* LevelIconTexture;

//Weapons

void LoadIconFromBytes(ID3D11Device* device, const unsigned char* bytes, size_t bytesSize, ID3D11ShaderResourceView** texture);
void DrawItemIconWithCircle(ImVec2 position, ID3D11ShaderResourceView* iconTexture, ImColor borderColor, ImColor gradientStartColor, ImColor gradientEndColor, float radius);
void DrawItemIconWithCircleCostom(ImVec2 position, ID3D11ShaderResourceView* iconTexture, ImColor borderColor, ImColor gradientStartColor, ImColor gradientEndColor, float radius);
void DrawItemIconWithCirclePlayer(ImVec2 position, ID3D11ShaderResourceView* iconTexture, ImColor borderColor, ImColor gradientStartColor, ImColor gradientEndColor, float radius, const std::string& text, ImColor textColor);

ID3D11ShaderResourceView* GetItemTexture(const std::string& ItemName);

void DrawItemTextAndIcon(Vector2 ScreenLocation, const std::string& ItemText, ImColor outlineColor, ImColor textColor, float fontSize, bool iconsEnabled, const std::string& ItemName, float iconSize);
void AddRadialGradient(ImDrawList* draw_list, const ImVec2& center, float radius, ImU32 col_in, ImU32 col_out);

#endif // ICONS_H
