#pragma once
#include <string>
#include "../../../Framwork/Vectors.h"
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../../../Menu UI/ImGui/imgui.h"
#include "../../../GameClass/GameSettings.h"
#include "../../../GameClass/GameFunctions.h"

class Drawing {
public:
	// Box drawing functions
	static void DrawBox(int x, int y, int w, int h, ImColor color, float width, float rounding, ImColor fillcolor);
	static void DrawCornerBox(int X, int Y, int W, int H, const ImColor color, int thickness, ImColor fillcolor);
	
	// Player specific drawing functions
	static void DrawHeadCircle(uintptr_t mesh, ImU32 visible_color);
	static void DrawSkeleton(uintptr_t Mesh, ImColor SkeleColor);
	static void DrawWeaponText(std::string weaponName, const Vector2& Bottom2D, ImColor pickcolor);
	static void DrawDistanceText(float distance, const Vector2& Bottom2D);
	static void DrawEspText(uintptr_t PlayerStates, uintptr_t Pawn, const Vector2& Head2D);
	static void DrawEspRankandLevel(uintptr_t PlayerStates, uintptr_t Pawn, const Vector2& Head2D);
	
	// Crosshair and FOV drawing
	static void DrawCrosshair(const ImVec2& center);
	static void DrawFovCircle(float r, bool filled, bool rainbow, bool toMouse);
	
	// Radar functions
	static void DrawRadar(float x, float y, float CirleSize, float RectangleSize);
	static void AddPlayerToRadar(Vector3 WorldLocation, float distance, ImColor radar_visible_color);
	
	// Helper function
	static void DrawLine(ImVec2 a, ImVec2 b, ImColor color, float width);
	static void DrawOutlinedText(const std::string& text, const ImVec2& pos, ImColor textColor, ImColor outlineColor);
};

// Helper functions for name conversion
std::string WStringToUTF8(const std::wstring& wstr);
std::string DecryptPlayerName(uintptr_t PlayerState, uintptr_t Actor);
std::string GetPlatformText(uintptr_t PlayerState);
ImColor GetColorByPlatform(const std::string& platform);
std::string GetRankByProgress(uint32_t rank);
int GetPlayerKills(uintptr_t PlayerState);
int GetPlayerLevel(uintptr_t PlayerState);
