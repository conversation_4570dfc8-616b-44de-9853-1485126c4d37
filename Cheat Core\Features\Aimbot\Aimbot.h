#pragma once
#include <string>
#include <vector>
#include "../../Framwork/Vectors.h"
#include "WeaponTypes.h"

// Moved these variables to GlobalVariables.h/cpp

class AimbotSpace {
public:    
    // Main aimbot functions
    static bool bIsInRectangle(double centerX, double centerY, double radius, double x, double y);
    
    static bool GetProjectileProperties(const std::wstring& WeaponName, float& ProjectileSpeed, float& ProjectileGravityScale, float distance);
    static WeaponTypes GetWeaponType(const std::wstring& weaponName);

    static void PerformAimbot(Vector3& Head, uintptr_t Mesh, const Vector3& Velocity, uintptr_t targeted_fort_pawn, uintptr_t AcknowlegedPawn, uintptr_t camera, std::wstring WeaponName, uintptr_t CurrentWeapon);
    static void HandleTriggerbot(const std::wstring& WeaponName, float distance, uintptr_t PlayerPawn);
    static void ResetLeftClick();
    static void PressLeftClick();
    static void HandleAimbot(uintptr_t Player, uintptr_t Mesh, const Vector3& HeadPosition, uintptr_t Targeted_fort_pawn, uintptr_t AcknowlegedPawn, uintptr_t PlayerState);
    static void ProcessTarget(uintptr_t target, uintptr_t targeted_fort_pawn, uintptr_t AcknowlegedPawn, uintptr_t camera);
    static void ProcessAimbot();
};

// Global variables for target tracking
extern uintptr_t g_CurrentTarget;
extern float g_ClosestDistance;
extern uintptr_t g_Targeted_fort_pawn;
extern uintptr_t g_AcknowlegedPawn;
