#include "SignatureScanner.h"
#include <iostream>
#include <sstream>
#include <iomanip>

namespace SignatureScanner {

    bool ParseSignature(const std::string& signature, std::vector<uint8_t>& bytes, std::string& mask) {
        bytes.clear();
        mask.clear();
        
        std::istringstream stream(signature);
        std::string token;
        
        while (stream >> token) {
            if (token == "?") {
                bytes.push_back(0x00);
                mask += "?";
            } else {
                try {
                    uint8_t byte = static_cast<uint8_t>(std::stoul(token, nullptr, 16));
                    bytes.push_back(byte);
                    mask += "x";
                } catch (const std::exception&) {
                    return false;
                }
            }
        }
        
        return !bytes.empty();
    }

    bool CompareBytes(const uint8_t* data, const std::vector<uint8_t>& bytes, const std::string& mask) {
        for (size_t i = 0; i < bytes.size(); ++i) {
            if (mask[i] == 'x' && data[i] != bytes[i]) {
                return false;
            }
        }
        return true;
    }

    uintptr_t FindPattern(uintptr_t baseAddress, size_t scanSize, const std::string& signature) {
        std::vector<uint8_t> bytes;
        std::string mask;
        
        if (!ParseSignature(signature, bytes, mask)) {
            return 0;
        }
        
        if (bytes.empty()) {
            return 0;
        }
        
        // Allocate buffer for reading memory
        std::vector<uint8_t> buffer(scanSize);
        
        // Read memory chunk by chunk to avoid large allocations
        const size_t chunkSize = 0x10000; // 64KB chunks
        const size_t patternSize = bytes.size();
        
        for (size_t offset = 0; offset < scanSize; offset += chunkSize) {
            size_t currentChunkSize = (std::min)(chunkSize, scanSize - offset);
            
            // Ensure we don't miss patterns that span chunk boundaries
            if (offset > 0 && currentChunkSize > patternSize) {
                offset -= patternSize - 1;
                currentChunkSize += patternSize - 1;
            }
            
            if (!UseDriver::ReadString(baseAddress + offset, buffer.data(), currentChunkSize)) {
                continue;
            }
            
            // Search for pattern in current chunk
            for (size_t i = 0; i <= currentChunkSize - patternSize; ++i) {
                if (CompareBytes(buffer.data() + i, bytes, mask)) {
                    return baseAddress + offset + i;
                }
            }
        }
        
        return 0;
    }

    uintptr_t FindPatternInModule(uintptr_t moduleBase, const std::string& signature) {
        if (!moduleBase) {
            return 0;
        }
        
        // Read DOS header
        IMAGE_DOS_HEADER dosHeader;
        if (!UseDriver::ReadString(moduleBase, &dosHeader, sizeof(dosHeader))) {
            return 0;
        }
        
        if (dosHeader.e_magic != IMAGE_DOS_SIGNATURE) {
            return 0;
        }
        
        // Read NT headers
        IMAGE_NT_HEADERS ntHeaders;
        uintptr_t ntHeadersAddress = moduleBase + dosHeader.e_lfanew;
        if (!UseDriver::ReadString(ntHeadersAddress, &ntHeaders, sizeof(ntHeaders))) {
            return 0;
        }
        
        if (ntHeaders.Signature != IMAGE_NT_SIGNATURE) {
            return 0;
        }
        
        // Scan the entire module
        size_t moduleSize = ntHeaders.OptionalHeader.SizeOfImage;
        return FindPattern(moduleBase, moduleSize, signature);
    }

    uintptr_t ResolveRipRelative(uintptr_t signatureAddress, int instructionOffset, int instructionSize) {
        // Read the 32-bit displacement
        int32_t displacement;
        if (!UseDriver::ReadString(signatureAddress + instructionOffset, &displacement, sizeof(displacement))) {
            return 0;
        }
        
        // Calculate RIP-relative address
        // RIP = address of next instruction = signatureAddress + instructionSize
        uintptr_t ripAddress = signatureAddress + instructionSize;
        return ripAddress + displacement;
    }

    uintptr_t FindUWorldOffset(uintptr_t baseAddress) {
        const std::string uWorldSignature = "48 8B 3D ? ? ? ? 4A 85 FF 74 ? 4A 8B CF";
        
        // Find the signature in the main module
        uintptr_t signatureAddress = FindPatternInModule(baseAddress, uWorldSignature);
        if (!signatureAddress) {
            std::cout << "[SignatureScanner] UWorld signature not found!" << std::endl;
            return 0;
        }
        
        std::cout << "[SignatureScanner] UWorld signature found at: 0x" << std::hex << signatureAddress << std::endl;
        
        // The signature is: 48 8B 3D ? ? ? ? (mov rdi, qword ptr [rip + displacement])
        // The displacement is at offset 3, and the instruction size is 7
        uintptr_t uWorldPointer = ResolveRipRelative(signatureAddress, 3, 7);
        if (!uWorldPointer) {
            std::cout << "[SignatureScanner] Failed to resolve RIP-relative address!" << std::endl;
            return 0;
        }
        
        // Calculate offset from base address
        uintptr_t uWorldOffset = uWorldPointer - baseAddress;
        
        std::cout << "[SignatureScanner] UWorld pointer: 0x" << std::hex << uWorldPointer << std::endl;
        std::cout << "[SignatureScanner] UWorld offset: 0x" << std::hex << uWorldOffset << std::endl;
        
        return uWorldOffset;
    }

} 