#pragma once
#include <map>
#include <vector>
#include <string>
#include "../ImGui/imgui.h"

using HTextureID = void*;

struct HImage {
    bool isGif = false;
    HTextureID texture = nullptr;  // For static images
    std::vector<HTextureID> frames;  // For GIF frames
    std::vector<float> delays;  // Delay for each frame in milliseconds
    int width = 0;
    int height = 0;
    int currentFrame = 0;
    float elapsedTime = 0.0f;
    float delay = 100.0f;  // Default delay in milliseconds

    ~HImage() {
        // Note: Texture cleanup should be handled by the graphics API
        frames.clear();
        delays.clear();
    }
};

// Function pointer types for texture management
using CreateTextureFunc = HTextureID(*)(unsigned char*, int, int, int);
using DeleteTextureFunc = void(*)(HTextureID);

struct HImGuiIO {
    CreateTextureFunc CreateTexture = nullptr;
    DeleteTextureFunc DeleteTexture = nullptr;
};

class HImageManager {
private:
    static std::map<std::string, HImage*> images;
    static HImGuiIO io;

public:
    static HImGuiIO& GetIO() { return io; }
    
    // Load and manage GIF images
    static bool GetImage_gif(const char* path, HImage*& image, float defaultDelay = 100.0f);
    static void Image_gif(const char* path, const ImVec2& size, float defaultDelay = 100.0f);
    
    // Update animation states
    static void updata(float deltaTime);

    // Cleanup resources
    static void Cleanup() {
        if (io.DeleteTexture) {
            for (auto& pair : images) {
                HImage* img = pair.second;
                if (img) {
                    if (img->texture) {
                        io.DeleteTexture(img->texture);
                    }
                    for (auto& frame : img->frames) {
                        if (frame) {
                            io.DeleteTexture(frame);
                        }
                    }
                    delete img;
                }
            }
        }
        images.clear();
    }
}; 