
#include "icons.h"

ID3D11ShaderResourceView* chestTexture = nullptr;
ID3D11ShaderResourceView* sobigshieldTexture = nullptr;
ID3D11ShaderResourceView* ChugSplashTexture = nullptr;
ID3D11ShaderResourceView* medkitTexture = nullptr;
ID3D11ShaderResourceView* FlowBerry_FizzTexture = nullptr;
ID3D11ShaderResourceView* Small_ShieldTexture = nullptr;
ID3D11ShaderResourceView* Nitro_SplashTexture = nullptr;
ID3D11ShaderResourceView* BandagesTexture = nullptr;
ID3D11ShaderResourceView* NukaColaTexture = nullptr;

ID3D11ShaderResourceView* Light_BulletsTexture = nullptr;
ID3D11ShaderResourceView* Medium_BulletsTexture = nullptr;
ID3D11ShaderResourceView* Heavy_BulletsTexture = nullptr;
ID3D11ShaderResourceView* ShellsTexture = nullptr;
ID3D11ShaderResourceView* RocketsTexture = nullptr;

ID3D11ShaderResourceView* Boom_BoltTexture= nullptr;
ID3D11ShaderResourceView* Combat_Assault_RifleTexture= nullptr;
ID3D11ShaderResourceView* Combat_ShotgunTexture= nullptr;
ID3D11ShaderResourceView* Enforcer_ARTexture= nullptr;
ID3D11ShaderResourceView* Frenzy_Auto_ShotgunTexture= nullptr; 
ID3D11ShaderResourceView* Hand_CannonTexture= nullptr;
ID3D11ShaderResourceView* Hammer_Pump_ShotgunTexture= nullptr;
ID3D11ShaderResourceView* Gatekeeper_ShotgunTexture= nullptr;
ID3D11ShaderResourceView* Nitro_FistsTexture= nullptr;
ID3D11ShaderResourceView* Huntress_DMRTexture= nullptr;
ID3D11ShaderResourceView* Harbinger_SMGTexture= nullptr;
ID3D11ShaderResourceView* Tactical_Assault_RifleTexture= nullptr;
ID3D11ShaderResourceView* Shockwave_grenadeTexture= nullptr;
ID3D11ShaderResourceView* Ranger_PistolTexture= nullptr;
ID3D11ShaderResourceView* Warforged_Assault_RifleTexture= nullptr;
ID3D11ShaderResourceView* Thunder_Burst_SMGTexture= nullptr;
ID3D11ShaderResourceView* HeavyImpactSniperRifleTexture = nullptr;

//RELOAD MODE
ID3D11ShaderResourceView* MKSevenAssaultRifleTexture = nullptr;
ID3D11ShaderResourceView* OGPump_ShotgunTexture = nullptr;
ID3D11ShaderResourceView* NewPump_ShotgunTexture = nullptr;
ID3D11ShaderResourceView* ChugJugTexture = nullptr;
ID3D11ShaderResourceView* BurstARTexture = nullptr;
ID3D11ShaderResourceView* MidasDrumGunTexture = nullptr;
ID3D11ShaderResourceView* SkyesARTexture = nullptr;
ID3D11ShaderResourceView* SlonesBurstARTexture = nullptr;
ID3D11ShaderResourceView* HeistedBreacherShotgunTexture = nullptr;
ID3D11ShaderResourceView* HeistedAccelerantShotgunTexture = nullptr;
ID3D11ShaderResourceView* HeistedExplosiveARTexture = nullptr;
ID3D11ShaderResourceView* HeistedBlinkMagSMGTexture = nullptr;
ID3D11ShaderResourceView* HeistedRunGunSMGTexture = nullptr;
ID3D11ShaderResourceView* TacticalShotgunTexture = nullptr;
ID3D11ShaderResourceView* LeverActionShotgunTexture = nullptr;
ID3D11ShaderResourceView* HeavyShotgunTexture = nullptr;
ID3D11ShaderResourceView* RangerShotgunTexture = nullptr;
ID3D11ShaderResourceView* AssaultRifleTexture = nullptr;
ID3D11ShaderResourceView* ScarAssaultRifleTexture = nullptr;
ID3D11ShaderResourceView* HammerAssaultRifleTexture = nullptr;
ID3D11ShaderResourceView* HeavyAssaultRifleTexture = nullptr;
ID3D11ShaderResourceView* InfantryRifleTexture = nullptr;
ID3D11ShaderResourceView* SubmachineGunTexture = nullptr;
ID3D11ShaderResourceView* TacticalSubmachineGunTexture = nullptr;
ID3D11ShaderResourceView* StingerSMGTexture = nullptr;
ID3D11ShaderResourceView* BoltActionSniperRifleTexture = nullptr;
ID3D11ShaderResourceView* HuntingRifleTexture = nullptr;
ID3D11ShaderResourceView* PistolTexture = nullptr;
ID3D11ShaderResourceView* RevolverTexture = nullptr;
ID3D11ShaderResourceView* GrapplerTexture = nullptr;
ID3D11ShaderResourceView* RocketLauncherTexture = nullptr;
ID3D11ShaderResourceView* CrashPadTexture = nullptr;

ID3D11ShaderResourceView* Striker_Burst_RifleTexture = nullptr;
ID3D11ShaderResourceView* Striker_ARTexture = nullptr;
ID3D11ShaderResourceView* Sovereign_ShotgunTexture = nullptr;
ID3D11ShaderResourceView* Monarch_PistolTexture = nullptr;
ID3D11ShaderResourceView* Hyper_SMGTexture = nullptr;
ID3D11ShaderResourceView* Firefly_JarTexture = nullptr;
ID3D11ShaderResourceView* Dual_Micro_SMGsTexture = nullptr;
ID3D11ShaderResourceView* Captain_AmericaShieldWeaponTexture = nullptr;


ID3D11ShaderResourceView* DualPistolsTexture = nullptr;
ID3D11ShaderResourceView* RapidFireSMGTexture = nullptr;
ID3D11ShaderResourceView* SuppressedSMGTexture = nullptr;
ID3D11ShaderResourceView* SuppressedAssaultRifleTexture = nullptr;
ID3D11ShaderResourceView* HeavySniperRifleTexture = nullptr;
ID3D11ShaderResourceView* SemiAutomaticSniperRifleTexture = nullptr;
ID3D11ShaderResourceView* GrenadeLauncherTexture = nullptr;
ID3D11ShaderResourceView* RemoteExplosivesTexture = nullptr;
ID3D11ShaderResourceView* RegularGrenadesTexture = nullptr;
ID3D11ShaderResourceView* StinkBombTexture = nullptr;
ID3D11ShaderResourceView* BandageBazookaTexture = nullptr;
ID3D11ShaderResourceView* BoogieBombTexture = nullptr;
ID3D11ShaderResourceView* ClingersTexture = nullptr;

ID3D11ShaderResourceView* HoloTwisterAssaultRifleTexture = nullptr;
ID3D11ShaderResourceView* FuryAssaultRifleTexture = nullptr;
ID3D11ShaderResourceView* VeiledPrecisionSMGTexture = nullptr;
ID3D11ShaderResourceView* SentinelPumpTexture = nullptr;
ID3D11ShaderResourceView* TwinfireAutoShotgunTexture = nullptr;
ID3D11ShaderResourceView* OniShotgunTexture = nullptr;
ID3D11ShaderResourceView* SurgefireSMGTexture = nullptr;



ID3D11ShaderResourceView* iosIconTexture = nullptr;
ID3D11ShaderResourceView* linuxIconTexture = nullptr;
ID3D11ShaderResourceView* androidIconTexture = nullptr;
ID3D11ShaderResourceView* switchIconTexture = nullptr;
ID3D11ShaderResourceView* pcIconTexture = nullptr;
ID3D11ShaderResourceView* playstationIconTexture = nullptr;
ID3D11ShaderResourceView* xboxIconTexture = nullptr;


ID3D11ShaderResourceView* BronzeIIconTexture = nullptr;
ID3D11ShaderResourceView* BronzeIIIconTexture = nullptr;
ID3D11ShaderResourceView* BronzeIIIIconTexture = nullptr;
ID3D11ShaderResourceView* SilverIIconTexture = nullptr;
ID3D11ShaderResourceView* SilverIIIpcIconTexture = nullptr;
ID3D11ShaderResourceView* SilverIIIIIconTexture = nullptr;
ID3D11ShaderResourceView* GoldIIconTexture = nullptr;
ID3D11ShaderResourceView* GoldIIIconTexture = nullptr;
ID3D11ShaderResourceView* GoldIIIIconTexture = nullptr;
ID3D11ShaderResourceView* PlatinumIIconTexture = nullptr;
ID3D11ShaderResourceView* PlatinumIIIconTexture = nullptr;
ID3D11ShaderResourceView* PlatinumIIIIconTexture = nullptr;

ID3D11ShaderResourceView* DiamondIIconTexture = nullptr;
ID3D11ShaderResourceView* DiamondIIIconTexture = nullptr;
ID3D11ShaderResourceView* DiamondIIIIconTexture = nullptr;

ID3D11ShaderResourceView* EliteIconTexture = nullptr;
ID3D11ShaderResourceView* ChampionIconTexture = nullptr;
ID3D11ShaderResourceView* UnrealIconTexture = nullptr;

ID3D11ShaderResourceView* UnRankedIconTexture = nullptr;


ID3D11ShaderResourceView* KillsIconTexture = nullptr;
ID3D11ShaderResourceView* LevelIconTexture = nullptr;


void AddRadialGradient(ImDrawList* draw_list, const ImVec2& center, float radius, ImU32 col_in, ImU32 col_out) {
    if (((col_in | col_out) & IM_COL32_A_MASK) == 0 || radius < 0.5f)
        return;

    // Use arc with automatic segment count
    draw_list->_PathArcToFastEx(center, radius, 0, IM_DRAWLIST_ARCFAST_SAMPLE_MAX, 0);
    const int count = draw_list->_Path.Size - 1;

    unsigned int vtx_base = draw_list->_VtxCurrentIdx;
    draw_list->PrimReserve(count * 3, count + 1);

    // Submit vertices
    const ImVec2 uv = draw_list->_Data->TexUvWhitePixel;
    draw_list->PrimWriteVtx(center, uv, col_in);
    for (int n = 0; n < count; n++)
        draw_list->PrimWriteVtx(draw_list->_Path[n], uv, col_out);

    // Submit a fan of triangles
    for (int n = 0; n < count; n++) {
        draw_list->PrimWriteIdx((ImDrawIdx)(vtx_base));
        draw_list->PrimWriteIdx((ImDrawIdx)(vtx_base + 1 + n));
        draw_list->PrimWriteIdx((ImDrawIdx)(vtx_base + 1 + ((n + 1) % count)));
    }
    draw_list->_Path.Size = 0;
}

void DrawItemIconWithCircle(ImVec2 position, ID3D11ShaderResourceView* iconTexture, ImColor borderColor, ImColor gradientStartColor, ImColor gradientEndColor, float radius) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();

    // Draw the gradient circle using the AddRadialGradient function
    AddRadialGradient(drawList, position, radius, gradientStartColor, gradientEndColor);

    // Draw the outer grey circle
    drawList->AddCircle(position, radius, borderColor, 64, 2.0f); // Adjust the thickness as needed

    // Calculate icon position
    ImVec2 iconSize(radius * 1.8f, radius * 1.8f); // Adjust the icon size relative to the radius
    ImVec2 iconTopLeft(position.x - iconSize.x / 2, position.y - iconSize.y / 2);
    ImVec2 iconBottomRight(position.x + iconSize.x / 2, position.y + iconSize.y / 2);

    // Draw the icon in the center of the circle
    drawList->AddImage((void*)iconTexture, iconTopLeft, iconBottomRight);
}
void DrawItemIconWithCirclePlayer(ImVec2 position, ID3D11ShaderResourceView* iconTexture, ImColor borderColor, ImColor gradientStartColor, ImColor gradientEndColor, float radius, const std::string& text = "", ImColor textColor = ImColor(255, 255, 255)) {
	ImDrawList* drawList = ImGui::GetBackgroundDrawList();

	// Draw the gradient circle
	AddRadialGradient(drawList, position, radius, gradientStartColor, gradientEndColor);

	// Draw the outer circle (border)
	drawList->AddCircle(position, radius, borderColor, 64, 2.0f); // Adjust thickness as needed

	// Calculate icon position and size
	ImVec2 iconSize(radius * 1.5f, radius * 1.5f); // Adjust icon size relative to the radius
	ImVec2 iconTopLeft(position.x - iconSize.x / 2, position.y - iconSize.y / 2);
	ImVec2 iconBottomRight(position.x + iconSize.x / 2, position.y + iconSize.y / 2);

	// Draw the icon centered in the circle
	drawList->AddImage((void*)iconTexture, iconTopLeft, iconBottomRight);

	// If text is provided, draw it centered below the icon
	if (!text.empty()) {
		ImFont* font = ImGui::GetFont();
		float fontSize = font->FontSize;

		// Measure text size
		ImVec2 textSize = font->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, text.c_str());

		// Calculate text position
		float textPosX = position.x - textSize.x / 2; // Center text horizontally
		float textPosY = position.y + radius + 5;     // Slightly below the circle

		// Draw the text with an outline for visibility
		ImVec2 offsets[] = { {-1, -1}, {-1, 0}, {-1, 1}, {0, -1}, {0, 1}, {1, -1}, {1, 0}, {1, 1} };
		for (auto& offset : offsets) {
			ImVec2 outlinePos = ImVec2(textPosX + offset.x, textPosY + offset.y);
			drawList->AddText(font, fontSize, outlinePos, ImColor(0, 0, 0), text.c_str());
		}
		drawList->AddText(font, fontSize, ImVec2(textPosX, textPosY), textColor, text.c_str());
	}
}

void DrawItemIconWithCircleCostom(ImVec2 position, ID3D11ShaderResourceView* iconTexture, ImColor borderColor, ImColor gradientStartColor, ImColor gradientEndColor, float radius) {
	ImDrawList* drawList = ImGui::GetBackgroundDrawList();

	// Draw the gradient circle using the AddRadialGradient function
	AddRadialGradient(drawList, position, radius, gradientStartColor, gradientEndColor);

	// Draw the outer grey circle
	drawList->AddCircle(position, radius, borderColor, 64, 2.0f); // Adjust the thickness as needed

	// Calculate icon position
	ImVec2 iconSize(radius * 1.5f, radius * 1.5f); // Adjust the icon size relative to the radius
	ImVec2 iconTopLeft(position.x - iconSize.x / 2, position.y - iconSize.y / 2);
	ImVec2 iconBottomRight(position.x + iconSize.x / 2, position.y + iconSize.y / 2);

	// Draw the icon in the center of the circle
	drawList->AddImage((void*)iconTexture, iconTopLeft, iconBottomRight);
}

void DrawItemTextAndIcon(Vector2 ScreenLocation, const std::string& ItemText, ImColor outlineColor, ImColor textColor, float fontSize, bool iconsEnabled, const std::string& ItemName, float iconSize)
{
	ImFont* font = ImGui::GetFont();
	ImVec2 textSize = font->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, ItemText.c_str());
	ImVec2 textPosition = ImVec2(ScreenLocation.x - textSize.x / 2, ScreenLocation.y + 3 + 1);

	ImVec2 offsets[] = { {-0.5, -0.5}, {-0.5, 0}, {-0.5, 0.5}, {0, -0.5}, {0, 0.5}, {0.5, -0.5}, {0.5, 0}, {0.5, 0.5} };
	for (auto& offset : offsets)
	{
		ImVec2 outlinePos = ImVec2(textPosition.x + offset.x, textPosition.y + offset.y);
		ImGui::GetBackgroundDrawList()->AddText(font, fontSize, outlinePos, outlineColor, ItemText.c_str());
	}
	ImGui::GetBackgroundDrawList()->AddText(font, fontSize, textPosition, textColor, ItemText.c_str());

	if (iconsEnabled)
	{
		ImVec2 iconPosition = (ItemText.empty()) ? ImVec2(ScreenLocation.x, ScreenLocation.y) : ImVec2(ScreenLocation.x, ScreenLocation.y - 20);
		ID3D11ShaderResourceView* currentTexture = GetItemTexture(ItemName);
		if (currentTexture)
		{
			DrawItemIconWithCircle(iconPosition, currentTexture, outlineColor, outlineColor, IM_COL32(0, 0, 0, 155), iconSize);
		}
	}
}

ID3D11ShaderResourceView* GetItemTexture(const std::string& ItemName)
{
	if (ItemName.find("Chest") != std::string::npos) return chestTexture;
	else if (ItemName.find("Shield Potion") != std::string::npos) return sobigshieldTexture;
	else if (ItemName.find("Small Shield Potion") != std::string::npos) return Small_ShieldTexture;
	else if (ItemName.find("Med Kit") != std::string::npos) return medkitTexture;
	else if (ItemName.find("Bandage") != std::string::npos) return BandagesTexture;
	else if (ItemName.find("FlowBerry Fizz") != std::string::npos) return FlowBerry_FizzTexture;
	else if (ItemName.find("Chug Splash") != std::string::npos) return ChugSplashTexture;
	else if (ItemName.find("Nitro Splash") != std::string::npos) return Nitro_SplashTexture;
	else if (ItemName.find("Nuka") != std::string::npos) return Nitro_SplashTexture;
	else if (ItemName.find("Light Bullets") != std::string::npos) return Light_BulletsTexture;
	else if (ItemName.find("Medium Bullets") != std::string::npos) return Medium_BulletsTexture;
	else if (ItemName.find("Heavy Bullets") != std::string::npos) return Heavy_BulletsTexture;
	else if (ItemName.find("Shells") != std::string::npos) return ShellsTexture;
	else if (ItemName.find("Rockets") != std::string::npos) return RocketsTexture;
	else if (ItemName.find("Harbinger") != std::string::npos) return Harbinger_SMGTexture;
	else if (ItemName.find("Thunder Burst SMG") != std::string::npos) return Thunder_Burst_SMGTexture;
	else if (ItemName.find("Combat Assault Rifle") != std::string::npos) return Combat_Assault_RifleTexture;
	else if (ItemName.find("Enforcer AR") != std::string::npos) return Enforcer_ARTexture;
	else if (ItemName.find("Tactical Assault Rifle") != std::string::npos) return Tactical_Assault_RifleTexture;
	else if (ItemName.find("Warforged Assault Rifle") != std::string::npos) return Warforged_Assault_RifleTexture;
	else if (ItemName.find("Hammer Pump Shotgun") != std::string::npos) return Hammer_Pump_ShotgunTexture;
	else if (ItemName.find("Gatekeeper Shotgun") != std::string::npos) return Gatekeeper_ShotgunTexture;
	else if (ItemName.find("Combat Shotgun") != std::string::npos) return Combat_ShotgunTexture;
	else if (ItemName.find("Frenzy Auto Shotgun") != std::string::npos) return Frenzy_Auto_ShotgunTexture;
	else if (ItemName.find("Pistol") != std::string::npos) return Ranger_PistolTexture;
	else if (ItemName.find("DMR") != std::string::npos) return Huntress_DMRTexture;
	else if (ItemName.find("Boom Bolt") != std::string::npos) return Boom_BoltTexture;
	else if (ItemName.find("Hand") != std::string::npos) return Hand_CannonTexture;
	else if (ItemName.find("Nitro Fists") != std::string::npos) return Nitro_FistsTexture;
	else if (ItemName.find("Shockwave Grenade") != std::string::npos) return Shockwave_grenadeTexture;
	else if (ItemName.find("Heavy Impact Sniper Rifle") != std::string::npos) return HeavyImpactSniperRifleTexture;

	/*else if (ItemName.find("Dual Micro SMGs") != std::string::npos) return HeavyImpactSniperRifleTexture;
	else if (ItemName.find("Mysterio's Sovereign Shotgun") != std::string::npos) return HeavyImpactSniperRifleTexture;
	else if (ItemName.find("War Machine's Arsenal") != std::string::npos) return HeavyImpactSniperRifleTexture;
	else if (ItemName.find("Monarch Pistol") != std::string::npos) return HeavyImpactSniperRifleTexture;
	else if (ItemName.find("Striker Burst Rifle") != std::string::npos) return HeavyImpactSniperRifleTexture;*/

	return nullptr;
}
