#pragma once
#include <string>
#include <unordered_set>
#include <shared_mutex>
#include <set>
#include <unordered_map>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../../Menu UI/ImGui/imgui.h"
#include <d3d11.h>
#include "../../GameClass/GameSettings.h"
#include "../../Framwork/Vectors.h"
#include "../../Kernel Driver/Driver/Driver.h"
#include "../../GameClass/Offsets.h"
#include "icons.h"


class LootSystem {
public:
	static void ProcessLoot();
	static void DrawItems();
};

enum class EFortRarity : uint8_t
{
	Common = 0,
	Uncommon = 1,
	Rare = 2,
	Epic = 3,
	Legendary = 4,
	Mythic = 5
};

typedef struct Pickups
{
	uintptr_t Actor;
	std::string Name;
	std::string Consumable;
	std::string Weapons;
	std::string Ammo;
	std::string Other;
	std::string isVehicle;
	std::string isLlama;
	std::string isChest;
	std::string isSupplyDrop;
	std::string isPickup;
	std::string isAmmoBox;
	std::string distance;
	Vector3 ItemPosition;
	ImColor Color;
	ID3D11ShaderResourceView* ItemsIcon;
	EFortRarity Rarity;  // Add this line

} Pickups;
enum class EFortPickupSpawnSource : uint8_t {
	Unset = 0,
	PlayerElimination = 1,
	Chest = 2,
	SupplyDrop = 3,
	AmmoBox = 4,
	Drone = 5,
	ItemSpawner = 6,
	BotElimination = 7,
	NPCElimination = 8,
	LootDrop = 9,
	TossedByPlayer = 10,
	NPC = 11,
	NPCGift = 12,
	CraftingBench = 13,
	VendingMachine = 14,
	QuestReward = 15

};

ImColor GetPickupColor(EFortRarity Rarity);
bool IsConsumableItem(const std::string& itemName);
bool IsWeaponItem(const std::string& itemName);
ID3D11ShaderResourceView* GetTextureForItem(const std::string& itemName, EFortRarity itemRarity);
bool IsAmmoItem(const std::string& itemName);
bool IsChestItem(const std::string& itemName);
void AddPickup(std::vector<Pickups>& pickupList, uintptr_t actor, const std::string& itemName, const Vector3& itemPosition, ImColor itemColor, const std::string& category, EFortRarity itemRarity);
void ReadPickups(std::vector<Pickups>& PickupTempList);
std::string GetExactRenamedItem(const std::string& itemName, EFortRarity rarity);
void RenderItem(const std::string& itemName, const Vector3& itemPosition, EFortRarity itemRarity, float fontSize, bool showName, bool showDistance, float iconSize, bool showIcon, ImColor outlineColor, ImColor textColor, bool isWeapon, Vector2 ScreenLocation);
