#include "FontSystem.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <shlobj.h>
#include <shlwapi.h>
#include "../../../Menu UI/ImGui/imgui.h"
#include "../../Features/Config/Config.h"

// Global font system instance
FontSystem g_FontSystem;

FontSystem& FontSystem::GetInstance() {
    return g_FontSystem;
}

void FontSystem::Initialize() {
    // Set up directories
    fontDirectory = "C:\\BitCheats\\Fonts\\";
    configDirectory = "C:\\BitCheats\\Fortnite_Config\\";
    
    // Create font directory if it doesn't exist
    CreateFontDirectory();
    
    // Load default fonts
    LoadDefaultFonts();
    
    // Load custom fonts from config
    LoadFontsFromConfig();
    
    // Load font selections
    LoadFontSelections();
}

void FontSystem::CreateFontDirectory() {
    if (!std::filesystem::exists(fontDirectory)) {
        std::filesystem::create_directories(fontDirectory);
    }
}

void FontSystem::LoadDefaultFonts() {
    // Clear existing default fonts
    defaultFonts.clear();
    
    // Define default system fonts to load
    std::vector<std::string> defaultFontNames = {
        "Arial",
        "Calibri", 
        "Segoe UI"
    };
    
    for (const auto& fontName : defaultFontNames) {
        std::string fontPath = GetSystemFontPath(fontName);
        if (!fontPath.empty() && std::filesystem::exists(fontPath)) {
            FontInfo fontInfo;
            fontInfo.name = fontName;
            fontInfo.filepath = fontPath;
            fontInfo.filename = std::filesystem::path(fontPath).filename().string();
            fontInfo.isCustom = false;
            fontInfo.isLoaded = false;
            fontInfo.fontPtr = nullptr;
            fontInfo.size = 16.0f;
            fontInfo.lastModified = std::chrono::system_clock::now();
            
            // Try to load the font
            if (LoadFontFromFile(fontPath, 16.0f, false)) {
                fontInfo.isLoaded = true;
                fontInfo.fontPtr = fontMap[fontName];
            }
            
            defaultFonts.push_back(fontInfo);
        }
    }
}

std::string FontSystem::GetSystemFontPath(const std::string& fontName) {
    char windowsDir[MAX_PATH];
    GetWindowsDirectoryA(windowsDir, MAX_PATH);
    
    std::string fontPath = std::string(windowsDir) + "\\Fonts\\" + fontName + ".ttf";
    if (std::filesystem::exists(fontPath)) {
        return fontPath;
    }
    
    // Try alternative extensions
    std::vector<std::string> extensions = { ".otf", ".TTF", ".OTF" };
    for (const auto& ext : extensions) {
        std::string altPath = std::string(windowsDir) + "\\Fonts\\" + fontName + ext;
        if (std::filesystem::exists(altPath)) {
            return altPath;
        }
    }
    
    return "";
}

bool FontSystem::LoadFontFromFile(const std::string& filepath, float size, bool isCustom) {
    try {
        if (!std::filesystem::exists(filepath)) {
            return false;
        }
        
        // Validate font file
        if (!ValidateFontFile(filepath)) {
            return false;
        }
        
        // Check file size (max 5MB)
        float fileSize = GetFontFileSize(filepath);
        if (fileSize > 5.0f) {
            return false;
        }
        
        ImGuiIO& io = ImGui::GetIO();
        std::string fontName = std::filesystem::path(filepath).stem().string();
        
        // Load the font
        ImFont* font = io.Fonts->AddFontFromFileTTF(filepath.c_str(), size);
        if (font) {
            fontMap[fontName] = font;
            return true;
        }
        
        return false;
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading font from file: " << e.what() << std::endl;
        return false;
    }
}

bool FontSystem::LoadCustomFont(const std::string& filepath, float size) {
    try {
        if (!std::filesystem::exists(filepath)) {
            return false;
        }
        
        // Validate font file
        if (!ValidateFontFile(filepath)) {
            return false;
        }
        
        // Check file size (max 5MB)
        float fileSize = GetFontFileSize(filepath);
        if (fileSize > 5.0f) {
            return false;
        }
        
        std::string fontName = std::filesystem::path(filepath).stem().string();
        
        // Check if font already exists
        for (const auto& font : customFonts) {
            if (font.name == fontName) {
                return false; // Font already exists
            }
        }
        
        // Load the font
        if (LoadFontFromFile(filepath, size, true)) {
            FontInfo fontInfo;
            fontInfo.name = fontName;
            fontInfo.filepath = filepath;
            fontInfo.filename = std::filesystem::path(filepath).filename().string();
            fontInfo.isCustom = true;
            fontInfo.isLoaded = true;
            fontInfo.fontPtr = fontMap[fontName];
            fontInfo.size = size;
            fontInfo.lastModified = std::chrono::system_clock::now();
            
            customFonts.push_back(fontInfo);
            
            // Save to config
            SaveFontsToConfig();
            
            return true;
        }
        
        return false;
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading custom font: " << e.what() << std::endl;
        return false;
    }
}

bool FontSystem::UnloadCustomFont(const std::string& name) {
    auto it = std::find_if(customFonts.begin(), customFonts.end(),
        [&name](const FontInfo& font) { return font.name == name; });
    
    if (it != customFonts.end()) {
        // Remove from font map
        fontMap.erase(name);
        
        // Remove from custom fonts
        customFonts.erase(it);
        
        // Save to config
        SaveFontsToConfig();
        
        return true;
    }
    
    return false;
}

ImFont* FontSystem::GetFont(const std::string& name) {
    auto it = fontMap.find(name);
    if (it != fontMap.end()) {
        return it->second;
    }
    
    // Return default font if not found
    if (!fontMap.empty()) {
        return fontMap.begin()->second;
    }
    
    return nullptr;
}

const std::vector<FontInfo>& FontSystem::GetAvailableFonts() const {
    static std::vector<FontInfo> allFonts;
    allFonts.clear();
    
    // Add default fonts
    allFonts.insert(allFonts.end(), defaultFonts.begin(), defaultFonts.end());
    
    // Add custom fonts
    allFonts.insert(allFonts.end(), customFonts.begin(), customFonts.end());
    
    return allFonts;
}

const std::vector<FontInfo>& FontSystem::GetDefaultFonts() const {
    return defaultFonts;
}

const std::vector<FontInfo>& FontSystem::GetCustomFonts() const {
    return customFonts;
}

bool FontSystem::FontExists(const std::string& name) const {
    return fontMap.find(name) != fontMap.end();
}

bool FontSystem::ValidateFontFile(const std::string& filepath) const {
    if (!std::filesystem::exists(filepath)) {
        return false;
    }
    
    // Check file extension
    std::string extension = std::filesystem::path(filepath).extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
    
    if (extension != ".ttf" && extension != ".otf") {
        return false;
    }
    
    return true;
}

float FontSystem::GetFontFileSize(const std::string& filepath) const {
    try {
        if (!std::filesystem::exists(filepath)) {
            return 0.0f;
        }
        
        std::filesystem::path path(filepath);
        std::uintmax_t fileSize = std::filesystem::file_size(path);
        
        // Convert to MB
        return static_cast<float>(fileSize) / (1024.0f * 1024.0f);
    }
    catch (const std::exception& e) {
        return 0.0f;
    }
}

void FontSystem::SaveFontSelections() {
    try {
        std::string configPath = configDirectory + "font_selections.cfg";
        std::ofstream file(configPath);
        
        if (file.is_open()) {
            file << "[FontSelections]" << std::endl;
            for (const auto& selection : fontSelections) {
                file << selection.first << "=" << selection.second << std::endl;
            }
            file.close();
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error saving font selections: " << e.what() << std::endl;
    }
}

void FontSystem::LoadFontSelections() {
    try {
        std::string configPath = configDirectory + "font_selections.cfg";
        std::ifstream file(configPath);
        
        if (file.is_open()) {
            std::string line;
            std::string section;
            
            while (std::getline(file, line)) {
                if (line.empty() || line[0] == ';') continue;
                
                if (line[0] == '[' && line.back() == ']') {
                    section = line;
                }
                else if (section == "[FontSelections]") {
                    size_t equalPos = line.find('=');
                    if (equalPos != std::string::npos) {
                        std::string key = line.substr(0, equalPos);
                        std::string value = line.substr(equalPos + 1);
                        fontSelections[key] = value;
                    }
                }
            }
            file.close();
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading font selections: " << e.what() << std::endl;
    }
}

void FontSystem::LoadFontsFromConfig() {
    try {
        std::string configPath = configDirectory + "custom_fonts.cfg";
        std::ifstream file(configPath);
        
        if (file.is_open()) {
            std::string line;
            while (std::getline(file, line)) {
                if (line.empty() || line[0] == ';') continue;
                
                size_t commaPos = line.find(',');
                if (commaPos != std::string::npos) {
                    std::string filepath = line.substr(0, commaPos);
                    std::string sizeStr = line.substr(commaPos + 1);
                    
                    try {
                        float size = std::stof(sizeStr);
                        if (std::filesystem::exists(filepath)) {
                            LoadCustomFont(filepath, size);
                        }
                    }
                    catch (const std::exception& e) {
                        // Skip invalid entries
                    }
                }
            }
            file.close();
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error loading fonts from config: " << e.what() << std::endl;
    }
}

void FontSystem::SaveFontsToConfig() {
    try {
        std::string configPath = configDirectory + "custom_fonts.cfg";
        std::ofstream file(configPath);
        
        if (file.is_open()) {
            for (const auto& font : customFonts) {
                file << font.filepath << "," << font.size << std::endl;
            }
            file.close();
        }
    }
    catch (const std::exception& e) {
        std::cerr << "Error saving fonts to config: " << e.what() << std::endl;
    }
}

std::string FontSystem::GetFontDirectory() const {
    return fontDirectory;
}

void FontSystem::RefreshFonts() {
    // Reload default fonts
    LoadDefaultFonts();
    
    // Reload custom fonts from config
    LoadFontsFromConfig();
    
    // Reload font selections
    LoadFontSelections();
} 