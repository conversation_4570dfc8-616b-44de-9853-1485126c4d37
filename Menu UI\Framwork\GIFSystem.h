#pragma once
#include <Windows.h>
#include <d3d11.h>
#include <vector>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../ImGui/imgui.h"

// GIF frame structure
struct GIFFrame {
    ID3D11ShaderResourceView* texture;
    float delay; // delay time for each frame
};

// Global GIF variables
extern std::vector<GIFFrame> gifFrames;
extern bool g_GifLoaded;
extern int currentFrame;
extern float frameTime;

// Global GIF functions
extern void RenderGIFWithDrawList(const ImVec2& size, float speed, float life_cycle, const ImVec2& uv0, const ImVec2& uv1, const ImVec4& tint_col, const ImVec4& border_col);
extern void LoadGIF(ID3D11Device* device, const unsigned char* gif_data, size_t gif_size);
extern void CleanupGIF();
extern ID3D11ShaderResourceView* CreateTextureFromMemory(ID3D11Device* device, unsigned char* data, int width, int height); 