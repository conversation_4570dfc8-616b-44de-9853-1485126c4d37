﻿  HImGuiImageManager.cpp
C:\bitcheats_cheats - New UI\Menu UI\HImGuiImageManager\Logger.h(32,38): error C4996: 'localtime': This function or variable may be unsafe. Consider using localtime_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
C:\bitcheats_cheats - New UI\Menu UI\HImGuiImageManager\Logger.h(39,42): error C4996: 'localtime': This function or variable may be unsafe. Consider using localtime_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  Aimbot.cpp
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(31,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(32,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(33,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(58,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(59,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(60,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(103,1): warning C4005: 'LOBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(206): message : see previous definition of 'LOBYTE'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(104,1): warning C4005: 'LOWORD': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(204): message : see previous definition of 'LOWORD'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(106,1): warning C4005: 'HIBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(207): message : see previous definition of 'HIBYTE'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(161,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(162,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,39): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,29): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(207,41): warning C4305: '=': truncation from 'double' to 'float'
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(212,41): warning C4305: '=': truncation from 'double' to 'float'
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(299,50): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(299,33): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(298,52): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(298,33): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(307,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(325,141): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(340,40): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Aimbot\Aimbot.cpp(436,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2541,23): warning C4244: 'argument': conversion from 'wchar_t' to 'const _Elem', possible loss of data
          with
          [
              _Elem=char
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
  Cache.cpp
C:\bitcheats_cheats - New UI\Cheat Core\Features\Caching\Cache.h(6,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Cheat Core\Features\Caching\Cache.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(31,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(32,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(33,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(58,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(59,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(60,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(103,1): warning C4005: 'LOBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(206): message : see previous definition of 'LOBYTE'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(104,1): warning C4005: 'LOWORD': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(204): message : see previous definition of 'LOWORD'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(106,1): warning C4005: 'HIBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(207): message : see previous definition of 'HIBYTE'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2541,23): warning C4244: 'argument': conversion from 'wchar_t' to 'const _Elem', possible loss of data
          with
          [
              _Elem=char
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
  Config.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(161,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(162,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,39): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,29): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(41,53): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(42,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(58,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(61,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(71,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(76,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(92,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(95,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(105,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(110,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(134,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(137,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(150,43): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(155,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(175,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(180,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(330,32): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(565,32): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(815,32): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(2182,34): warning C4101: 'e': unreferenced local variable
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(2598,34): warning C4101: 'e': unreferenced local variable
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(2705,34): warning C4101: 'e': unreferenced local variable
  FontSystem.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Cheat Core\Features\FontSystem\FontSystem.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\FontSystem\FontSystem.cpp(275,34): warning C4101: 'e': unreferenced local variable
C:\bitcheats_cheats - New UI\Cheat Core\Features\FontSystem\FontSystem.cpp(351,50): warning C4101: 'e': unreferenced local variable
  Loot.cpp
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.h(7,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(31,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(32,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(33,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(58,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(59,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(60,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(103,1): warning C4005: 'LOBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(206): message : see previous definition of 'LOBYTE'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(104,1): warning C4005: 'LOWORD': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(204): message : see previous definition of 'LOWORD'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(106,1): warning C4005: 'HIBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(207): message : see previous definition of 'HIBYTE'
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp(222,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp(423,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp(438,88): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp(438,49): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp(451,97): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp(451,76): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp(451,144): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Loot\Loot.cpp(451,126): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2541,23): warning C4244: 'argument': conversion from 'wchar_t' to 'const _Elem', possible loss of data
          with
          [
              _Elem=char
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
  Drawing.cpp
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(31,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(32,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(33,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(58,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(59,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(60,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(103,1): warning C4005: 'LOBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(206): message : see previous definition of 'LOBYTE'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(104,1): warning C4005: 'LOWORD': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(204): message : see previous definition of 'LOWORD'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(106,1): warning C4005: 'HIBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(207): message : see previous definition of 'HIBYTE'
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(16,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(19,57): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(19,36): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(81,93): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(81,83): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(81,65): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(81,55): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(82,97): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(82,85): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(82,65): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(82,55): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(84,111): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(84,92): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(84,65): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(84,55): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(85,123): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(85,107): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(85,83): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(85,64): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(86,117): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(86,101): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(86,77): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(86,61): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(88,109): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(88,91): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(88,65): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(88,55): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(89,119): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(89,104): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(89,81): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(89,63): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(90,113): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(90,98): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(90,75): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(90,60): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(92,105): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(92,91): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(92,69): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(92,57): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(93,111): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(93,96): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(93,73): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(93,59): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(94,115): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(94,99): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(94,75): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(94,60): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(96,103): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(96,90): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(96,69): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(96,57): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(97,107): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(97,93): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(97,71): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(97,58): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(98,111): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(98,96): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(98,73): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(98,59): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(137,62): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(137,30): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(165,62): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(165,30): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(256,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(258,74): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(258,31): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(260,148): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(279,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(280,14): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(281,78): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(281,71): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(281,58): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(281,55): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(282,85): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(282,63): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(282,52): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(282,49): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(283,85): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(283,74): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(283,52): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(283,49): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(284,93): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(284,82): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(284,77): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(284,64): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(285,93): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(285,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(285,56): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(285,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(286,93): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(286,80): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(286,75): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(286,49): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(287,93): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(287,80): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(287,54): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(287,49): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(288,101): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(288,88): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(288,81): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(288,66): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(289,101): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(289,88): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(289,81): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(289,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(300,73): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(300,66): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(300,53): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(300,50): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(304,79): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(304,72): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(304,59): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(304,56): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(390,46): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(390,33): warning C4244: 'argument': conversion from 'const double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(424,46): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(424,33): warning C4244: 'argument': conversion from 'const double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(449,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(452,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(453,15): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(456,22): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(478,18): warning C4244: 'initializing': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(481,57): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(488,44): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\Drawing\Drawing.cpp(488,35): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2541,23): warning C4244: 'argument': conversion from 'wchar_t' to 'const _Elem', possible loss of data
          with
          [
              _Elem=char
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
  PlayerVisuals.cpp
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(31,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(32,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(33,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(58,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(59,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Math.h(60,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(103,1): warning C4005: 'LOBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(206): message : see previous definition of 'LOBYTE'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(104,1): warning C4005: 'LOWORD': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(204): message : see previous definition of 'LOWORD'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\HexArray.h(106,1): warning C4005: 'HIBYTE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\shared\minwindef.h(207): message : see previous definition of 'HIBYTE'
C:\bitcheats_cheats - New UI\Utils.h(161,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(162,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,39): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,29): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(20,47): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(39,48): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(39,29): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(68,78): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(68,61): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(68,44): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(68,27): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(115,21): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(117,14): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(118,14): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(119,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(120,24): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(176,101): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(176,91): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(176,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(185,120): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(185,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(185,76): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(185,72): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Features\Visuals\PlayerVisuals.cpp(185,45): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2541,23): warning C4244: 'argument': conversion from 'wchar_t' to 'const _Elem', possible loss of data
          with
          [
              _Elem=char
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2552): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(_Iter,const _Iter,std::input_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2532): message : see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct<wchar_t*>(const _Iter,const _Iter,std::forward_iterator_tag)' being compiled
          with
          [
              _Iter=wchar_t *
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameFunctions.h(132): message : see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
  Settings.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(2,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Cheat Core\Settings\Settings.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  AbstractAnimation.cpp
  PauseAnimation.cpp
  Components.cpp
C:\bitcheats_cheats - New UI\Menu UI\Components\Components.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Components\Components.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(144,36): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(185,22): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(193,66): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(193,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(200,62): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(200,47): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
  nav_elements.cpp
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(144,36): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(185,22): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(193,66): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(193,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(200,62): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(200,47): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(161,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(162,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,39): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,29): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(450,73): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(451,73): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(452,50): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(509,94): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(534,76): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(768,27): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(990,46): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(995,35): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1567,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1604,91): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1820,73): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1821,73): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1822,50): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1942,31): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(3504,73): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(3505,70): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(3506,50): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(3862,73): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(3863,70): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(3864,50): warning C4566: character represented by universal-character-name '\uEA80' cannot be represented in the current code page (1252)
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(3940,31): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4074,48): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4079,39): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  GUI.cpp
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(161,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(162,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,39): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Utils.h(207,29): warning C4244: 'argument': conversion from 'double' to 'ULONG', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(144,36): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(185,22): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(193,66): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(193,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(200,62): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(200,47): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(76,1): warning C4005: 'ICON_KEYBOARD_LINE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(56): message : see previous definition of 'ICON_KEYBOARD_LINE'
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(80,1): warning C4005: 'ICON_SETTINGS_LINE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(54): message : see previous definition of 'ICON_SETTINGS_LINE'
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(87,1): warning C4005: 'ICON_DOWNLOAD_LINE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(48): message : see previous definition of 'ICON_DOWNLOAD_LINE'
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(349,46): warning C4838: conversion from 'int' to 'ImWchar' requires a narrowing conversion
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(349,58): warning C4305: 'initializing': truncation from 'int' to 'ImWchar'
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(349,46): warning C4309: 'initializing': truncation of constant value
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(381,39): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(397,44): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(445,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(690,47): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(724,23): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(2398,27): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(2418,60): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(2447,60): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(2603,60): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(2685,60): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(2748,49): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  imgui_widgets.cpp
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(3,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_widgets.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(144,36): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(185,22): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(193,66): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(193,51): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(200,62): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(200,47): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
  main.cpp
C:\bitcheats_cheats - New UI\main.cpp(17,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\main.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(89,19): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(174,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(175,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(176,22): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(178,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(179,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(180,12): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(182,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(183,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(184,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(185,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(186,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(187,32): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(189,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(190,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(191,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(192,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(194,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(195,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(196,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(197,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(199,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(200,13): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(201,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\Framwork\Vectors.h(202,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(411,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(412,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(413,21): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(414,25): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(419,23): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\Cheat Core\GameClass\GameSettings.h(420,28): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
C:\bitcheats_cheats - New UI\main.cpp(222,31): error C2679: binary '=': no operator found which takes a right-hand operand of type 'WCHAR [260]' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(3061,42): message : could be 'std::basic_string<char,std::char_traits<char>,std::allocator<char>> &std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator =(const _Elem)'
          with
          [
              _Elem=char
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(3057,42): message : or       'std::basic_string<char,std::char_traits<char>,std::allocator<char>> &std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator =(const _Elem *const )'
          with
          [
              _Elem=char
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(3042,42): message : or       'std::basic_string<char,std::char_traits<char>,std::allocator<char>> &std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator =(const std::basic_string<char,std::char_traits<char>,std::allocator<char>> &)'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2931,42): message : or       'std::basic_string<char,std::char_traits<char>,std::allocator<char>> &std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator =(std::initializer_list<_Elem>)'
          with
          [
              _Elem=char
          ]
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\xstring(2814,42): message : or       'std::basic_string<char,std::char_traits<char>,std::allocator<char>> &std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator =(std::basic_string<char,std::char_traits<char>,std::allocator<char>> &&) noexcept(<expr>)'
C:\bitcheats_cheats - New UI\main.cpp(222,31): message : while trying to match the argument list '(std::string, WCHAR [260])'
C:\bitcheats_cheats - New UI\main.cpp(321,24): warning C4305: '=': truncation from 'double' to 'float'
C:\bitcheats_cheats - New UI\main.cpp(324,24): warning C4305: '=': truncation from 'double' to 'float'
C:\bitcheats_cheats - New UI\main.cpp(327,23): warning C4305: '=': truncation from 'double' to 'float'
C:\bitcheats_cheats - New UI\main.cpp(330,23): warning C4305: '=': truncation from 'double' to 'float'
  Generating Code...
C:\bitcheats_cheats - New UI\Cheat Core\Features\Config\Config.cpp(2079): warning C4715: 'SaveAimSettingsToFile': not all control paths return a value
