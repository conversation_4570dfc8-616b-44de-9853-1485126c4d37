#include "PlayerVisuals.h"
#include "../../Framwork/Vectors.h"
#include "../Caching/Cache.h"
#include "../../Kernel Driver/Driver/Driver.h"
#include "../../GameClass/Offsets.h"
#include "../../GameClass/GameSettings.h"
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../../Menu UI/ImGui/imgui.h"
#include "../Aimbot/Aimbot.h"
#include "Drawing/Drawing.h"
#include "../../../Utils.h"
#include "../Loot/Loot.h"
#include "../../Settings/Settings.h"

// Initialize static members
bool PlayerVisuals::hasTarget = false;
bool PlayerVisuals::isReady = false;

float PlayerVisuals::CalculateDistance(Vector3& HeadBone3D) {
	return vCamera.Location.Distance(HeadBone3D) / 100;
}

void PlayerVisuals::HandleToggleKeys() {
	if ((GetAsyncKeyState(HOTKEY_SETTINGS.global.toggle.key) & 0x8001) == 0x8001) AIMBOT_SETTINGS.general.enabled = !AIMBOT_SETTINGS.general.enabled;
	if ((GetAsyncKeyState(HOTKEY_SETTINGS.global.togglePlayers.key) & 0x8001) == 0x8001) PLAYER_ESP_SETTINGS.enabled = !PLAYER_ESP_SETTINGS.enabled;
	if ((GetAsyncKeyState(HOTKEY_SETTINGS.global.toggleItems.key) & 0x8001) == 0x8001) {
		ITEM_ESP_SETTINGS.ammo.enabled = !ITEM_ESP_SETTINGS.ammo.enabled;
		ITEM_ESP_SETTINGS.consumables.enabled = !ITEM_ESP_SETTINGS.consumables.enabled;
		ITEM_ESP_SETTINGS.weapons.enabled = !ITEM_ESP_SETTINGS.weapons.enabled;
		ITEM_ESP_SETTINGS.others.enabled = !ITEM_ESP_SETTINGS.others.enabled;
	}
	if ((GetAsyncKeyState(HOTKEY_SETTINGS.global.toggleRadar.key) & 0x8001) == 0x8001) RADAR_SETTINGS.enabled = !RADAR_SETTINGS.enabled;
}

void PlayerVisuals::DrawAllPlayers() {
	// Ensure settings are synced before drawing
	SettingsHelper::SyncSettings();
	
	ImVec2 center(OverlayWidth / 2, OverlayHeight / 2);

	// Draw FOV and Crosshair if enabled
	if (AIMBOT_SETTINGS.general.visuals.drawFov) Drawing::DrawFovCircle(AIMBOT_SETTINGS.general.fov, AIMBOT_SETTINGS.general.visuals.drawFovFilled, AIMBOT_SETTINGS.general.visuals.drawFovRgb, false);
	if (AIMBOT_SETTINGS.general.visuals.drawCrosshair) Drawing::DrawCrosshair(center);

	g_ClosestDistance = FLT_MAX;
	g_CurrentTarget = NULL;
    hasTarget = false;

	// Process each player in the CachedPlayerList
	const std::vector<CachedPlayer>& players = CacheSystem::GetCachedPlayers();
    for (const auto& player : players) {
        ProcessPlayerESP(player);
    }

	DrawItems();

	if (AIMBOT_SETTINGS.general.enabled) {
		if (g_CurrentTarget && hasTarget) {
			AimbotSpace::ProcessTarget(g_CurrentTarget, g_Targeted_fort_pawn, g_AcknowlegedPawn, Results::PlayerCameraManager);
		}
		else if (!hasTarget) {
			g_CurrentTarget = NULL;
			g_ClosestDistance = FLT_MAX;
		}
	}

	if (RADAR_SETTINGS.enabled) {
		Drawing::DrawRadar(RADAR_SETTINGS.positionX, RADAR_SETTINGS.positionY, RADAR_SETTINGS.circleSize, RADAR_SETTINGS.rectangleSize);
	}
}

void PlayerVisuals::ProcessPlayerESP(const CachedPlayer& Actor) {
    // Skip invalid actors
    if (!Actor.Pawn || !Actor.Mesh || !Actor.PlayerState || !Actor.PlayerStates) 
        return;

    // Check for lobby state
    float player_health = GameFunctions::GetHealth(Actor.Pawn);
    bool currentInLobby = (player_health <= 0);
    if (currentInLobby != InLobby) {
        if (currentInLobby) {
            ResetLootCache();
            InLobby = true;
        } else {
            InLobby = false;
        }
    }

    // Team check filtering
    if (PLAYER_ESP_SETTINGS.teamCheck && Actor.AcknowledgedPawn) {
        int localPlayerTeamIndex = UseDriver::read<int>(UseDriver::read<uint64_t>(Actor.AcknowledgedPawn + Offsets::PlayerState) + Offsets::TeamIndex);
        int playerTeamIndex = UseDriver::read<int>(Actor.PlayerState + Offsets::TeamIndex);
        if (playerTeamIndex == localPlayerTeamIndex) return;
    }

    // AI filtering
    if (!PLAYER_ESP_SETTINGS.playerAi) {
        uintptr_t bIsABot = (UseDriver::read<char>(Actor.PlayerStates + Offsets::bIsABot) >> 3) & 1;
        if (bIsABot) return;
    }

    // Downed player filtering
    if (AIMBOT_SETTINGS.general.ignoreDowned || PLAYER_ESP_SETTINGS.ignoreDowned) {
        uintptr_t bIsKnocked = (UseDriver::read<char>(Actor.Pawn + Offsets::bIsKnocked) >> 4) & 1;
        if ((AIMBOT_SETTINGS.general.ignoreDowned && bIsKnocked) || (PLAYER_ESP_SETTINGS.ignoreDowned && bIsKnocked)) return;
    }

    // Get bone and screen positions
    Vector3 HeadBone3D = GameFunctions::GetBoneWithRotation(Actor.Mesh, 110);
    Vector2 Head2D = GameFunctions::ProjectWorldToScreen(HeadBone3D + Vector3(0, 0, 15));
    Vector2 Bottom2D = GameFunctions::ProjectWorldToScreen(GameFunctions::GetBoneWithRotation(Actor.Mesh, 0));
    float distance = CalculateDistance(HeadBone3D);

    // Calculate box dimensions
    float boxHeight = abs(Head2D.y - Bottom2D.y);
    float boxWidth = boxHeight * 0.6f;
    int BoxX = Head2D.x - (boxWidth / 2);
    int BoxY = Head2D.y;
    float CornerWidth = boxHeight * 0.6;
    float CornerHeight = abs(Head2D.y - Bottom2D.y);

    // Check visibility and knocked state
    bool isVisible = GameFunctions::IsVisible(Actor.Mesh);
    bool isKnocked = (UseDriver::read<char>(Actor.Pawn + Offsets::isDBNO) >> 6) & 1;

    // Set colors based on visibility and knocked state
    ImColor lineColor = isKnocked ? 
        (isVisible ? ImColor(Settings.Colors.PlayerColors_KnockedLineVisible[0], Settings.Colors.PlayerColors_KnockedLineVisible[1], Settings.Colors.PlayerColors_KnockedLineVisible[2]) 
                   : ImColor(Settings.Colors.PlayerColors_KnockedLineNonVisible[0], Settings.Colors.PlayerColors_KnockedLineNonVisible[1], Settings.Colors.PlayerColors_KnockedLineNonVisible[2]))
        : (isVisible ? ImColor(Settings.Colors.PlayerColors_LineVisible[0], Settings.Colors.PlayerColors_LineVisible[1], Settings.Colors.PlayerColors_LineVisible[2]) 
                     : ImColor(Settings.Colors.PlayerColors_LineNonVisible[0], Settings.Colors.PlayerColors_LineNonVisible[1], Settings.Colors.PlayerColors_LineNonVisible[2]));

    ImColor boxColor = isKnocked ? 
        (isVisible ? ImColor(Settings.Colors.PlayerColors_KnockedBoxVisible[0], Settings.Colors.PlayerColors_KnockedBoxVisible[1], Settings.Colors.PlayerColors_KnockedBoxVisible[2]) 
                   : ImColor(Settings.Colors.PlayerColors_KnockedBoxNonVisible[0], Settings.Colors.PlayerColors_KnockedBoxNonVisible[1], Settings.Colors.PlayerColors_KnockedBoxNonVisible[2]))
        : (isVisible ? ImColor(Settings.Colors.PlayerColors_BoxVisible[0], Settings.Colors.PlayerColors_BoxVisible[1], Settings.Colors.PlayerColors_BoxVisible[2]) 
                     : ImColor(Settings.Colors.PlayerColors_BoxNonVisible[0], Settings.Colors.PlayerColors_BoxNonVisible[1], Settings.Colors.PlayerColors_BoxNonVisible[2]));

    ImColor boxFillColor = isKnocked ? 
        (isVisible ? ImColor(Settings.Colors.PlayerColors_KnockedBoxFillVisible[0], Settings.Colors.PlayerColors_KnockedBoxFillVisible[1], Settings.Colors.PlayerColors_KnockedBoxFillVisible[2], Settings.Colors.PlayerColors_KnockedBoxFillVisible[3])
                   : ImColor(Settings.Colors.PlayerColors_KnockedBoxFillNonVisible[0], Settings.Colors.PlayerColors_KnockedBoxFillNonVisible[1], Settings.Colors.PlayerColors_KnockedBoxFillNonVisible[2], Settings.Colors.PlayerColors_KnockedBoxFillNonVisible[3]))
        : (isVisible ? ImColor(Settings.Colors.PlayerColors_BoxFillVisible[0], Settings.Colors.PlayerColors_BoxFillVisible[1], Settings.Colors.PlayerColors_BoxFillVisible[2], Settings.Colors.PlayerColors_BoxFillVisible[3])
                     : ImColor(Settings.Colors.PlayerColors_BoxFillNonVisible[0], Settings.Colors.PlayerColors_BoxFillNonVisible[1], Settings.Colors.PlayerColors_BoxFillNonVisible[2], Settings.Colors.PlayerColors_BoxFillNonVisible[3]));

    ImColor headCircleColor = isKnocked ? 
        (isVisible ? ImColor(Settings.Colors.PlayerColors_KnockedHeadVisible[0], Settings.Colors.PlayerColors_KnockedHeadVisible[1], Settings.Colors.PlayerColors_KnockedHeadVisible[2]) 
                   : ImColor(Settings.Colors.PlayerColors_KnockedHeadNonVisible[0], Settings.Colors.PlayerColors_KnockedHeadNonVisible[1], Settings.Colors.PlayerColors_KnockedHeadNonVisible[2]))
        : (isVisible ? ImColor(Settings.Colors.PlayerColors_HeadVisible[0], Settings.Colors.PlayerColors_HeadVisible[1], Settings.Colors.PlayerColors_HeadVisible[2]) 
                     : ImColor(Settings.Colors.PlayerColors_HeadNonVisible[0], Settings.Colors.PlayerColors_HeadNonVisible[1], Settings.Colors.PlayerColors_HeadNonVisible[2]));

    ImColor skeletonColor = isKnocked ? 
        (isVisible ? ImColor(Settings.Colors.PlayerColors_KnockedSkeletonVisible[0], Settings.Colors.PlayerColors_KnockedSkeletonVisible[1], Settings.Colors.PlayerColors_KnockedSkeletonVisible[2]) 
                   : ImColor(Settings.Colors.PlayerColors_KnockedSkeletonNonVisible[0], Settings.Colors.PlayerColors_KnockedSkeletonNonVisible[1], Settings.Colors.PlayerColors_KnockedSkeletonNonVisible[2]))
        : (isVisible ? ImColor(Settings.Colors.PlayerColors_SkeletonVisible[0], Settings.Colors.PlayerColors_SkeletonVisible[1], Settings.Colors.PlayerColors_SkeletonVisible[2]) 
                     : ImColor(Settings.Colors.PlayerColors_SkeletonNonVisible[0], Settings.Colors.PlayerColors_SkeletonNonVisible[1], Settings.Colors.PlayerColors_SkeletonNonVisible[2]));

    ImColor radarColor = isKnocked ? 
        (isVisible ? ImColor(Settings.Colors.PlayerColors_KnockedRadarVisible[0], Settings.Colors.PlayerColors_KnockedRadarVisible[1], Settings.Colors.PlayerColors_KnockedRadarVisible[2]) 
                   : ImColor(Settings.Colors.PlayerColors_KnockedRadarNonVisible[0], Settings.Colors.PlayerColors_KnockedRadarNonVisible[1], Settings.Colors.PlayerColors_KnockedRadarNonVisible[2]))
        : (isVisible ? ImColor(Settings.Colors.PlayerColors_RadarVisible[0], Settings.Colors.PlayerColors_RadarVisible[1], Settings.Colors.PlayerColors_RadarVisible[2]) 
                     : ImColor(Settings.Colors.PlayerColors_RadarNonVisible[0], Settings.Colors.PlayerColors_RadarNonVisible[1], Settings.Colors.PlayerColors_RadarNonVisible[2]));

    // Add player to radar if enabled
    if (RADAR_SETTINGS.enabled) {
        Drawing::AddPlayerToRadar(GameFunctions::GetBoneWithRotation(Actor.Mesh, 0), distance, radarColor);
    }

    // Skip if not on screen
    if (!GameFunctions::isOnScreen(Head2D)) return;

    // Skip if outside range or ESP disabled
    if (!PLAYER_ESP_SETTINGS.enabled || distance >= PLAYER_ESP_SETTINGS.maxDistance) return;

    // Draw visual elements
    if (PLAYER_ESP_SETTINGS.lines.enabled) {
        ImGui::GetBackgroundDrawList()->AddLine(ImVec2(OverlayWidth / 2, 0), ImVec2(Head2D.x, Head2D.y), lineColor, PLAYER_ESP_SETTINGS.lines.thickness);
    }
    
    if (PLAYER_ESP_SETTINGS.box.enabled) {
        if (PLAYER_ESP_SETTINGS.box.type == BoxType::Rounded) {
            float maxRounding = (std::min)(boxWidth, boxHeight) / 2.0f;
            float rounding = (std::min)(PLAYER_ESP_SETTINGS.box.rounding, maxRounding);
            Drawing::DrawBox(BoxX, BoxY, (int)boxWidth, (int)boxHeight, boxColor, PLAYER_ESP_SETTINGS.box.thickness, rounding, boxFillColor);
        } else if (PLAYER_ESP_SETTINGS.box.type == BoxType::Corner) {
            Drawing::DrawCornerBox(Head2D.x - (CornerWidth / 2), Head2D.y, CornerWidth, CornerHeight, boxColor, PLAYER_ESP_SETTINGS.box.thickness, boxFillColor);
        }
    }

    if (PLAYER_ESP_SETTINGS.skeleton.enabled) Drawing::DrawSkeleton(Actor.Mesh, skeletonColor);
    if (PLAYER_ESP_SETTINGS.headCircle.enabled) Drawing::DrawHeadCircle(Actor.Mesh, headCircleColor);
    if (PLAYER_ESP_SETTINGS.info.distance.enabled && !InLobby) Drawing::DrawDistanceText(distance, Bottom2D);
    if (PLAYER_ESP_SETTINGS.info.nickname.enabled || PLAYER_ESP_SETTINGS.info.platform.enabled) Drawing::DrawEspText(Actor.PlayerStates, Actor.Pawn, Head2D);
    if (PLAYER_ESP_SETTINGS.info.rank.enabled || PLAYER_ESP_SETTINGS.info.kills.enabled || PLAYER_ESP_SETTINGS.info.level.enabled) Drawing::DrawEspRankandLevel(Actor.PlayerStates, Actor.Pawn, Head2D);
    if (PLAYER_ESP_SETTINGS.info.weapon.enabled && !InLobby) Drawing::DrawWeaponText(Actor.WeaponName, Bottom2D, Actor.PickColor);

    // Handle aimbot if enabled
    if (AIMBOT_SETTINGS.general.enabled) {
        if (!IsKeyPressed(HOTKEY_SETTINGS.global.holdPrimary.key) && !IsKeyPressed(HOTKEY_SETTINGS.global.holdSecondary.key)) {
            g_CurrentTarget = NULL;
            g_ClosestDistance = FLT_MAX;
        }

        AimbotSpace::HandleAimbot(Actor.Pawn, Actor.Mesh, HeadBone3D, Actor.TargetedPawn, Actor.AcknowledgedPawn, Actor.PlayerState);
        if (g_CurrentTarget && Actor.Pawn == g_CurrentTarget) {
            hasTarget = true;
        }
    }
}

// External function used to call the player visuals system
void DrawEntityList() {
    PlayerVisuals::DrawAllPlayers();
}

// Placeholder for DrawItems until implementation
void PlayerVisuals::DrawItems() {
    LootSystem::DrawItems();
}
