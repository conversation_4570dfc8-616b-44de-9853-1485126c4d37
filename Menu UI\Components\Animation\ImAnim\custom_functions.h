#pragma once

#include "ImVec2Anim.h"
#include "ImVec4Anim.h"

#define IMGUI_DEFINE_MATH_OPERATORS
#include <vector>
#include <string>
#include <map>

#include <cmath>
#include <cstdint>
#include <sstream>
#include <iomanip>

#include "../../../ImGui/imgui_internal.h"
#include "../../../ImGui/imgui.h"
#include "../../../ImGui/imgui_impl_win32.h"
#include "../../../ImGui/imgui_impl_dx11.h"

struct easingv2_state
{
    imanim::ImVec2Anim* anim = nullptr;
    ImVec2 current_vec;
};

inline void EasingAnimationV2(std::string anim_name, ImVec2* current_vec, ImVec2 target_vec, float duration, imanim::EasingCurve::Type type, int loop)
{
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    const ImGuiID id = window->GetID(anim_name.c_str());

    static std::map<ImGuiID, easingv2_state> a;
    auto it_a = a.find(id);

    if (it_a == a.end())
    {
        a.insert({ id, easingv2_state() });
        it_a = a.find(id);
    }

    it_a->second.current_vec = *current_vec;

    if (it_a->second.anim == nullptr)
    {
        it_a->second.anim = new imanim::ImVec2Anim(current_vec);
        it_a->second.anim->setStartValue(it_a->second.current_vec);
        it_a->second.anim->setEndValue(target_vec);
        it_a->second.anim->setDuration(duration);
        it_a->second.anim->setLoopCount(loop);
        it_a->second.anim->setEasingCurve(type);
        it_a->second.anim->start();
    }
    else
    {
        it_a->second.anim->update();
        it_a->second.anim->setStartValue(it_a->second.current_vec);
        it_a->second.anim->setEndValue(target_vec); // ���������� �������� �����
    }
}

struct easingv4_state
{
    imanim::ImVec4Anim* anim = nullptr;
    ImVec4 current_vec;
};

inline void EasingAnimationV4(std::string anim_name, ImVec4* current_vec, ImVec4 target_vec, float duration, imanim::EasingCurve::Type type, int loop)
{
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    const ImGuiID id = window->GetID(anim_name.c_str());

    static std::map<ImGuiID, easingv4_state> a;
    auto it_a = a.find(id);

    if (it_a == a.end())
    {
        a.insert({ id, easingv4_state() });
        it_a = a.find(id);
    }

    it_a->second.current_vec = *current_vec;

    if (it_a->second.anim == nullptr)
    {
        it_a->second.anim = new imanim::ImVec4Anim(current_vec);
        it_a->second.anim->setStartValue(it_a->second.current_vec);
        it_a->second.anim->setEndValue(target_vec);
        it_a->second.anim->setDuration(duration);
        it_a->second.anim->setLoopCount(loop);
        it_a->second.anim->setEasingCurve(type);
        it_a->second.anim->start();
    }
    else
    {
        it_a->second.anim->update();
        it_a->second.anim->setStartValue(it_a->second.current_vec);
        it_a->second.anim->setEndValue(target_vec); // ���������� �������� �����
    }
}
