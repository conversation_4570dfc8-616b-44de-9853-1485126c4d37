#pragma once
#include "../Components/nav_elements.h"
#include "../../Cheat Core/Settings/Settings.h"

// Helper function to create color states for aimbot features
inline std::vector<nav_elements::ColorState> GetAimbotColorStates(const char* featureName) {
    std::vector<nav_elements::ColorState> colorStates;
    
    // FOV feature
    if (strcmp(featureName, "Draw FOV") == 0) {
        colorStates.push_back({ "FOV", { Settings.Colors.AimbotColorsFov[0], Settings.Colors.AimbotColorsFov[1], Settings.Colors.AimbotColorsFov[2], 1.0f }, Settings.Colors.AimbotColorsFov });
        return colorStates;
    }
    
    // Crosshair feature
    if (strcmp(featureName, "Draw Crosshair") == 0) {
        colorStates.push_back({ "Crosshair", { Settings.Colors.AimbotColorsCrossHair[0], Settings.Colors.AimbotColorsCrossHair[1], Settings.Colors.AimbotColorsCrossHair[2], 1.0f }, Settings.Colors.AimbotColorsCrossHair });
        return colorStates;
    }
    
    // Target line feature
    if (strcmp(featureName, "Draw Target Line") == 0) {
        colorStates.push_back({ "Target", { Settings.Colors.AimbotColorsTarget[0], Settings.Colors.AimbotColorsTarget[1], Settings.Colors.AimbotColorsTarget[2], 1.0f }, Settings.Colors.AimbotColorsTarget });
        return colorStates;
    }
    
    // Return empty vector for non-visual features
    return colorStates;
}

// Helper function to create color states for player ESP features
inline std::vector<nav_elements::ColorState> GetPlayerColorStates(const char* featureName) {
    std::vector<nav_elements::ColorState> colorStates;
    
    // Box feature
    if (strcmp(featureName, "Bounding Box") == 0 || strcmp(featureName, "Corner Box") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors_BoxVisible[0], Settings.Colors.PlayerColors_BoxVisible[1], Settings.Colors.PlayerColors_BoxVisible[2], Settings.Colors.PlayerColors_BoxVisible[3] }, Settings.Colors.PlayerColors_BoxVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors_BoxNonVisible[0], Settings.Colors.PlayerColors_BoxNonVisible[1], Settings.Colors.PlayerColors_BoxNonVisible[2], Settings.Colors.PlayerColors_BoxNonVisible[3] }, Settings.Colors.PlayerColors_BoxNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors_KnockedBoxVisible[0], Settings.Colors.PlayerColors_KnockedBoxVisible[1], Settings.Colors.PlayerColors_KnockedBoxVisible[2], Settings.Colors.PlayerColors_KnockedBoxVisible[3] }, Settings.Colors.PlayerColors_KnockedBoxVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors_KnockedBoxNonVisible[0], Settings.Colors.PlayerColors_KnockedBoxNonVisible[1], Settings.Colors.PlayerColors_KnockedBoxNonVisible[2], Settings.Colors.PlayerColors_KnockedBoxNonVisible[3] }, Settings.Colors.PlayerColors_KnockedBoxNonVisible });
        return colorStates;
    }
    
    // Skeleton feature
    if (strcmp(featureName, "Skeleton") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors_SkeletonVisible[0], Settings.Colors.PlayerColors_SkeletonVisible[1], Settings.Colors.PlayerColors_SkeletonVisible[2], 1.0f }, Settings.Colors.PlayerColors_SkeletonVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors_SkeletonNonVisible[0], Settings.Colors.PlayerColors_SkeletonNonVisible[1], Settings.Colors.PlayerColors_SkeletonNonVisible[2], 1.0f }, Settings.Colors.PlayerColors_SkeletonNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors_KnockedSkeletonVisible[0], Settings.Colors.PlayerColors_KnockedSkeletonVisible[1], Settings.Colors.PlayerColors_KnockedSkeletonVisible[2], 1.0f }, Settings.Colors.PlayerColors_KnockedSkeletonVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors_KnockedSkeletonNonVisible[0], Settings.Colors.PlayerColors_KnockedSkeletonNonVisible[1], Settings.Colors.PlayerColors_KnockedSkeletonNonVisible[2], 1.0f }, Settings.Colors.PlayerColors_KnockedSkeletonNonVisible });
        return colorStates;
    }
    
    // Head Circle feature
    if (strcmp(featureName, "Head Circle") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors_HeadVisible[0], Settings.Colors.PlayerColors_HeadVisible[1], Settings.Colors.PlayerColors_HeadVisible[2], 1.0f }, Settings.Colors.PlayerColors_HeadVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors_HeadNonVisible[0], Settings.Colors.PlayerColors_HeadNonVisible[1], Settings.Colors.PlayerColors_HeadNonVisible[2], 1.0f }, Settings.Colors.PlayerColors_HeadNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors_KnockedHeadVisible[0], Settings.Colors.PlayerColors_KnockedHeadVisible[1], Settings.Colors.PlayerColors_KnockedHeadVisible[2], 1.0f }, Settings.Colors.PlayerColors_KnockedHeadVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors_KnockedHeadNonVisible[0], Settings.Colors.PlayerColors_KnockedHeadNonVisible[1], Settings.Colors.PlayerColors_KnockedHeadNonVisible[2], 1.0f }, Settings.Colors.PlayerColors_KnockedHeadNonVisible });
        return colorStates;
    }
    
    // Snapline feature
    if (strcmp(featureName, "Snapline") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors_LineVisible[0], Settings.Colors.PlayerColors_LineVisible[1], Settings.Colors.PlayerColors_LineVisible[2], 1.0f }, Settings.Colors.PlayerColors_LineVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors_LineNonVisible[0], Settings.Colors.PlayerColors_LineNonVisible[1], Settings.Colors.PlayerColors_LineNonVisible[2], 1.0f }, Settings.Colors.PlayerColors_LineNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors_KnockedLineVisible[0], Settings.Colors.PlayerColors_KnockedLineVisible[1], Settings.Colors.PlayerColors_KnockedLineVisible[2], 1.0f }, Settings.Colors.PlayerColors_KnockedLineVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors_KnockedLineNonVisible[0], Settings.Colors.PlayerColors_KnockedLineNonVisible[1], Settings.Colors.PlayerColors_KnockedLineNonVisible[2], 1.0f }, Settings.Colors.PlayerColors_KnockedLineNonVisible });
        return colorStates;
    }
    
    // Distance feature
    if (strcmp(featureName, "Distance") == 0) {
        colorStates.push_back({ "Distance", { Settings.Colors.PlayerColors_BoxVisible[0], Settings.Colors.PlayerColors_BoxVisible[1], Settings.Colors.PlayerColors_BoxVisible[2], 1.0f }, Settings.Colors.PlayerColors_BoxVisible });
        return colorStates;
    }
    
    // Return empty vector for non-visual features
    return colorStates;
}

// Helper function to create color states for radar features
inline std::vector<nav_elements::ColorState> GetRadarColorStates(const char* featureName) {
    std::vector<nav_elements::ColorState> colorStates;
    
    // Radar feature
    if (strcmp(featureName, "Enable Radar") == 0) {
        colorStates.push_back({ "Visible", { Settings.Colors.PlayerColors_RadarVisible[0], Settings.Colors.PlayerColors_RadarVisible[1], Settings.Colors.PlayerColors_RadarVisible[2], 1.0f }, Settings.Colors.PlayerColors_RadarVisible });
        colorStates.push_back({ "Non-Visible", { Settings.Colors.PlayerColors_RadarNonVisible[0], Settings.Colors.PlayerColors_RadarNonVisible[1], Settings.Colors.PlayerColors_RadarNonVisible[2], 1.0f }, Settings.Colors.PlayerColors_RadarNonVisible });
        colorStates.push_back({ "Knocked Visible", { Settings.Colors.PlayerColors_KnockedRadarVisible[0], Settings.Colors.PlayerColors_KnockedRadarVisible[1], Settings.Colors.PlayerColors_KnockedRadarVisible[2], 1.0f }, Settings.Colors.PlayerColors_KnockedRadarVisible });
        colorStates.push_back({ "Knocked Non-Visible", { Settings.Colors.PlayerColors_KnockedRadarNonVisible[0], Settings.Colors.PlayerColors_KnockedRadarNonVisible[1], Settings.Colors.PlayerColors_KnockedRadarNonVisible[2], 1.0f }, Settings.Colors.PlayerColors_KnockedRadarNonVisible });
        return colorStates;
    }
    
    // Return empty vector for non-visual features
    return colorStates;
}
