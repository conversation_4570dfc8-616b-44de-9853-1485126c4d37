#pragma once
#include <Windows.h>
#include <d3d11.h>
#include <string>
#include <functional>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../ImGui/imgui.h"
#include "../ImGui/imgui_internal.h"
#include "../Components/Components.h"
#include "../../Cheat Core/Settings/Settings.h"
#include "../../Cheat Core/Features/Config/Config.h"

// Uncomment this line to disable animations temporarily if you get linker errors
// #define DISABLE_ANIMATIONS

#ifndef DISABLE_ANIMATIONS
#include "AnimationSystem.h"
#endif

extern float accent_color[4];

class CGui
{
public:
    void Initialize(ID3D11Device* g_pd3dDevice);
    void Render(bool& ShowMenu);

    // Different menu tabs
    void RenderMainTab();
    void RenderAimbotTab();
    void RenderVisualsTab();
    void RenderRadarTab();
    void RenderSettingsTab();
    void RenderConfigsTab();
    void RenderConfigPopup(); // Robust config popup system
    void RenderProfileWindow(); // Profile window for top-right corner
    void RenderHeaderWindow(); // Header window above main menu
    // Loot category tabs
    void RenderConsumablesTab();
    void RenderWeaponsTab();
    void RenderAmmoTab();
    void RenderOtherItemsTab();

    // Animation helpers - legacy (keeping for compatibility)
    float EaseOutExpo(float x);
    float EaseInOutQuad(float x);
    float EaseInOutCubic(float x);
    float EaseOutBack(float x);
    bool AnimateNextFeature();
    void RenderIOSWaterAnimation(float progress, ImVec2 contentSize);
    void AnimateContentElement(float delay = 0.0f);
    
    // Tab content helpers
    const char* GetTabName(int tabIndex);
    const char* GetTabDescription(int tabIndex);
    void RenderTabTransition(float progress, ImVec2 contentSize);
    void RenderGlowingStars(ImVec2 contentSize);
    
    // Gradient UI helpers
    void RenderGradientText(const char* text, ImVec2 pos, ImColor startColor, ImColor endColor, ImFont* font = nullptr);
    void DrawGradientRect(ImRect rect, ImU32 color1, ImU32 color2, float thickness);
    void RenderGradientSeparator(float y_offset, float width_percentage);
    void RenderSectionHeader(const char* title, float titleY = 0, float separatorY = 0);

    // New animation integration methods
    void InitializeAnimations();
    void UpdateAnimations();
    void StartMenuShowAnimation();
    void StartMenuHideAnimation();
    void StartTabSwitchAnimation(int fromTab, int toTab);
    void StartContentFadeIn();
    void RenderAnimatedElement(int elementIndex, std::function<void()> renderFunction);
    
    // Animated popup handling
    void ShowAnimatedPopup(const char* popupId);
    void HideAnimatedPopup(const char* popupId);
    bool BeginAnimatedWindow(const char* name, bool* p_open, ImGuiWindowFlags flags = 0);
    void EndAnimatedWindow();
    
    // Content animation helpers
    void BeginAnimatedContent();
    void EndAnimatedContent();
    bool ShouldSkipElement(int elementIndex);
    void ApplyElementAnimation(int elementIndex);

    // Tab state - maps to ModernUI::NavigationPanel::TabIndex
    int currentTab = 0;
    int previousTab = 0;

    // Animation states (legacy - now integrated with AnimationSystem)
    float menuAlpha = 0.0f;
    float contentAlpha = 0.0f;
    float tabTransitionProgress = 1.0f;
    bool isMenuOpen = false;
    float animationSpeed = 0.15f;
    float lastTabChangeTime = 0.0f;
    float featureStartTime = 0.0f;
    int featureIndex = 0;
    
    // New animation integration
    bool animationsInitialized = false;
    bool isMenuAnimating = false;
    bool isTabSwitching = false;
    int contentElementCount = 0;
    float lastMenuToggleTime = 0.0f;

private:
 
};

extern CGui pGUi; 