#include "Drawing.h"
#include "../../../GameClass/GameSettings.h"


void Drawing::DrawHeadCircle(uintptr_t mesh, ImU32 visible_color) {
	if (!PLAYER_ESP_SETTINGS.headCircle.enabled)
		return;

	// Assume GetBoneWithRotation and ProjectWorldToScreen are available to get screen positions of bones
	Vector3 headPosition3D = GameFunctions::GetBoneWithRotation(mesh, 110); // Index for the head bone
	Vector3 neckPosition3D = GameFunctions::GetBoneWithRotation(mesh, 67);  // Index for the neck bone

	Vector2 headPosition2D = GameFunctions::ProjectWorldToScreen(headPosition3D);
	Vector2 neckPosition2D = GameFunctions::ProjectWorldToScreen(neckPosition3D);

	// Calculate radius based on the vertical distance between head and neck
	float radius = std::abs(headPosition2D.y - neckPosition2D.y) + 0.5f;

	// Adjust the center position of the circle so that the bottom aligns with the top of the head
	ImVec2 circleCenter(headPosition2D.x, headPosition2D.y - radius);
	//ImGui::GetBackgroundDrawList()->AddCircleFilled(ImVec2(headPosition2D.x, headPosition2D.y), radius, visible_color);

	// Draw the circle with the adjusted center and radius
	ImGui::GetBackgroundDrawList()->AddCircle(circleCenter, radius, visible_color, 70, PLAYER_ESP_SETTINGS.headCircle.thickness);
}

void Drawing::DrawSkeleton(uintptr_t Mesh, ImColor SkeleColor) {

	Vector2 Head2D, Neck2D, Pelvis2D, RightShoulder2D, RightElbow2D, RightWrist2D,
		RightHip2D, RightKnee2D, RightAnkle2D, LeftShoulder2D, LeftElbow2D, LeftWrist2D,
		LeftHip2D, LeftKnee2D, LeftAnkle2D, Bottom2D;

	Vector3 Head3D = GameFunctions::GetBoneWithRotation(Mesh, 110);
	Head2D = GameFunctions::ProjectWorldToScreen(Head3D);

	Vector3 Neck3D = GameFunctions::GetBoneWithRotation(Mesh, 67);
	Neck2D = GameFunctions::ProjectWorldToScreen(Neck3D);

	Vector3 Pelvis3D = GameFunctions::GetBoneWithRotation(Mesh, 2);
	Pelvis2D = GameFunctions::ProjectWorldToScreen(Pelvis3D);

	Vector3 RightShoulder3D = GameFunctions::GetBoneWithRotation(Mesh, 9);
	RightShoulder2D = GameFunctions::ProjectWorldToScreen(RightShoulder3D);

	Vector3 RightElbow3D = GameFunctions::GetBoneWithRotation(Mesh, 10); // 10
	RightElbow2D = GameFunctions::ProjectWorldToScreen(RightElbow3D);

	Vector3 RightWrist3D = GameFunctions::GetBoneWithRotation(Mesh, 90);
	RightWrist2D = GameFunctions::ProjectWorldToScreen(RightWrist3D);

	Vector3 RightHip3D = GameFunctions::GetBoneWithRotation(Mesh, 71);
	RightHip2D = GameFunctions::ProjectWorldToScreen(RightHip3D);

	Vector3 RightKnee3D = GameFunctions::GetBoneWithRotation(Mesh, 72);
	RightKnee2D = GameFunctions::ProjectWorldToScreen(RightKnee3D);

	Vector3 RightAnkle3D = GameFunctions::GetBoneWithRotation(Mesh, 75);
	RightAnkle2D = GameFunctions::ProjectWorldToScreen(RightAnkle3D);

	Vector3 LeftShoulder3D = GameFunctions::GetBoneWithRotation(Mesh, 38);
	LeftShoulder2D = GameFunctions::ProjectWorldToScreen(LeftShoulder3D);

	Vector3 LeftElbow3D = GameFunctions::GetBoneWithRotation(Mesh, 39); // 105 and 106
	LeftElbow2D = GameFunctions::ProjectWorldToScreen(LeftElbow3D);

	Vector3 LeftWrist3D = GameFunctions::GetBoneWithRotation(Mesh, 40); // 107 and 112
	LeftWrist2D = GameFunctions::ProjectWorldToScreen(LeftWrist3D);

	Vector3 LeftHip3D = GameFunctions::GetBoneWithRotation(Mesh, 78);
	LeftHip2D = GameFunctions::ProjectWorldToScreen(LeftHip3D);

	Vector3 LeftKnee3D = GameFunctions::GetBoneWithRotation(Mesh, 79);
	LeftKnee2D = GameFunctions::ProjectWorldToScreen(LeftKnee3D);

	Vector3 LeftAnkle3D = GameFunctions::GetBoneWithRotation(Mesh, 82);
	LeftAnkle2D = GameFunctions::ProjectWorldToScreen(LeftAnkle3D);


	Vector3 Bottom3D = GameFunctions::GetBoneWithRotation(Mesh, 0);
	Bottom2D = GameFunctions::ProjectWorldToScreen(Bottom3D);

	float thickness = PLAYER_ESP_SETTINGS.skeleton.thickness;
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(Head2D.x, Head2D.y), ImVec2(Neck2D.x, Neck2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(Neck2D.x, Neck2D.y), ImVec2(Pelvis2D.x, Pelvis2D.y), SkeleColor, thickness);

	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(Neck2D.x, Neck2D.y), ImVec2(RightShoulder2D.x, RightShoulder2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(RightShoulder2D.x, RightShoulder2D.y), ImVec2(RightElbow2D.x, RightElbow2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(RightElbow2D.x, RightElbow2D.y), ImVec2(RightWrist2D.x, RightWrist2D.y), SkeleColor, thickness);

	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(Neck2D.x, Neck2D.y), ImVec2(LeftShoulder2D.x, LeftShoulder2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(LeftShoulder2D.x, LeftShoulder2D.y), ImVec2(LeftElbow2D.x, LeftElbow2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(LeftElbow2D.x, LeftElbow2D.y), ImVec2(LeftWrist2D.x, LeftWrist2D.y), SkeleColor, thickness);

	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(Pelvis2D.x, Pelvis2D.y), ImVec2(RightHip2D.x, RightHip2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(RightHip2D.x, RightHip2D.y), ImVec2(RightKnee2D.x, RightKnee2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(RightKnee2D.x, RightKnee2D.y), ImVec2(RightAnkle2D.x, RightAnkle2D.y), SkeleColor, thickness);

	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(Pelvis2D.x, Pelvis2D.y), ImVec2(LeftHip2D.x, LeftHip2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(LeftHip2D.x, LeftHip2D.y), ImVec2(LeftKnee2D.x, LeftKnee2D.y), SkeleColor, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(LeftKnee2D.x, LeftKnee2D.y), ImVec2(LeftAnkle2D.x, LeftAnkle2D.y), SkeleColor, thickness);
}

void Drawing::DrawRadar(float x, float y, float CirleSize, float RectangleSize) {
	if (RADAR_SETTINGS.type == RadarType::Circle) {
		ImGui::SetNextWindowPos(ImVec2(0, 0), ImGuiCond_Once);
		ImGui::SetNextWindowSize(ImGui::GetIO().DisplaySize, ImGuiCond_Once);

		static const auto flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse | ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoBackground;
		ImGui::Begin(("##radar"), nullptr, flags);

		ImDrawList* drawList = ImGui::GetWindowDrawList();
		drawList->AddCircleFilled(ImVec2(x + CirleSize / 2, y + CirleSize / 2), CirleSize, IM_COL32(23, 17, 13, 100), 1000);
		drawList->AddCircleFilled(ImVec2(x + CirleSize / 2, y + CirleSize / 2), 5, IM_COL32(81, 65, 55, 255), 1000);
		drawList->AddCircle(ImVec2(x + CirleSize / 2, y + CirleSize / 2), CirleSize, IM_COL32(25, 25, 25, 255), 1000, 3);

		ImGui::End();
	}
	else if (RADAR_SETTINGS.type == RadarType::Rectangle) {
		ImVec2 midRadar = ImVec2(x + (RectangleSize / 2), y + (RectangleSize / 2));
		ImGui::GetForegroundDrawList()->AddRectFilled(ImVec2(x, y), ImVec2(x + RectangleSize, y + RectangleSize), IM_COL32(0, 0, 0, 45));
		ImGui::GetForegroundDrawList()->AddRect(ImVec2(x, y), ImVec2(x + RectangleSize, y + RectangleSize), IM_COL32(0, 0, 0, 125));
		ImGui::GetForegroundDrawList()->AddLine(ImVec2(midRadar.x, y), ImVec2(midRadar.x, y + RectangleSize), IM_COL32(255, 255, 255, 90));
		ImGui::GetForegroundDrawList()->AddLine(ImVec2(x, midRadar.y), ImVec2(x + RectangleSize, midRadar.y), IM_COL32(255, 255, 255, 90));
	}
}

void Drawing::DrawWeaponText(std::string weaponName, const Vector2& Bottom2D, ImColor pickcolor) {
	// Convert wide string to UTF-8
	std::string utf8_weapon = weaponName;

	//ImGui::PushFont((ImFont*)FortniteFont);
	ImFont* font = ImGui::GetFont();
	float fontSize = PLAYER_ESP_SETTINGS.info.weapon.font.size;

	// Use UTF-8 string for size calculation
	ImVec2 textSize = font->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, utf8_weapon.c_str());
	ImColor textColor = ImColor(255, 255, 255);
	ImColor outlineColor = ImColor(0, 0, 0);
	ImVec2 textPos((Bottom2D.x) - (textSize.x / 2), (Bottom2D.y + 17));

	if (PLAYER_ESP_SETTINGS.info.weaponRarity && !InLobby) {
		ImVec2 offsets[] = { {-1, -1}, {-1, 0}, {-1, 1}, {0, -1}, {0, 1}, {1, -1}, {1, 0}, {1, 1} };
		for (auto& offset : offsets) {
			ImVec2 outlinePos = ImVec2(textPos.x + offset.x, textPos.y + offset.y);
			// Use UTF-8 string for rendering
			ImGui::GetBackgroundDrawList()->AddText(font, fontSize, outlinePos, pickcolor, utf8_weapon.c_str());
		}
		ImGui::GetBackgroundDrawList()->AddText(font, fontSize, textPos, textColor, utf8_weapon.c_str());
	}
	else {
		ImVec2 offsets[] = { {-1, -1}, {-1, 0}, {-1, 1}, {0, -1}, {0, 1}, {1, -1}, {1, 0}, {1, 1} };
		for (auto& offset : offsets) {
			ImVec2 outlinePos = ImVec2(textPos.x + offset.x, textPos.y + offset.y);
			ImGui::GetBackgroundDrawList()->AddText(font, fontSize, outlinePos, outlineColor, utf8_weapon.c_str());
		}
		ImGui::GetBackgroundDrawList()->AddText(font, fontSize, textPos, textColor, utf8_weapon.c_str());
	}
	//ImGui::PopFont();
}
void Drawing::DrawDistanceText(float distance, const Vector2& Bottom2D) {
	//ImGui::PushFont((ImFont*)FortniteFont);
	std::string distanceText = "[" + std::to_string(static_cast<int>(distance)) + "m]";
	ImFont* font = ImGui::GetFont();
	float fontSize = PLAYER_ESP_SETTINGS.info.distance.font.size;

	ImVec2 textSize = font->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, distanceText.c_str());
	ImVec2 textPos((Bottom2D.x) - (textSize.x / 2), (Bottom2D.y + 3));
	ImColor textColor = ImColor(255, 255, 255);
	ImColor outlineColor = ImColor(0, 0, 0);
	ImVec2 offsets[] = { {-1, -1}, {-1, 0}, {-1, 1}, {0, -1}, {0, 1}, {1, -1}, {1, 0}, {1, 1} };

	for (auto& offset : offsets) {
		ImVec2 outlinePos = ImVec2(textPos.x + offset.x, textPos.y + offset.y);
		ImGui::GetBackgroundDrawList()->AddText(font, fontSize, outlinePos, outlineColor, distanceText.c_str());
	}
	ImGui::GetBackgroundDrawList()->AddText(font, fontSize, textPos, textColor, distanceText.c_str());
	//ImGui::PopFont();

}

void Drawing::DrawLine(ImVec2 a, ImVec2 b, ImColor color, float width)
{
	ImGui::GetWindowDrawList()->AddLine(a, b, color, width);

}

void Drawing::DrawCrosshair(const ImVec2& center) {
	auto& vis = AIMBOT_SETTINGS.general.visuals;
	ImColor color(vis.crosshairColor.r, vis.crosshairColor.g, vis.crosshairColor.b);

	// ImGui draw list
	ImDrawList* drawList = ImGui::GetBackgroundDrawList();

	switch (vis.crosshairType) {
	case CrosshairType::Cross: {
		drawList->AddLine(ImVec2(center.x - vis.drawSize, center.y), ImVec2(center.x + vis.drawSize, center.y), color, vis.drawThickness);
		drawList->AddLine(ImVec2(center.x, center.y - vis.drawSize), ImVec2(center.x, center.y + vis.drawSize), color, vis.drawThickness);
		break;
	}
	case CrosshairType::Circle: {
		drawList->AddCircle(center, vis.drawSize, color, 64, vis.drawThickness);
		drawList->AddLine(ImVec2(center.x - vis.drawSize, center.y), ImVec2(center.x - vis.drawSize / 2, center.y), color, vis.drawThickness);
		drawList->AddLine(ImVec2(center.x + vis.drawSize / 2, center.y), ImVec2(center.x + vis.drawSize, center.y), color, vis.drawThickness);
		drawList->AddLine(ImVec2(center.x, center.y - vis.drawSize), ImVec2(center.x, center.y - vis.drawSize / 2), color, vis.drawThickness);
		drawList->AddLine(ImVec2(center.x, center.y + vis.drawSize / 2), ImVec2(center.x, center.y + vis.drawSize), color, vis.drawThickness);
		// center dot
		drawList->AddCircleFilled(center, vis.drawThickness, color);
		break;
	}
	case CrosshairType::Dot: {
		drawList->AddCircleFilled(center, vis.drawThickness, color);
		break;
	}
	default:
		break;
	}
}
void Drawing::DrawFovCircle(float r, bool filled, bool rainbow, bool toMouse) {
	auto& io = ImGui::GetIO();

	ImVec2 center = toMouse ? ImVec2(io.MousePos.x, io.MousePos.y) : ImVec2(io.DisplaySize.x * 0.5f, io.DisplaySize.y * 0.5f);
	auto drawList = ImGui::GetBackgroundDrawList();
	int sides = 100;
	for (int i = 0; i < sides; ++i) {
		auto pos = center;
		float angle = (i / static_cast<float>(sides)) * 2 * M_PI;
		auto lastPos = ImVec2(pos.x + cos(angle) * r, pos.y + sin(angle) * r);
		auto nextPos = ImVec2(pos.x + cos(angle + 2 * M_PI / sides) * r, pos.y + sin(angle + 2 * M_PI / sides) * r);

		ImU32 currentColor = 0;
		if (rainbow) {
			currentColor = ImGui::ColorConvertFloat4ToU32(ImColor::HSV((fmod(ImGui::GetTime(), 5.0f) / 5.0f - i / static_cast<float>(sides)) + 1.0f, 0.5f, 1.0f));
		} else {
			auto fovColor = AIMBOT_SETTINGS.general.visuals.fovColor;
			currentColor = ImGui::ColorConvertFloat4ToU32(ImColor(fovColor.r, fovColor.g, fovColor.b));
		}

		ImU32 fillCol = filled ? ImGui::ColorConvertFloat4ToU32({ ImGui::ColorConvertU32ToFloat4(currentColor).x, ImGui::ColorConvertU32ToFloat4(currentColor).y, ImGui::ColorConvertU32ToFloat4(currentColor).z, 0.2f }) : 0; // 0.2f = fill opacity

		if (AIMBOT_SETTINGS.general.visuals.drawFovFilled)
		{
			ImVec2 triangle[3] = { lastPos, nextPos, center };
			drawList->AddConvexPolyFilled(triangle, 3, fillCol); // fill
		}
		if (AIMBOT_SETTINGS.general.visuals.drawFovOutline)
		{
			drawList->AddLine(lastPos, nextPos, IM_COL32(0, 0, 0, 255), 4.f); // outline 
		}
		drawList->AddLine(lastPos, nextPos, currentColor, 2.f); // main 
	}
}


void Drawing::DrawCornerBox(int X, int Y, int W, int H, const ImColor color, int thickness, ImColor fillcolor) {
	float lineW = (W / 3);
	float lineH = (H / 3);
	ImGui::GetBackgroundDrawList()->AddRectFilled(ImVec2(X, Y), ImVec2(X + W, Y + H), fillcolor);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(X, Y), ImVec2(X, Y + lineH), color, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(X, Y), ImVec2(X + lineW, Y), color, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(X + W - lineW, Y), ImVec2(X + W, Y), color, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(X + W, Y), ImVec2(X + W, Y + lineH), color, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(X, Y + H - lineH), ImVec2(X, Y + H), color, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(X, Y + H), ImVec2(X + lineW, Y + H), color, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(X + W - lineW, Y + H), ImVec2(X + W, Y + H), color, thickness);
	ImGui::GetBackgroundDrawList()->AddLine(ImVec2(X + W, Y + H - lineH), ImVec2(X + W, Y + H), color, thickness);
}

void Drawing::DrawBox(int x, int y, int w, int h, ImColor color, float width, float rounding, ImColor fillcolor)
{
	// Ensure rounding does not exceed half the size of the smallest dimension
	float maxRounding = (std::min)(w, h) / 2.0f;
	rounding = (std::min)(rounding, maxRounding);

	// Border
	ImGui::GetBackgroundDrawList()->AddRect(ImVec2(x, y), ImVec2(x + w, y + h), color, rounding, ImDrawFlags_RoundCornersAll , width);
	// Fill if enabled
	if (PLAYER_ESP_SETTINGS.box.filled) {
		ImGui::GetBackgroundDrawList()->AddRectFilled(ImVec2(x, y), ImVec2(x + w, y + h), fillcolor, rounding);
	}

}

void Drawing::DrawOutlinedText(const std::string& text, const ImVec2& pos, ImColor textColor, ImColor outlineColor) {
	ImFont* font = ImGui::GetFont();
	float fontSize = PLAYER_ESP_SETTINGS.info.distance.font.size;
	
	ImVec2 offsets[] = { {-1, -1}, {-1, 0}, {-1, 1}, {0, -1}, {0, 1}, {1, -1}, {1, 0}, {1, 1} };
	for (auto& offset : offsets) {
		ImVec2 outlinePos = ImVec2(pos.x + offset.x, pos.y + offset.y);
		ImGui::GetBackgroundDrawList()->AddText(font, fontSize, outlinePos, outlineColor, text.c_str());
	}
	ImGui::GetBackgroundDrawList()->AddText(font, fontSize, pos, textColor, text.c_str());
}

std::string WStringToUTF8(const std::wstring& wstr) {
	if (wstr.empty()) return "";
	int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
	std::string str(size_needed, 0);
	WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &str[0], size_needed, NULL, NULL);
	return str;
}

std::string DecryptPlayerName(uintptr_t PlayerState, uintptr_t Actor) {
	// Implement player name decryption based on your game's memory structure
	// This is a placeholder and should be replaced with actual implementation
	return "Player";
}

std::string GetPlatformText(uintptr_t PlayerState) {
	// Implement platform detection based on your game's memory structure
	// This is a placeholder and should be replaced with actual implementation
	return "PC";
}

ImColor GetColorByPlatform(const std::string& platform) {
	if (platform == "PC") return ImColor(255, 255, 255);
	if (platform == "Xbox") return ImColor(0, 255, 0);
	if (platform == "PlayStation") return ImColor(0, 0, 255);
	if (platform == "Switch") return ImColor(255, 0, 0);
	if (platform == "Mobile") return ImColor(255, 255, 0);
	return ImColor(255, 255, 255);
}

int GetPlayerKills(uintptr_t PlayerState) {
	// Implement kill count retrieval based on your game's memory structure
	// This is a placeholder and should be replaced with actual implementation
	return 0;
}

int GetPlayerLevel(uintptr_t PlayerState) {
	// Implement player level retrieval based on your game's memory structure
	// This is a placeholder and should be replaced with actual implementation
	return 1;
}

std::string GetRankByProgress(uint32_t rank) {
	// Implement rank text based on rank value
	// This is a placeholder and should be replaced with actual implementation
	return "Unranked";
}


void Drawing::DrawEspText(uintptr_t PlayerState, uintptr_t Actor, const Vector2& Head2D) {
	std::string text;
	
	// Decrypt and retrieve player data
	std::string username = DecryptPlayerName(PlayerState, Actor);
	std::string platform = GetPlatformText(PlayerState);
	ImColor PlatformColor = GetColorByPlatform(platform);

	// Construct text based on user settings
	if (PLAYER_ESP_SETTINGS.info.nickname.enabled && PLAYER_ESP_SETTINGS.info.platform.enabled && !InLobby) {
		text = platform.empty() ? username : username + " - [" + platform + "]";
	}
	else if (PLAYER_ESP_SETTINGS.info.nickname.enabled && !InLobby) {
		text = username;
	}
	else if (PLAYER_ESP_SETTINGS.info.platform.enabled && !InLobby) {
		text = platform;
	}

	// Render text if not empty
	if (!text.empty()) {
		ImVec2 textPos = ImVec2(Head2D.x, Head2D.y - 20);
		ImColor textColor = ImColor(255, 255, 255);
		ImColor outlineColor = ImColor(0, 0, 0);
		DrawOutlinedText(text, textPos, textColor, outlineColor);
	}
}

void Drawing::DrawEspRankandLevel(uintptr_t PlayerState, uintptr_t Actor, const Vector2& Head2D) {
	std::string text;
	ImColor textColor;
	
	// Retrieve player rank, level, and kills
	uintptr_t HabaneroComponent = UseDriver::read<uintptr_t>(PlayerState + Offsets::HabaneroComponent);
	uint32_t Rank = UseDriver::read<uint32_t>(HabaneroComponent + Offsets::Rank + 0x10);
	int kills = GetPlayerKills(PlayerState);
	int Level = GetPlayerLevel(PlayerState);

	// Construct text components
	if ((PLAYER_ESP_SETTINGS.info.level.enabled && !InLobby) || (PLAYER_ESP_SETTINGS.info.kills.enabled && !InLobby) || (PLAYER_ESP_SETTINGS.info.rank.enabled && !InLobby)) {
		if (PLAYER_ESP_SETTINGS.info.level.enabled) {
			text += "Level: " + std::to_string(Level);
		}
		if (PLAYER_ESP_SETTINGS.info.kills.enabled) {
			if (!text.empty()) text += " - ";
			text += "Kills: " + std::to_string(kills);
		}
		if (PLAYER_ESP_SETTINGS.info.rank.enabled) {
			if (!text.empty()) text += " - ";
			text += GetRankByProgress(Rank);
		}
	}

	// Render combined text if available
	if (!text.empty()) {
		ImVec2 textPos = ImVec2(Head2D.x, Head2D.y - 40);
		textColor = ImColor(255, 255, 255); // Default text color
		ImColor outlineColor = ImColor(0, 0, 0); // Default outline color
		DrawOutlinedText(text, textPos, textColor, outlineColor);
	}
}

void Drawing::AddPlayerToRadar(Vector3 WorldLocation, float distance, ImColor radar_visible_color) {
	static const auto flags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse | ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoBackground;
	ImGui::Begin(("##radar"), nullptr, flags);

	int ScreenX = 0, ScreenY = 0;
	const auto& drawList = ImGui::GetWindowDrawList();

	// Function to convert world position to radar position
	auto ConvertToRadarPos = [&](const Vector3& worldPos, int& outScreenX, int& outScreenY) {
		// Get player's position
		Vector3 playerOrigin = vCamera.Location;
		Vector3 playerRotation = vCamera.Rotation;

		// Calculate distance between player and target
		Vector3 diff = worldPos - playerOrigin;

		// Convert to radar coordinates
		float distance = (float)sqrt(diff.x * diff.x + diff.y * diff.y);
		float angle = (float)atan2(diff.y, diff.x) - playerRotation.y * 3.14159265358979323846f / 180.0f;

		// Set radar center position
		float rad_x = RADAR_SETTINGS.positionX + RADAR_SETTINGS.circleSize / 2;
		float rad_y = RADAR_SETTINGS.positionY + RADAR_SETTINGS.circleSize / 2;

		// Convert position to radar coordinates
		float radar_radius = RADAR_SETTINGS.circleSize / 2 - 5;
		float radar_scale = radar_radius / RADAR_SETTINGS.maxDistance;

		float radar_dist = (std::min)(distance * radar_scale, radar_radius);
		float x = rad_x + radar_dist * (float)cos(angle);
		float y = rad_y + radar_dist * (float)sin(angle);

		// Clamp to radar circle
		if (x < rad_x - radar_radius) x = rad_x - radar_radius;
		if (x > rad_x + radar_radius) x = rad_x + radar_radius;
		if (y < rad_y - radar_radius) y = rad_y - radar_radius;
		if (y > rad_y + radar_radius) y = rad_y + radar_radius;

		outScreenX = (int)x;
		outScreenY = (int)y;
	};

	ConvertToRadarPos(WorldLocation, ScreenX, ScreenY);

	if (RADAR_SETTINGS.showDistance) {
		std::string distanceText = "[" + std::to_string(static_cast<int>(distance)) + "m]";
		ImFont* font = ImGui::GetFont();
		float fontSize = 10.0f;

		ImVec2 textSize = font->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, distanceText.c_str());
		ImVec2 textPos((ScreenX) - (textSize.x / 2), (ScreenY + 3));
		ImColor textColor = ImColor(255, 255, 255);
		ImColor outlineColor = ImColor(0, 0, 0);
		
		DrawOutlinedText(distanceText, textPos, textColor, outlineColor);
	}
	
	drawList->AddCircleFilled(ImVec2(ScreenX, ScreenY), 3, radar_visible_color, 12);
	ImGui::End();
}