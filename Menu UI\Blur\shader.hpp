#define IMGUI_DEFINE_MATH_OPERATORS

#include <d3d11.h>
#include <d3dcompiler.h>
#include "../ImGui/imgui.h"

struct float2
{
    float x, y;
};

inline ID3D11ShaderResourceView* effectResourceView;

struct alignas(16) BlurInputBufferT {
    float2 textureSize;
    float blurDirections;
    float blurQuality;
    float blurRadius;
};

namespace shaders {
    inline void blurBegin(const ImDrawList* drawList, const ImDrawCmd* drawCmd);
    inline void blurEnd(const ImDrawList* drawList, const ImDrawCmd* drawCmd);
    inline void createDeviceObjects(IDXGISwapChain* swapChain);
    inline void drawToScreenUV(ImVec2 pos, ImVec2 size, ImColor color, float rounding, ImDrawFlags flags);
    inline void applyBlur(float size, float quality, float directions);
    inline void beginFrame(IDXGISwapChain* swapChain);
}

inline void bluring(const ImVec2& pos, const ImVec2& size, ImColor color, float rounding, ImDrawFlags flags, bool background) {
    const ImVec2& scaledPos{ pos };
    const ImVec2& scaledSize{ size };
    const ImVec2& finalPos{ scaledPos - (scaledSize / 2.f) };
    shaders::drawToScreenUV(finalPos, finalPos + scaledSize, color, rounding, flags);
} 