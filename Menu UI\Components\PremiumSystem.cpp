#include "PremiumSystem.h"
#include <iostream>
#include <algorithm>

PremiumSystem& PremiumSystem::getInstance() {
    static PremiumSystem instance;
    return instance;
}

bool PremiumSystem::isPremiumUser() const {
    return isPremium;
}

void PremiumSystem::setPremiumUser(bool premium) {
    isPremium = premium;
}

bool PremiumSystem::isFeaturePremium(const std::string& featureName) const {
    return premiumFeatures.find(featureName) != premiumFeatures.end();
}

void PremiumSystem::addPremiumFeature(const std::string& featureName) {
    premiumFeatures.insert(featureName);
}

void PremiumSystem::removePremiumFeature(const std::string& featureName) {
    premiumFeatures.erase(featureName);
}

std::vector<std::string> PremiumSystem::getPremiumFeatures() const {
    std::vector<std::string> features;
    features.reserve(premiumFeatures.size());
    
    for (const auto& feature : premiumFeatures) {
        features.push_back(feature);
    }
    
    std::sort(features.begin(), features.end());
    return features;
}

bool PremiumSystem::shouldLockFeature(const std::string& featureName) const {
    return isFeaturePremium(featureName) && !isPremiumUser();
}

void PremiumSystem::initializeDefaultPremiumFeatures() {
    // Add default premium features
    addPremiumFeature("Silent Aimbot");
    addPremiumFeature("Freeze Player");
    addPremiumFeature("Aim Lock");
    addPremiumFeature("Prediction");
    addPremiumFeature("Save Target");
    addPremiumFeature("Humanized Smooth");
    addPremiumFeature("Advanced ESP");
    addPremiumFeature("Weapon ESP");
    addPremiumFeature("Health ESP");
    addPremiumFeature("Shield ESP");
    
    // For demonstration, set to false - in a real app this would check license
    setPremiumUser(false);
}

void PremiumSystem::onUpgradeButtonClick() {
    // In a real implementation, this would:
    // 1. Open a web browser to the upgrade page
    // 2. Show a licensing dialog
    // 3. Handle payment processing
    // 4. Update user status
    
    std::cout << "Premium upgrade requested - opening upgrade page..." << std::endl;
    
    // For demo purposes, we'll just show a message
    // In real implementation, you would open a browser or show a dialog
    // system("start https://your-premium-upgrade-url.com");
} 