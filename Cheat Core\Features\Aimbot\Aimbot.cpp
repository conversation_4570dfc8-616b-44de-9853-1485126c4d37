#include "Aimbot.h"
#include <cmath>
#include <Windows.h>
#include "../../Kernel Driver/Driver/Driver.h"
#include "../../GameClass/Offsets.h"
#include "../../GameClass/GameFunctions.h"
#include "../../GameClass/GameSettings.h"
#include "../../Settings/Settings.h"
#include <mutex>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../../../Menu UI/ImGui/imgui.h"
#include "../../../Utils.h"

bool  AimbotSpace::bIsInRectangle(double centerX, double centerY, double radius, double x, double y) {
    return x >= centerX - radius && x <= centerX + radius &&
        y >= centerY - radius && y <= centerY + radius;
}

WeaponTypes  AimbotSpace::GetWeaponType(const std::wstring& weaponName) {
    if (weaponName.find(L"DMR") != std::wstring::npos) return WeaponTypes::DMR;
    if (weaponName.find(L"SMG") != std::wstring::npos) return WeaponTypes::SMG;
    if (weaponName.find(L"Shotgun") != std::wstring::npos) return WeaponTypes::Shotgun;
    if (weaponName.find(L"Pistol") != std::wstring::npos) return WeaponTypes::Pistol;
    if (weaponName.find(L"AR") != std::wstring::npos || weaponName.find(L"Assault Rifle") != std::wstring::npos) return WeaponTypes::AssaultRifle;
    if (weaponName.find(L"Sniper") != std::wstring::npos) return WeaponTypes::Sniper;
    return WeaponTypes::WeaponCount;
}
bool AimbotSpace::GetProjectileProperties(const std::wstring& WeaponName, float& ProjectileSpeed, float& ProjectileGravityScale, float distance) {
    if (wcsstr(WeaponName.c_str(), L"Striker AR")) {
        ProjectileSpeed = 80000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Nemesis AR")) {
        ProjectileSpeed = 80000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Reaper Sniper Rifle")) {
        ProjectileSpeed = 50000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Reaper Modular Sniper Rifle")) {
        ProjectileSpeed = 50000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Ranger Pistol")) {
        ProjectileSpeed = 60000.0;
        ProjectileGravityScale = 2.0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Burst SMG")) {
        ProjectileSpeed = 70000.0;
        ProjectileGravityScale = 3.0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Hyper SMG")) {
        ProjectileSpeed = 70000.0;
        ProjectileGravityScale = 3.0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Harbinger SMG")) {
        ProjectileSpeed = 80000.0;
        ProjectileGravityScale = 3.0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Warforged Assault Rifle")) {
        ProjectileSpeed = 80000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Tactical Assault Rifle")) {
        ProjectileSpeed = 80000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Huntress DMR")) {
        ProjectileSpeed = 96000.0;
        ProjectileGravityScale = 2.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Boom Bolt")) {
        ProjectileSpeed = 60000.0;
        ProjectileGravityScale = 0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Enforcer AR")) {
        ProjectileSpeed = 80000.0;
        ProjectileGravityScale = 3.0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Drum Gun")) {
        ProjectileSpeed = 75000.0;
        ProjectileGravityScale = 3.0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Combat Assault Rifle")) {
        ProjectileSpeed = 80000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Heavy Impact Sniper Rifle")) {
        ProjectileSpeed = 50000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Hunting Rifle")) {
        ProjectileSpeed = 70000.0;
        ProjectileGravityScale = 3.5;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Bolt-Action Sniper Rifle")) {
        ProjectileSpeed = 45000.0;
        ProjectileGravityScale = 0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Ringmaster's Boom Bolt")) {
        ProjectileSpeed = 60000.0;
        ProjectileGravityScale = 0;
        return true;
    }
    /*if (wcsstr(WeaponName.c_str(), L"Striker Burst Rifle")) {
        ProjectileSpeed = 80000.0;
        ProjectileGravityScale = 3.0;
        return true;
    }*/
    if (wcsstr(WeaponName.c_str(), L"Dual Micro SMGs")) {
        ProjectileSpeed = 75000.0;
        ProjectileGravityScale = 3.0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Monarch Pistol")) {
        ProjectileSpeed = 70000.0;
        ProjectileGravityScale = 2.0;
        return true;
    }
    if (wcsstr(WeaponName.c_str(), L"Falcon Eye Sniper")) {
        ProjectileSpeed = 55000.0;
        ProjectileGravityScale = 2.5;
        return true;
    }
    /* if (wcsstr(WeaponName.c_str(), L"Submachine Gun")) {
         ProjectileSpeed = 70000.0;
         ProjectileGravityScale = 0;
         return true;
     }
     if (wcsstr(WeaponName.c_str(), L"Revolver")) {
         ProjectileSpeed = 70000.0;
         ProjectileGravityScale = 0;
         return true;
     }
     if (wcsstr(WeaponName.c_str(), L"Burst Assault Rifle")) {
         ProjectileSpeed = 70000.0;
         ProjectileGravityScale = 0;
         return true;
     }
     if (wcsstr(WeaponName.c_str(), L"Havoc Suppressed Assault Rifle")) {
         ProjectileSpeed = 70000.0;
         ProjectileGravityScale = 0;
         return true;
     }*/
     /* if (wcsstr(WeaponName.c_str(), L"Holo Twister Assault Rifle")) {
          ProjectileSpeed = 70000.0;
          ProjectileGravityScale = 0;
          return true;
      }*/
      /*if (wcsstr(WeaponName.c_str(), L"Fury Assault Rifle")) {
          ProjectileSpeed = 70000.0;
          ProjectileGravityScale = 0;
          return true;
      }*/
      /*if (wcsstr(WeaponName.c_str(), L"Surgefire SMG")) {
          ProjectileSpeed = 70000.0;
          ProjectileGravityScale = 0;
          return true;
      }*/
      /*if (wcsstr(WeaponName.c_str(), L"Mammoth Pistol")) {
          ProjectileSpeed = 70000.0;
          ProjectileGravityScale = 0;
          return true;
      }
      if (wcsstr(WeaponName.c_str(), L"Hand Cannon")) {
          ProjectileSpeed = 70000.0;
          ProjectileGravityScale = 2.0;
          return true;
      }*/
      /*  if (wcsstr(WeaponName.c_str(), L"Ranger Assault Rifle")) {
            ProjectileSpeed = 70000.0;
            ProjectileGravityScale = 0;
            return true;
        }*/
        /* if (wcsstr(WeaponName.c_str(), L"Hammer Assault Rifle")) {
             ProjectileSpeed = 70000.0;
             ProjectileGravityScale = 0;
             return true;
         }*/
         /* if (wcsstr(WeaponName.c_str(), L"Veiled Precision SMG")) {
              ProjectileSpeed = 70000.0;
              ProjectileGravityScale = 0;
              return true;
          }*/
    if (wcsstr(WeaponName.c_str(), L"Heavy Sniper Rifle")) {
        if (distance < 100.0f) {
            // Close-range values
            ProjectileSpeed = 70000.0;//31818.0
            ProjectileGravityScale = 0.4;
        }
        else {
            // Far-range values
            ProjectileSpeed = 70000.0;//34205.0
            ProjectileGravityScale = 0.2;
        }
        return true;
    }

    // Add more weapons as needed

    return false; // Weapon not found in the list
}

void AimbotSpace::PressLeftClick() {
    if (!IsPressed) {
        IsPressed = true;
        Driver->MoveMouse(0, 0, MOUSE_LEFT_BUTTON_DOWN);
    }
}

void AimbotSpace::ResetLeftClick() {
    if (IsPressed) {
        IsPressed = false;
        Driver->MoveMouse(0, 0, MOUSE_LEFT_BUTTON_UP);
    }
}
void AimbotSpace::HandleTriggerbot(const std::wstring& WeaponName, float distance, uintptr_t PlayerPawn) {
    static auto last_trigger_time = std::chrono::steady_clock::now();
    constexpr auto ReleaseDelay = std::chrono::milliseconds(1);

    if (!TRIGGERBOT_SETTINGS.general.enabled || !IsKeyPressed(HOTKEY_SETTINGS.global.holdTrigger.key)) {
        ResetLeftClick();
        return;
    }

    if (distance >= TRIGGERBOT_SETTINGS.general.maxDistance || !PlayerPawn) {
        ResetLeftClick();
        return;
    }

    bool isValidWeapon = TRIGGERBOT_SETTINGS.general.enableAllWeapons ||
        (TRIGGERBOT_SETTINGS.general.enableOnlyShotguns && wcsstr(WeaponName.c_str(), L"Shotgun"));
    if (!isValidWeapon) {
        ResetLeftClick();
        return;
    }

    auto now = std::chrono::steady_clock::now();

    // Compare durations directly, no need for duration_cast to count()
    if (now - last_trigger_time > std::chrono::milliseconds(TRIGGERBOT_SETTINGS.general.delay)) {
        PressLeftClick();
        last_trigger_time = now;
    }

    if (IsPressed && (now - last_trigger_time) > ReleaseDelay) {
        ResetLeftClick();
    }
}
void AimbotSpace::PerformAimbot(Vector3& Head, uintptr_t Mesh, const Vector3& Velocity, uintptr_t targeted_fort_pawn, uintptr_t AcknowlegedPawn, uintptr_t camera, std::wstring WeaponName, uintptr_t CurrentWeapon) {
    Vector2 hitbox_screen = GameFunctions::ProjectWorldToScreen(Head);
    if (hitbox_screen.x == 0 || hitbox_screen.y == 0) return;

    bool enabled = AIMBOT_SETTINGS.general.enabled;
    bool predictEnabled = AIMBOT_SETTINGS.general.predict;
    bool aimLockEnabled = AIMBOT_SETTINGS.general.aimLock;
    bool weaponOnlyEnabled = AIMBOT_SETTINGS.general.weaponOnly;
    bool drawTargetEnabled = AIMBOT_SETTINGS.general.visuals.drawTarget;
    bool humanizedSmoothEnabled = AIMBOT_SETTINGS.general.humanizedSmooth;
    float fovByWeapon = AIMBOT_SETTINGS.general.fov;
    float smoothByWeapon = AIMBOT_SETTINGS.general.smooth;
    float humanizedSmoothPercentByWeapon = AIMBOT_SETTINGS.general.humanizedSmoothPercent;

    if (AIMBOT_SETTINGS.general.perWeapon) {
        auto weaponType = GetWeaponType(WeaponName);
        AimbotCore& weaponSettingsForCurrentWeapon = AIMBOT_SETTINGS.perWeaponSettings[static_cast<int>(weaponType)];
        enabled = weaponSettingsForCurrentWeapon.enabled;
        predictEnabled = weaponSettingsForCurrentWeapon.predict;
        aimLockEnabled = weaponSettingsForCurrentWeapon.aimLock;
        weaponOnlyEnabled = weaponSettingsForCurrentWeapon.weaponOnly;
        drawTargetEnabled = weaponSettingsForCurrentWeapon.visuals.drawTarget;
        humanizedSmoothEnabled = weaponSettingsForCurrentWeapon.humanizedSmooth;
        fovByWeapon = weaponSettingsForCurrentWeapon.fov;
        smoothByWeapon = weaponSettingsForCurrentWeapon.smooth;
        humanizedSmoothPercentByWeapon = weaponSettingsForCurrentWeapon.humanizedSmoothPercent;
    }

    if (drawTargetEnabled) {
        ImGui::GetForegroundDrawList()->AddLine(
            ImVec2(OverlayWidth / 2, OverlayHeight / 2),
            ImVec2(hitbox_screen.x, hitbox_screen.y),
            ImColor(Settings.Colors.AimbotColorsTarget[0], Settings.Colors.AimbotColorsTarget[1], Settings.Colors.AimbotColorsTarget[2]),
            1
        );
    }

    if (weaponOnlyEnabled && (WeaponName.empty() || wcsstr(WeaponName.c_str(), L"Pickaxe"))) return;

    float distance = vCamera.Location.Distance(Head) / 100;
    uintptr_t PlayerPawn = UseDriver::read<std::uintptr_t>(Results::PlayerController + Offsets::TargetedFortPawn);

    // Triggerbot logic
    HandleTriggerbot(WeaponName, distance, PlayerPawn);

    float ProjectileSpeed = 0.0;
    float ProjectileGravity = 0.0;
    Vector3 PredictedPosition;
    if (predictEnabled) {
        /* auto GetProjectileSpeed = DriverHook::read<float>(CurrentWeapon + 0x1d58);
         auto GetProjectileGravity = DriverHook::read<float>(CurrentWeapon + 0x1d5c);
         std::wcout << L"WeaponName: " << WeaponName << std::endl;
         std::cout << "GetProjectileSpeed :" << GetProjectileSpeed << std::endl;
         std::cout << "GetProjectileGravity :" << GetProjectileGravity << std::endl;*/

        if (GetProjectileProperties(WeaponName, ProjectileSpeed, ProjectileGravity, distance)) {

            PredictedPosition = GameFunctions::PredictLocation(Head, Velocity, ProjectileSpeed, ProjectileGravity, vCamera.Location.Distance(Head));
            hitbox_screen = GameFunctions::ProjectWorldToScreen(PredictedPosition);
        }
    }


    // Aimbot logic
    if (enabled) {
        double dx = hitbox_screen.x - (OverlayWidth / 2);
        double dy = hitbox_screen.y - (OverlayHeight / 2);
        double closest_head_distance = sqrt(dx * dx + dy * dy);

        if (bIsInRectangle(OverlayWidth / 2, OverlayHeight / 2, AIMBOT_SETTINGS.general.fov, hitbox_screen.x, hitbox_screen.y) &&
            closest_head_distance <= fovByWeapon * 2 && distance <= AIMBOT_SETTINGS.general.maxDistance) {
            if (IsKeyPressed(HOTKEY_SETTINGS.global.holdPrimary.key) || IsKeyPressed(HOTKEY_SETTINGS.global.holdSecondary.key)) {
                AimMove(hitbox_screen, closest_head_distance, fovByWeapon, smoothByWeapon, humanizedSmoothPercentByWeapon, humanizedSmoothEnabled);
            }
        }
    }
}


void AimbotSpace::ProcessTarget(uintptr_t target, uintptr_t targeted_fort_pawn, uintptr_t AcknowlegedPawn, uintptr_t camera) {
    if (!target) return;

    uintptr_t Mesh = UseDriver::read<uintptr_t>(target + Offsets::Mesh);
    if (!Mesh) return;

    Vector3 hitbox;
    Vector3 Velocity = UseDriver::read<Vector3>(UseDriver::read<uintptr_t>(target + Offsets::RootComponent) + Offsets::ComponentVelocity);

    // Determine weapon name
    std::wstring WeaponName;
    auto CurrentWeapon = UseDriver::read<uintptr_t>(AcknowlegedPawn + Offsets::CurrentWeapon);
    auto WeaponData = UseDriver::read<uintptr_t>(CurrentWeapon + Offsets::WeaponData);
    uint64_t ftext_ptr = UseDriver::read<uint64_t>(WeaponData + 0x40);

    if (ftext_ptr) {
        uint64_t ftext_data = UseDriver::read<uint64_t>(ftext_ptr + Offsets::FData);
        int ftext_length = UseDriver::read<int>(ftext_ptr + Offsets::FLength);

        if (ftext_length > 0 && ftext_length < 50) {
            std::wstring ftext_buf(ftext_length, L'\0');
            UseDriver::ReadString(ftext_data, ftext_buf.data(), ftext_length * sizeof(wchar_t));
            WeaponName = std::move(ftext_buf);

        }
    }
    //WeaponName = GameFunctions::GetWeaponName(AcknowlegedPawn);


    // Apply per-weapon settings for hitbox
    int selectedHitbox = AIMBOT_SETTINGS.general.hitBox;
    if (AIMBOT_SETTINGS.general.perWeapon) {
        auto weaponType = GetWeaponType(WeaponName);
        selectedHitbox = AIMBOT_SETTINGS.perWeaponSettings[static_cast<int>(weaponType)].hitBox;
    }

    // Select the hitbox based on settings
    switch (selectedHitbox) {
    case 0: // Head
        hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Head);
        break;
    case 1: // Neck
        hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Neck);
        break;
    case 2: // Chest
        hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Chest);
        break;
    case 3: // Pelvis
        hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Pelvis);
        break;
    case 4: // Random
        if (selectedRandomHitbox == -1) { // Only select a random hitbox once per key press
            selectedRandomHitbox = rand() % 4; // Random between 0 and 3
        }

        switch (selectedRandomHitbox) {
        case 0:
            hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Head);
            break;
        case 1:
            hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Neck);
            break;
        case 2:
            hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Chest);
            break;
        case 3:
            hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Pelvis);
            break;
        }
        break;
    default: // Default to head if no valid hitbox is selected
        hitbox = GameFunctions::GetBoneWithRotation(Mesh, EBoneIndex::Head);
        break;
    }

    PerformAimbot(hitbox, Mesh, Velocity, targeted_fort_pawn, AcknowlegedPawn, camera, WeaponName, CurrentWeapon);

    // Reset random hitbox when the key is released
            if (!IsKeyPressed(HOTKEY_SETTINGS.global.holdPrimary.key) && !IsKeyPressed(HOTKEY_SETTINGS.global.holdSecondary.key)) {
        selectedRandomHitbox = -1; // Reset for the next key press
    }
}

void  AimbotSpace::HandleAimbot(uintptr_t Player, uintptr_t Mesh, const Vector3& HeadPosition, uintptr_t Targeted_fort_pawn, uintptr_t AcknowlegedPawn, uintptr_t PlayerState) {
    Vector2 Head2D = GameFunctions::ProjectWorldToScreen(HeadPosition);
    if (!GameFunctions::isOnScreen(Head2D)) return;

    double dx = Head2D.x - (OverlayWidth / 2);
    double dy = Head2D.y - (OverlayHeight / 2);
    float distance = sqrt(dx * dx + dy * dy);

    if (AIMBOT_SETTINGS.general.saveTarget && g_CurrentTarget && (GetAsyncKeyState(VK_RBUTTON) & 0x8000)) {
        if (Player == g_CurrentTarget && distance < AIMBOT_SETTINGS.general.fov) {
            g_ClosestDistance = distance;
            return;
        }
    }

    if (AIMBOT_SETTINGS.general.visibilityCheck) {
        // Perform visibility check
        bool bIsVisible = GameFunctions::IsVisible(Mesh);

        if (!bIsVisible) {
            // Skip this target if not visible
            return;
        }
    }

    // Proceed if visibility is not required or the target is visible
    if (distance < g_ClosestDistance && distance < AIMBOT_SETTINGS.general.fov) {
        auto isDBNO = (UseDriver::read<char>(Player + Offsets::isDBNO) >> 4) & 1;
        bool bIsAnAthenaGameParticipant = (UseDriver::read<char>(PlayerState + Offsets::bIsAnAthenaGameParticipant) >> 0) & 1;

        // Conditions based on Aimbot settings
        if (AIMBOT_SETTINGS.general.ignoreDowned || !AIMBOT_SETTINGS.general.playerAi) {
            if (!isDBNO || bIsAnAthenaGameParticipant) {
                // Update closest target details
                g_ClosestDistance = distance;
                g_CurrentTarget = Player;
                g_Targeted_fort_pawn = Targeted_fort_pawn;
                g_AcknowlegedPawn = AcknowlegedPawn;
            }
        }
        else {
            // Update closest target details
            g_ClosestDistance = distance;
            g_CurrentTarget = Player;
            g_Targeted_fort_pawn = Targeted_fort_pawn;
            g_AcknowlegedPawn = AcknowlegedPawn;
        }
    }

}

void AimbotSpace::ProcessAimbot() {
    if (g_CurrentTarget && hasTarget) {
        ProcessTarget(g_CurrentTarget, g_Targeted_fort_pawn, g_AcknowlegedPawn, Results::PlayerCameraManager);
    }
    else if (!hasTarget) {
        g_CurrentTarget = NULL;
        g_ClosestDistance = FLT_MAX;
    }
}