#include "HImGuiImageManager.h"
#include "Logger.h"
#include <cstdio>
#include <sstream>
#include "../ImGui/stb_image.h"

std::map<std::string, HImage*> HImageManager::images;
HImGuiIO HImageManager::io;

// Helper function to load GIF data from a file
unsigned char* stbi_load_gif_from_file_custom(FILE* f, int** delays, int* width, int* height, int* frames, int* channels, int req_comp) {
    try {
        if (!f) {
            Logger::LogError("Invalid file handle provided to stbi_load_gif_from_file_custom");
            return nullptr;
        }

        // Get file size
	fseek(f, 0, SEEK_END);
        long size = ftell(f);
		fseek(f, 0, SEEK_SET);

        if (size <= 0) {
            Logger::LogError("Invalid file size: " + std::to_string(size));
            return nullptr;
        }

        Logger::LogDebug("Loading GIF file of size: " + std::to_string(size) + " bytes");

        // Read file into memory
        unsigned char* buffer = new unsigned char[size];
        size_t bytesRead = fread(buffer, 1, size, f);
        
        if (bytesRead != size_t(size)) {
            Logger::LogError("Failed to read entire file. Expected: " + std::to_string(size) + ", Read: " + std::to_string(bytesRead));
            delete[] buffer;
            return nullptr;
        }

        // Load GIF data
        int comp;
        unsigned char* data = stbi_load_from_memory(buffer, size, width, height, &comp, req_comp);
        delete[] buffer;

        if (!data) {
            Logger::LogError("stbi_load_from_memory failed: " + std::string(stbi_failure_reason()));
            return nullptr;
        }

        Logger::LogDebug("Successfully loaded image: " + std::to_string(*width) + "x" + std::to_string(*height));

        *frames = 1;
        *delays = (int*)malloc(sizeof(int));
        (*delays)[0] = 100; // Default 100ms delay
        if (channels) *channels = comp;

        return data;
    } catch (const std::exception& e) {
        Logger::LogError("Exception in stbi_load_gif_from_file_custom: " + std::string(e.what()));
        return nullptr;
    } catch (...) {
        Logger::LogError("Unknown exception in stbi_load_gif_from_file_custom");
        return nullptr;
    }
}

bool HImageManager::GetImage_gif(const char* path, HImage*& image, float defaultDelay) {
    try {
        if (!path) {
            Logger::LogError("Null path provided to GetImage_gif");
            return false;
        }

        Logger::LogDebug("Loading GIF: " + std::string(path));

        // Check if image is already loaded
        auto it = images.find(path);
        if (it != images.end()) {
            image = it->second;
            Logger::LogDebug("Found cached image for: " + std::string(path));
		return true;
        }

        // Load new image
        FILE* f = nullptr;
        errno_t err = fopen_s(&f, path, "rb");
        if (err != 0 || !f) {
            char errMsg[256];
            strerror_s(errMsg, sizeof(errMsg), err);
            Logger::LogError("Failed to open file: " + std::string(path) + ", Error: " + errMsg);
							return false;
					}

        // Read file header to check if it's a GIF
        char header[6];
        if (fread(header, 1, 6, f) != 6) {
            Logger::LogError("Failed to read GIF header from: " + std::string(path));
            fclose(f);
					return false;
				}
        fseek(f, 0, SEEK_SET);

        bool isGif = (memcmp(header, "GIF87a", 6) == 0 || memcmp(header, "GIF89a", 6) == 0);
        Logger::LogDebug("File " + std::string(path) + " is" + (isGif ? "" : " not") + " a GIF");
        
        image = new HImage();
        image->isGif = isGif;
        image->currentFrame = 0;
        image->elapsedTime = 0;
        image->delay = defaultDelay;

        if (isGif) {
            int* delays = nullptr;
            int frameCount = 0;
            int channels = 0;
            unsigned char* data = stbi_load_gif_from_file_custom(f, &delays, &image->width, &image->height, &frameCount, &channels, 4);
            fclose(f);

            if (!data) {
                Logger::LogError("Failed to load GIF data from: " + std::string(path));
                delete image;
                return false;
            }

            // Create texture for the frame
            if (!GetIO().CreateTexture) {
                Logger::LogError("CreateTexture callback not set");
                stbi_image_free(data);
                delete image;
                return false;
            }

            HTextureID texture = GetIO().CreateTexture(data, image->width, image->height, 4);
            if (texture) {
                image->frames.push_back(texture);
                image->delays.push_back(defaultDelay);
                Logger::LogDebug("Successfully created texture for GIF frame");
            } else {
                Logger::LogError("Failed to create texture for GIF frame");
            }

            stbi_image_free(data);
            if (delays) free(delays);

            if (image->frames.empty()) {
                Logger::LogError("No frames loaded for GIF: " + std::string(path));
                delete image;
				return false;
		}
        } else {
            int width, height, channels;
            unsigned char* data = stbi_load(path, &width, &height, &channels, 4);
            fclose(f);

            if (!data) {
                Logger::LogError("Failed to load image data from: " + std::string(path));
                delete image;
				return false;
            }

            image->width = width;
            image->height = height;

            if (!GetIO().CreateTexture) {
                Logger::LogError("CreateTexture callback not set");
                stbi_image_free(data);
                delete image;
                return false;
            }

            image->texture = GetIO().CreateTexture(data, width, height, 4);
            stbi_image_free(data);

            if (!image->texture) {
                Logger::LogError("Failed to create texture for image: " + std::string(path));
                delete image;
                return false;
            }
        }

        images[path] = image;
        Logger::LogDebug("Successfully loaded and cached image: " + std::string(path));
		return true;
    } catch (const std::exception& e) {
        Logger::LogError("Exception in GetImage_gif: " + std::string(e.what()));
        if (image) {
            delete image;
            image = nullptr;
        }
				return false;
    } catch (...) {
        Logger::LogError("Unknown exception in GetImage_gif");
        if (image) {
            delete image;
            image = nullptr;
		}
		return false;
	}
}

void HImageManager::Image_gif(const char* path, const ImVec2& size, float defaultDelay) {
    try {
        HImage* image = nullptr;
        if (!GetImage_gif(path, image, defaultDelay) || !image) {
            Logger::LogWarning("Failed to load or display GIF: " + std::string(path ? path : "null"));
		return;
	}

        if (image->isGif && !image->frames.empty()) {
            ImGui::Image(image->frames[image->currentFrame], size);
        } else if (image->texture) {
            ImGui::Image(image->texture, size);
        }
    } catch (const std::exception& e) {
        Logger::LogError("Exception in Image_gif: " + std::string(e.what()));
    } catch (...) {
        Logger::LogError("Unknown exception in Image_gif");
    }
}

void HImageManager::updata(float deltaTime) {
    try {
        for (auto& pair : images) {
            HImage* image = pair.second;
            if (image->isGif && !image->frames.empty()) {
                image->elapsedTime += deltaTime * 1000.0f;  // Convert to milliseconds
                
                if (image->elapsedTime >= image->delays[image->currentFrame]) {
                    image->elapsedTime = 0;
                    image->currentFrame = (image->currentFrame + 1) % image->frames.size();
                }
            }
        }
    } catch (const std::exception& e) {
        Logger::LogError("Exception in updata: " + std::string(e.what()));
    } catch (...) {
        Logger::LogError("Unknown exception in updata");
    }
}