#pragma once
#include <Windows.h>
#include "../../GameClass/GameSettings.h"
#include <vector>
#include <string>
#include <unordered_map>
#include <map>
#include <chrono>

// Change notification types
enum class ChangeType {
    Enable,    // Boolean enable/disable
    Value,     // Numeric value change
    Color      // Color change
};

// Structure for notification system settings
struct NotificationSettings {
    bool Enable = true;              // Enable/disable notifications
    float DisplayDuration = 5.0f;    // Duration in seconds (default: 5s, max: 20s)
};

// Structure to represent a single setting change
struct SettingChange {
    std::string category;    // Category of the setting (Aimbot, ESP, etc.)
    std::string name;        // Name of the setting that changed
    ChangeType type;         // Type of change

    // Values depending on change type
    union {
        struct {
            bool oldValue;
            bool newValue;
        } boolChange;

        struct {
            float oldValue;
            float newValue;
        } valueChange;

        struct {
            float oldColor[3];
            float newColor[3];
        } colorChange;
    };

    // Time when this change was recorded
    float changeTime;
};

// Configuration file structure
struct ConfigFile {
    std::string name;           // Display name of the config
    std::string type;           // Type of config (e.g., "Rage", "Legit")
    std::string filename;       // Actual filename on disk
    bool isSelected;            // Currently selected config
    bool isDefault;             // Is this the default config
    bool isDownloaded;          // Whether the config is downloaded
    bool isCloudConfig;         // Whether this is a cloud/shared config
    std::chrono::system_clock::time_point lastModified; // Last modified time
};

// Structure for the configuration system state
struct ConfigSystem {
    std::vector<ConfigFile> configs;       // Available configurations
    std::string configFolder;              // Path to config folder
    int selectedConfigIndex;               // Index of currently selected config
    bool showSharePopup;                   // Whether to show the share popup

    // Share popup fields
    std::string shareConfigName;           // Name for the config to be shared
    std::string shareConfigType;           // Type for the config to be shared
    std::string searchQuery;               // Search filter query

    ConfigSystem() :
        configFolder("C:\\BitCheats\\Fortnite_Config\\"),
        selectedConfigIndex(-1),
        showSharePopup(false) {}
};

// Notification manager class
class NotificationManager {
public:
    void AddChange(const std::string& category, const std::string& name, bool oldValue, bool newValue);
    void AddChange(const std::string& category, const std::string& name, float oldValue, float newValue);
    void AddChange(const std::string& category, const std::string& name, float oldColor[3], float newColor[3]);

    void ClearChanges();
    const std::vector<SettingChange>& GetChanges() const;
    void UpdateChangeTime(const std::string& category, const std::string& name);

    void ShowNotifications();

    bool HasActiveNotifications() const { return !m_changes.empty() && m_notificationsActive; }

private:
    std::vector<SettingChange> m_changes;
    bool m_notificationsActive = false;
    float m_notificationStartTime = 0.0f;

    // Helper to find existing change by category and name
    int FindExistingChange(const std::string& category, const std::string& name);
};

struct Config
{
    // Status
    bool Run = false;
};

extern Config fucme;
extern NotificationSettings g_NotificationSettings;
extern NotificationManager g_NotificationManager;
extern ConfigSystem g_ConfigSystem;  // Global instance of the config system

// Functions for the new config system
void InitializeConfigSystem();
void RefreshConfigurations();
bool SaveConfiguration(const std::string& filename);
bool LoadConfiguration(const std::string& filename);
bool DeleteConfiguration(const std::string& filename);
bool CreateConfiguration(const std::string& name, const std::string& type);
bool ShareConfiguration(const std::string& name, const std::string& type);
void SelectConfiguration(int index);
void SelectDefaultConfiguration();

// Snapshot of settings for tracking changes
struct ConfigSnapshot {
    void CaptureSettings();
    void DetectChanges();

    // Settings snapshots
    AimbotCore aimbot;
    TriggerbotCore triggerbot;
    ItemESPSettings items;
    PlayerESPSettings players;
    HotkeySettings keys;
    RadarSettings radar;
    ConfigurationSettings configsMenu;
};

extern ConfigSnapshot g_ConfigSnapshot;

