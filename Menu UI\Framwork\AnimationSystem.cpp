#include "AnimationSystem.h"
#include <algorithm>
#include <cmath>
#include <string>

namespace AnimationSystem {

    // Global animation manager instance
    AnimationManager g_AnimationManager;

    // Initialize the animation system
    void AnimationManager::Initialize() {
        currentTime = ImGui::GetTime();
        lastFrameTime = currentTime;
        deltaTime = 0.0f;
        
        // Initialize states
        windowState = WindowAnimationState();
        tabState = TabTransitionState();
        popupState = PopupAnimationState();
        contentState = ContentAnimationState();
        
        animations.clear();
    }

    // Update all animations
    void AnimationManager::Update() {
        currentTime = ImGui::GetTime();
        deltaTime = currentTime - lastFrameTime;
        lastFrameTime = currentTime;

        // Update individual animations - using traditional iterator for C++14 compatibility
        for (auto it = animations.begin(); it != animations.end(); ++it) {
            auto& anim = it->second;
            if (!anim.isActive || anim.isCompleted) continue;

            float elapsed = currentTime - anim.startTime - anim.delay;
            if (elapsed < 0.0f) continue; // Still in delay phase

            float t = (std::min)(elapsed / anim.duration, 1.0f);
            float easedT = ApplyEasing(t, anim.easing);
            
            anim.currentValue = anim.startValue + (anim.targetValue - anim.startValue) * easedT;

            if (t >= 1.0f) {
                anim.isCompleted = true;
                anim.isActive = false;
                anim.currentValue = anim.targetValue;
                
                if (anim.onComplete) {
                    anim.onComplete();
                }
            }
        }

        // Update specialized animations
        UpdateWindowAnimation();
        UpdateTabTransition();
        UpdatePopupAnimation();
    }

    // Easing functions implementation
    float AnimationManager::Linear(float t) {
        return t;
    }

    float AnimationManager::EaseInQuad(float t) {
        return t * t;
    }

    float AnimationManager::EaseOutQuad(float t) {
        return 1.0f - (1.0f - t) * (1.0f - t);
    }

    float AnimationManager::EaseInOutQuad(float t) {
        return t < 0.5f ? 2.0f * t * t : 1.0f - powf(-2.0f * t + 2.0f, 2.0f) / 2.0f;
    }

    float AnimationManager::EaseInCubic(float t) {
        return t * t * t;
    }

    float AnimationManager::EaseOutCubic(float t) {
        return 1.0f - powf(1.0f - t, 3.0f);
    }

    float AnimationManager::EaseInOutCubic(float t) {
        return t < 0.5f ? 4.0f * t * t * t : 1.0f - powf(-2.0f * t + 2.0f, 3.0f) / 2.0f;
    }

    float AnimationManager::EaseInQuart(float t) {
        return t * t * t * t;
    }

    float AnimationManager::EaseOutQuart(float t) {
        return 1.0f - powf(1.0f - t, 4.0f);
    }

    float AnimationManager::EaseInOutQuart(float t) {
        return t < 0.5f ? 8.0f * t * t * t * t : 1.0f - powf(-2.0f * t + 2.0f, 4.0f) / 2.0f;
    }

    float AnimationManager::EaseInExpo(float t) {
        return t == 0.0f ? 0.0f : powf(2.0f, 10.0f * (t - 1.0f));
    }

    float AnimationManager::EaseOutExpo(float t) {
        return t == 1.0f ? 1.0f : 1.0f - powf(2.0f, -10.0f * t);
    }

    float AnimationManager::EaseInOutExpo(float t) {
        if (t == 0.0f) return 0.0f;
        if (t == 1.0f) return 1.0f;
        return t < 0.5f ? powf(2.0f, 20.0f * t - 10.0f) / 2.0f : (2.0f - powf(2.0f, -20.0f * t + 10.0f)) / 2.0f;
    }

    float AnimationManager::EaseInBack(float t) {
        const float c1 = 1.70158f;
        const float c3 = c1 + 1.0f;
        return c3 * t * t * t - c1 * t * t;
    }

    float AnimationManager::EaseOutBack(float t) {
        const float c1 = 1.70158f;
        const float c3 = c1 + 1.0f;
        return 1.0f + c3 * powf(t - 1.0f, 3.0f) + c1 * powf(t - 1.0f, 2.0f);
    }

    float AnimationManager::EaseInOutBack(float t) {
        const float c1 = 1.70158f;
        const float c2 = c1 * 1.525f;
        return t < 0.5f
            ? (powf(2.0f * t, 2.0f) * ((c2 + 1.0f) * 2.0f * t - c2)) / 2.0f
            : (powf(2.0f * t - 2.0f, 2.0f) * ((c2 + 1.0f) * (t * 2.0f - 2.0f) + c2) + 2.0f) / 2.0f;
    }

    float AnimationManager::EaseInElastic(float t) {
        const float c4 = (2.0f * IM_PI) / 3.0f;
        if (t == 0.0f) return 0.0f;
        if (t == 1.0f) return 1.0f;
        return -powf(2.0f, 10.0f * t - 10.0f) * sinf((t * 10.0f - 10.75f) * c4);
    }

    float AnimationManager::EaseOutElastic(float t) {
        const float c4 = (2.0f * IM_PI) / 3.0f;
        if (t == 0.0f) return 0.0f;
        if (t == 1.0f) return 1.0f;
        return powf(2.0f, -10.0f * t) * sinf((t * 10.0f - 0.75f) * c4) + 1.0f;
    }

    float AnimationManager::EaseInOutElastic(float t) {
        const float c5 = (2.0f * IM_PI) / 4.5f;
        if (t == 0.0f) return 0.0f;
        if (t == 1.0f) return 1.0f;
        return t < 0.5f
            ? -(powf(2.0f, 20.0f * t - 10.0f) * sinf((20.0f * t - 11.125f) * c5)) / 2.0f
            : (powf(2.0f, -20.0f * t + 10.0f) * sinf((20.0f * t - 11.125f) * c5)) / 2.0f + 1.0f;
    }

    float AnimationManager::EaseInBounce(float t) {
        return 1.0f - EaseOutBounce(1.0f - t);
    }

    float AnimationManager::EaseOutBounce(float t) {
        const float n1 = 7.5625f;
        const float d1 = 2.75f;

        if (t < 1.0f / d1) {
            return n1 * t * t;
        } else if (t < 2.0f / d1) {
            return n1 * (t -= 1.5f / d1) * t + 0.75f;
        } else if (t < 2.5f / d1) {
            return n1 * (t -= 2.25f / d1) * t + 0.9375f;
        } else {
            return n1 * (t -= 2.625f / d1) * t + 0.984375f;
        }
    }

    float AnimationManager::EaseInOutBounce(float t) {
        return t < 0.5f
            ? (1.0f - EaseOutBounce(1.0f - 2.0f * t)) / 2.0f
            : (1.0f + EaseOutBounce(2.0f * t - 1.0f)) / 2.0f;
    }

    // Apply easing function
    float AnimationManager::ApplyEasing(float t, EasingType type) {
        switch (type) {
            case EasingType::Linear: return Linear(t);
            case EasingType::EaseInQuad: return EaseInQuad(t);
            case EasingType::EaseOutQuad: return EaseOutQuad(t);
            case EasingType::EaseInOutQuad: return EaseInOutQuad(t);
            case EasingType::EaseInCubic: return EaseInCubic(t);
            case EasingType::EaseOutCubic: return EaseOutCubic(t);
            case EasingType::EaseInOutCubic: return EaseInOutCubic(t);
            case EasingType::EaseInQuart: return EaseInQuart(t);
            case EasingType::EaseOutQuart: return EaseOutQuart(t);
            case EasingType::EaseInOutQuart: return EaseInOutQuart(t);
            case EasingType::EaseInExpo: return EaseInExpo(t);
            case EasingType::EaseOutExpo: return EaseOutExpo(t);
            case EasingType::EaseInOutExpo: return EaseInOutExpo(t);
            case EasingType::EaseInBack: return EaseInBack(t);
            case EasingType::EaseOutBack: return EaseOutBack(t);
            case EasingType::EaseInOutBack: return EaseInOutBack(t);
            case EasingType::EaseInElastic: return EaseInElastic(t);
            case EasingType::EaseOutElastic: return EaseOutElastic(t);
            case EasingType::EaseInOutElastic: return EaseInOutElastic(t);
            case EasingType::EaseInBounce: return EaseInBounce(t);
            case EasingType::EaseOutBounce: return EaseOutBounce(t);
            case EasingType::EaseInOutBounce: return EaseInOutBounce(t);
            default: return t;
        }
    }

    // Animation creation and control
    void AnimationManager::StartAnimation(const std::string& id, float from, float to, float duration, 
                                        EasingType easing, float delay, std::function<void()> onComplete) {
        AnimationState& anim = animations[id];
        anim.startValue = from;
        anim.targetValue = to;
        anim.currentValue = from;
        anim.duration = duration;
        anim.startTime = currentTime;
        anim.delay = delay;
        anim.isActive = true;
        anim.isCompleted = false;
        anim.easing = easing;
        anim.onComplete = onComplete;
    }

    void AnimationManager::StopAnimation(const std::string& id) {
        if (animations.find(id) != animations.end()) {
            animations[id].isActive = false;
            animations[id].isCompleted = true;
        }
    }

    void AnimationManager::PauseAnimation(const std::string& id) {
        if (animations.find(id) != animations.end()) {
            animations[id].isActive = false;
        }
    }

    void AnimationManager::ResumeAnimation(const std::string& id) {
        if (animations.find(id) != animations.end()) {
            animations[id].isActive = true;
        }
    }

    float AnimationManager::GetAnimationValue(const std::string& id) {
        if (animations.find(id) != animations.end()) {
            return animations[id].currentValue;
        }
        return 0.0f;
    }

    bool AnimationManager::IsAnimationActive(const std::string& id) {
        if (animations.find(id) != animations.end()) {
            return animations[id].isActive;
        }
        return false;
    }

    bool AnimationManager::IsAnimationCompleted(const std::string& id) {
        if (animations.find(id) != animations.end()) {
            return animations[id].isCompleted;
        }
        return false;
    }

    // Window animations
    void AnimationManager::ShowWindow(float duration, AnimationType type) {
        windowState.isVisible = true;
        windowState.isAnimating = true;
        
        // Start individual property animations
        StartAnimation("window_alpha", 0.0f, 1.0f, duration, EasingType::EaseOutCubic);
        
        switch (type) {
            case AnimationType::FadeIn:
                // FadeIn uses only alpha animation, no scaling
                break;
            case AnimationType::ScaleWithBounce:
                StartAnimation("window_scale", 0.7f, 1.0f, duration, EasingType::EaseOutBack);
                break;
            case AnimationType::ScaleIn:
                StartAnimation("window_scale", 0.8f, 1.0f, duration, EasingType::EaseOutCubic);
                break;
            case AnimationType::SlideUp:
                StartAnimation("window_slide", 50.0f, 0.0f, duration, EasingType::EaseOutCubic);
                break;
            default:
                StartAnimation("window_scale", 0.8f, 1.0f, duration, EasingType::EaseOutCubic);
                break;
        }
    }

    void AnimationManager::HideWindow(float duration, AnimationType type) {
        windowState.isAnimating = true;
        
        // Start hide animations with lambda capture fix
        auto self = this;
        StartAnimation("window_alpha", GetAnimationValue("window_alpha"), 0.0f, duration, EasingType::EaseInCubic, 0.0f, 
                      [self]() { 
                          self->windowState.isVisible = false; 
                          self->windowState.isAnimating = false; 
                      });
        
        switch (type) {
            case AnimationType::FadeOut:
                // FadeOut uses only alpha animation, no scaling
                break;
            case AnimationType::ScaleOut:
                StartAnimation("window_scale", GetAnimationValue("window_scale"), 0.8f, duration, EasingType::EaseInCubic);
                break;
            case AnimationType::SlideDown:
                StartAnimation("window_slide", GetAnimationValue("window_slide"), 30.0f, duration, EasingType::EaseInCubic);
                break;
            default:
                StartAnimation("window_scale", GetAnimationValue("window_scale"), 0.8f, duration, EasingType::EaseInCubic);
                break;
        }
    }

    void AnimationManager::UpdateWindowAnimation() {
        windowState.alpha = GetAnimationValue("window_alpha");
        windowState.scale = GetAnimationValue("window_scale");
        
        // Check if animation is complete
        if (windowState.isAnimating && 
            !IsAnimationActive("window_alpha") && 
            !IsAnimationActive("window_scale")) {
            windowState.isAnimating = false;
        }
    }

    // Tab transition animations
    void AnimationManager::StartTabTransition(int fromTab, int toTab, float duration, AnimationType type) {
        tabState.fromTab = fromTab;
        tabState.toTab = toTab;
        tabState.isTransitioning = true;
        tabState.transitionType = type;
        tabState.startTime = currentTime;
        
        auto self = this;
        StartAnimation("tab_transition", 0.0f, 1.0f, duration, EasingType::EaseInOutCubic, 0.0f,
                      [self]() { 
                          self->tabState.isTransitioning = false; 
                      });
    }

    void AnimationManager::UpdateTabTransition() {
        tabState.progress = GetAnimationValue("tab_transition");
    }

    // Content element animations
    void AnimationManager::StartContentAnimation(int elementCount, float staggerDelay, float duration) {
        contentState.hasStarted = true;
        contentState.elementIndex = 0;
        contentState.staggerDelay = staggerDelay;
        
        // Start staggered animations for each element
        for (int i = 0; i < elementCount; i++) {
            std::string alphaId = "content_alpha_" + std::to_string(i);
            std::string slideId = "content_slide_" + std::to_string(i);
            std::string scaleId = "content_scale_" + std::to_string(i);
            
            float delay = i * staggerDelay;
            
            StartAnimation(alphaId, 0.0f, 1.0f, duration, EasingType::EaseOutCubic, delay);
            StartAnimation(slideId, 30.0f, 0.0f, duration, EasingType::EaseOutBack, delay);
            StartAnimation(scaleId, 0.95f, 1.0f, duration, EasingType::EaseOutBack, delay);
        }
    }

    void AnimationManager::ResetContentAnimation() {
        contentState.hasStarted = false;
        contentState.elementIndex = 0;
    }

    bool AnimationManager::ShouldRenderElement(int elementIndex) {
        std::string alphaId = "content_alpha_" + std::to_string(elementIndex);
        return GetAnimationValue(alphaId) > 0.01f;
    }

    float AnimationManager::GetElementAlpha(int elementIndex) {
        std::string alphaId = "content_alpha_" + std::to_string(elementIndex);
        return GetAnimationValue(alphaId);
    }

    float AnimationManager::GetElementSlideOffset(int elementIndex) {
        std::string slideId = "content_slide_" + std::to_string(elementIndex);
        return GetAnimationValue(slideId);
    }

    float AnimationManager::GetElementScale(int elementIndex) {
        std::string scaleId = "content_scale_" + std::to_string(elementIndex);
        return GetAnimationValue(scaleId);
    }

    // Popup animations
    void AnimationManager::ShowPopup(float duration, AnimationType type) {
        popupState.isVisible = true;
        popupState.isAnimating = true;
        
        StartAnimation("popup_alpha", 0.0f, 1.0f, duration, EasingType::EaseOutCubic);
        StartAnimation("popup_bg_dim", 0.0f, 0.6f, duration, EasingType::EaseOutCubic);
        
        switch (type) {
            case AnimationType::ScaleIn:
                StartAnimation("popup_scale", 0.8f, 1.0f, duration, EasingType::EaseOutBack);
                break;
            case AnimationType::SlideUp:
                StartAnimation("popup_slide", 50.0f, 0.0f, duration, EasingType::EaseOutCubic);
                break;
            default:
                StartAnimation("popup_scale", 0.9f, 1.0f, duration, EasingType::EaseOutCubic);
                break;
        }
    }

    void AnimationManager::HidePopup(float duration, AnimationType type) {
        popupState.isAnimating = true;
        
        auto self = this;
        StartAnimation("popup_alpha", GetAnimationValue("popup_alpha"), 0.0f, duration, EasingType::EaseInCubic, 0.0f,
                      [self]() { 
                          self->popupState.isVisible = false; 
                          self->popupState.isAnimating = false; 
                      });
        StartAnimation("popup_bg_dim", GetAnimationValue("popup_bg_dim"), 0.0f, duration, EasingType::EaseInCubic);
        
        switch (type) {
            case AnimationType::ScaleOut:
                StartAnimation("popup_scale", GetAnimationValue("popup_scale"), 0.8f, duration, EasingType::EaseInCubic);
                break;
            case AnimationType::SlideDown:
                StartAnimation("popup_slide", GetAnimationValue("popup_slide"), 30.0f, duration, EasingType::EaseInCubic);
                break;
            default:
                StartAnimation("popup_scale", GetAnimationValue("popup_scale"), 0.9f, duration, EasingType::EaseInCubic);
                break;
        }
    }

    void AnimationManager::UpdatePopupAnimation() {
        popupState.alpha = GetAnimationValue("popup_alpha");
        popupState.scale = GetAnimationValue("popup_scale");
        popupState.backgroundDim = GetAnimationValue("popup_bg_dim");
    }

    // Utility functions
    ImVec2 AnimationManager::ApplyScaleToCenter(ImVec2 originalPos, ImVec2 originalSize, float scale) {
        ImVec2 center = ImVec2(originalPos.x + originalSize.x * 0.5f, originalPos.y + originalSize.y * 0.5f);
        ImVec2 scaledSize = ImVec2(originalSize.x * scale, originalSize.y * scale);
        return ImVec2(center.x - scaledSize.x * 0.5f, center.y - scaledSize.y * 0.5f);
    }

    void AnimationManager::PushAnimatedStyle(float alpha, float scale, ImVec2 offset) {
        ImGui::PushStyleVar(ImGuiStyleVar_Alpha, alpha);
        if (scale != 1.0f) {
            ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(ImGui::GetStyle().FramePadding.x * scale, ImGui::GetStyle().FramePadding.y * scale));
        }
    }

    void AnimationManager::PopAnimatedStyle() {
        ImGui::PopStyleVar(); // Alpha
        // Note: Only pop FramePadding if it was pushed
    }

    // Advanced animations
    void AnimationManager::CreateSpringAnimation(const std::string& id, float from, float to, float tension, float friction) {
        // Spring animation implementation would go here
        // For now, fall back to regular animation with elastic easing
        StartAnimation(id, from, to, 0.8f, EasingType::EaseOutElastic);
    }

    void AnimationManager::CreateWaveAnimation(const std::string& id, float amplitude, float frequency, float duration) {
        // Wave animation implementation would go here
        // For now, fall back to regular animation
        StartAnimation(id, 0.0f, amplitude, duration, EasingType::EaseInOutCubic);
    }

    // AnimatedElement implementation
    void AnimatedElement::BeginElement(bool visible, float fadeTime, float slideDistance) {
        if (visible && !wasVisible) {
            // Element is becoming visible
            g_AnimationManager.StartAnimation(id + "_alpha", 0.0f, 1.0f, fadeTime, EasingType::EaseOutCubic);
            g_AnimationManager.StartAnimation(id + "_slide", slideDistance, 0.0f, fadeTime, EasingType::EaseOutBack);
        } else if (!visible && wasVisible) {
            // Element is becoming hidden
            g_AnimationManager.StartAnimation(id + "_alpha", g_AnimationManager.GetAnimationValue(id + "_alpha"), 0.0f, fadeTime * 0.6f, EasingType::EaseInCubic);
            g_AnimationManager.StartAnimation(id + "_slide", g_AnimationManager.GetAnimationValue(id + "_slide"), slideDistance * 0.5f, fadeTime * 0.6f, EasingType::EaseInCubic);
        }
        
        wasVisible = visible;
        
        // Apply current animation values
        float alpha = g_AnimationManager.GetAnimationValue(id + "_alpha");
        float slideOffset = g_AnimationManager.GetAnimationValue(id + "_slide");
        
        if (alpha > 0.01f) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, alpha);
            
            // Apply slide offset
            ImVec2 currentPos = ImGui::GetCursorPos();
            ImGui::SetCursorPos(ImVec2(currentPos.x, currentPos.y + slideOffset));
        }
    }

    void AnimatedElement::EndElement() {
        if (GetAlpha() > 0.01f) {
            ImGui::PopStyleVar(); // Alpha
        }
    }

    bool AnimatedElement::IsVisible() {
        return GetAlpha() > 0.01f;
    }

    float AnimatedElement::GetAlpha() {
        return g_AnimationManager.GetAnimationValue(id + "_alpha");
    }

} // namespace AnimationSystem 