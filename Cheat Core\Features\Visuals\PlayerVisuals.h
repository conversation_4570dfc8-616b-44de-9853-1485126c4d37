#pragma once
#include <vector>
#include <string>
#include "../../Framwork/Vectors.h"
#include "../Caching/Cache.h"

// Player visuals system
class PlayerVisuals {
public:
    // Main function for drawing all player ESP
    static void DrawAllPlayers();
    
    // Helper functions
    static float CalculateDistance(Vector3& HeadBone3D);
    static void HandleToggleKeys();
    
    // Processing functions
    static void ProcessPlayerESP(const CachedPlayer& player);
    static void DrawItems();
    static bool hasTarget;
    static bool isReady;
};