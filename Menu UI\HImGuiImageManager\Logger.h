#pragma once
#include <string>
#include <fstream>
#include <iostream>
#include <ctime>
#include <sstream>
#include <filesystem>

class Logger {
private:
    static const std::filesystem::path BASE_LOG_PATH;
    
    static void EnsureLogDirectoryExists() {
        try {
            if (!std::filesystem::exists(BASE_LOG_PATH)) {
                std::filesystem::create_directories(BASE_LOG_PATH);
            }
        } catch (const std::exception& e) {
            // If we can't create the directory, we'll attempt to write to the executable's directory
            std::cerr << "Failed to create log directory: " << e.what() << std::endl;
        }
    }

public:
    static void Log(const std::string& message, const std::string& type = "INFO") {
        try {
            EnsureLogDirectoryExists();

            auto now = std::chrono::system_clock::now();
            auto time = std::chrono::system_clock::to_time_t(now);
            std::stringstream ss;
            ss << std::put_time(std::localtime(&time), "%Y-%m-%d");
            
            auto logPath = BASE_LOG_PATH / ("gif_loader_" + ss.str() + ".log");
            std::ofstream logFile(logPath, std::ios::app);
            
            if (logFile.is_open()) {
                ss.str("");
                ss << std::put_time(std::localtime(&time), "%Y-%m-%d %H:%M:%S");
                logFile << "[" << ss.str() << "] [" << type << "] " << message << std::endl;
                logFile.close();
            } else {
                std::cerr << "Failed to open log file: " << logPath << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "Logging failed: " << e.what() << std::endl;
        }
    }

    static void LogError(const std::string& message) {
        Log(message, "ERROR");
    }

    static void LogWarning(const std::string& message) {
        Log(message, "WARNING");
    }

    static void LogDebug(const std::string& message) {
        Log(message, "DEBUG");
    }
};

// Define the base log path
const std::filesystem::path Logger::BASE_LOG_PATH = "C:\\BitCheats\\Logs"; 