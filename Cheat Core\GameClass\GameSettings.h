#pragma once
#include <unordered_map>
#include <vector>
#include <string>
#include <winuser.rh>
#include "../Features/Aimbot/Aimbot.h"

// =============================================================================
// ENUMERATIONS
// =============================================================================

enum class Language : int {
    English = 0,
    French = 1,
    Spanish = 2,
    COUNT
};

enum class GameMode : int {
    BattleRoyale = 0,
    ZeroBuild = 1,
    Arena = 2,
    Creative = 3,
    COUNT
};

enum class WeaponRarity : int {
    Common = 0,
    Uncommon = 1,
    Rare = 2,
    Epic = 3,
    Legendary = 4,
    Mythic = 5,
    COUNT
};

enum class FontFamily : int {
    Arial = 0,
    Calibri = 1,
    Verdana = 2,
    TimesNewRoman = 3,
    COUNT
};

enum class BoxType : int {
    Corner = 0,
    Full = 1,
    Rounded = 2,
    COUNT
};

enum class LinePosition : int {
    Top = 0,
    Center = 1,
    Bottom = 2,
    COUNT
};

enum class CrosshairType : int {
    Cross = 0,
    Circle = 1,
    Dot = 2,
    COUNT
};

enum class TargetIndicatorType : int {
    Circle = 0,
    Square = 1,
    Triangle = 2,
    COUNT
};

enum class RadarType : int {
    Circle = 0,
    Rectangle = 1,
    COUNT
};

// =============================================================================
// COLOR STRUCTURES
// =============================================================================

struct ColorRGBA {
    float r = 1.0f, g = 1.0f, b = 1.0f, a = 1.0f;

    ColorRGBA() = default;
    ColorRGBA(float red, float green, float blue, float alpha = 1.0f)
        : r(red), g(green), b(blue), a(alpha) {}
};

struct ColorRGB {
    float r = 1.0f, g = 1.0f, b = 1.0f;

    ColorRGB() = default;
    ColorRGB(float red, float green, float blue)
        : r(red), g(green), b(blue) {}
};

// =============================================================================
// VISUAL SETTINGS STRUCTURES
// =============================================================================

struct FontSettings {
    FontFamily family = FontFamily::Arial;
    float size = 14.0f;
    bool bold = false;
    bool italic = false;
    bool outline = true;
    float outlineThickness = 1.0f;
    ColorRGBA color = ColorRGBA(1.0f, 1.0f, 1.0f, 1.0f);
    ColorRGBA outlineColor = ColorRGBA(0.0f, 0.0f, 0.0f, 1.0f);
};

struct BoxVisual {
    bool enabled = true;
    BoxType type = BoxType::Corner;
    bool filled = false;
    float thickness = 2.0f;
    float rounding = 0.0f;
    ColorRGBA visibleColor = ColorRGBA(0.0f, 1.0f, 0.0f, 1.0f);
    ColorRGBA nonVisibleColor = ColorRGBA(1.0f, 1.0f, 1.0f, 1.0f);
    ColorRGBA knockedVisibleColor = ColorRGBA(0.0f, 0.0f, 1.0f, 1.0f);
    ColorRGBA knockedNonVisibleColor = ColorRGBA(1.0f, 1.0f, 1.0f, 0.5f);
    ColorRGBA fillColor = ColorRGBA(0.0f, 0.0f, 0.0f, 0.22f);
};

struct LineVisual {
    bool enabled = true;
    LinePosition position = LinePosition::Center;
    float thickness = 2.0f;
    ColorRGB visibleColor = ColorRGB(0.0f, 1.0f, 0.0f);
    ColorRGB nonVisibleColor = ColorRGB(1.0f, 1.0f, 1.0f);
    ColorRGB knockedVisibleColor = ColorRGB(0.0f, 0.0f, 1.0f);
    ColorRGB knockedNonVisibleColor = ColorRGB(1.0f, 1.0f, 1.0f);
};

struct SkeletonVisual {
    bool enabled = true;
    bool curved = true;
    float thickness = 2.0f;
    float rounding = 10.0f;
    ColorRGB visibleColor = ColorRGB(0.0f, 1.0f, 0.0f);
    ColorRGB nonVisibleColor = ColorRGB(1.0f, 1.0f, 1.0f);
    ColorRGB knockedVisibleColor = ColorRGB(0.0f, 0.0f, 1.0f);
    ColorRGB knockedNonVisibleColor = ColorRGB(1.0f, 1.0f, 1.0f);
};

struct CircleVisual {
    bool enabled = true;
    float thickness = 2.0f;
    ColorRGB visibleColor = ColorRGB(0.0f, 1.0f, 0.0f);
    ColorRGB nonVisibleColor = ColorRGB(1.0f, 1.0f, 1.0f);
    ColorRGB knockedVisibleColor = ColorRGB(0.0f, 0.0f, 1.0f);
    ColorRGB knockedNonVisibleColor = ColorRGB(1.0f, 1.0f, 1.0f);
};

struct TextVisual {
    bool enabled = true;
    FontSettings font;
    bool showDistance = true;
    bool showIcons = true;
    float iconSize = 20.0f;
};

// =============================================================================
// AIMBOT SYSTEM
// =============================================================================

struct AimbotVisuals {
    bool drawFov = true;
    bool drawFovFilled = false;
    bool drawFovOutline = true;
    bool drawFovRgb = true;
    ColorRGB fovColor = ColorRGB(0.0f, 1.0f, 0.0f);

    bool drawCrosshair = false;
    CrosshairType crosshairType = CrosshairType::Cross;
    ColorRGB crosshairColor = ColorRGB(1.0f, 0.0f, 0.0f);

    bool drawTarget = true;
    TargetIndicatorType targetType = TargetIndicatorType::Circle;
    ColorRGB targetColor = ColorRGB(1.0f, 1.0f, 0.0f);

    float drawThickness = 2.0f;
    float drawSize = 20.0f;
};

struct AimbotCore {
    bool enabled = true;
    bool saveTarget = true;
    bool aimLock = false;
    bool visibilityCheck = true;
    bool humanizedSmooth = true;
    bool weaponOnly = true;
    bool ignoreDowned = true;
    bool predict = false;
    bool playerAi = true;
    bool perWeapon = false;

    int hitBox = 0; // 0=Head, 1=Neck, 2=Chest, etc.
    float fov = 200.0f;
    float smooth = 2.0f;
    float humanizedSmoothPercent = 2.0f;
    int maxDistance = 600;

    AimbotVisuals visuals;
};

struct AimbotSettings {
    AimbotCore general;
    std::unordered_map<int, AimbotCore> perWeaponSettings;
};

// =============================================================================
// TRIGGERBOT SYSTEM
// =============================================================================

struct TriggerbotCore {
    bool enabled = true;
    bool enableAllWeapons = false;
    bool enableOnlyShotguns = true;
    bool perWeapon = false;

    int delay = 1;
    float maxDistance = 10.0f;
    int hotkey = VK_RBUTTON;
};

struct TriggerbotSettings {
    TriggerbotCore general;
    std::unordered_map<int, TriggerbotCore> perWeaponSettings;
};

// =============================================================================
// PLAYER ESP SYSTEM
// =============================================================================

struct PlayerInfoDisplay {
    TextVisual nickname;
    TextVisual distance;
    TextVisual platform;
    TextVisual kills;
    TextVisual level;
    TextVisual rank;
    TextVisual weapon;
    bool weaponRarity = true;
    bool weaponAmmo = true;
};

struct PlayerESPSettings {
    bool enabled = true;
    int maxDistance = 800;
    bool teamCheck = true;
    bool playerAi = true;
    bool ignoreDowned = false;

    BoxVisual box;
    LineVisual lines;
    SkeletonVisual skeleton;
    CircleVisual headCircle;
    PlayerInfoDisplay info;
};

// =============================================================================
// ITEM ESP SYSTEM
// =============================================================================

struct ItemCategory {
    bool enabled = false;
    TextVisual display;
    int maxDistance = 300;

    // Specific items (can be expanded per category)
    std::unordered_map<std::string, bool> items;
};

struct ItemESPSettings {
    ItemCategory consumables;
    ItemCategory weapons;
    ItemCategory ammo;
    ItemCategory others;

    // Weapon rarity filter
    bool rarityFilter[static_cast<int>(WeaponRarity::COUNT)] = { true, true, true, true, true, true };

    void InitializeDefaults() {
        // Initialize consumables
        consumables.items = {
            {"Bandages", true},
            {"Medkit", true},
            {"SmallShieldPotion", true},
            {"ShieldPotion", true},
            {"FlowBerryFizz", true},
            {"ChugSplash", true},
            {"NitroSplash", true},
            {"NukaCola", true}
        };

        // Initialize weapons - ALL from original code
        weapons.enabled = true;
        weapons.items = {
            // Season weapons
            {"RangerPistol", true},
            {"HarbingerSMG", true},
            {"ThunderBurstSMG", true},
            {"WarforgedAssaultRifle", true},
            {"TacticalAssaultRifle", true},
            {"EnforcerAssaultRifle", true},
            {"CombatAssaultRifle", true},
            {"CombatShotgun", true},
            {"GatekeeperShotgun", true},
            {"HammerPumpShotgun", true},
            {"FrenzyAutoShotgun", true},
            {"HandCannon", true},
            {"HuntressDMR", true},
            {"BoomBolt", true},
            {"NitroFists", true},
            {"Shockwavegrenade", true},
            {"HeavyImpactSniperRifle", true},

            // Classic weapons
            {"MKSevenAssaultRifle", true},
            {"NewPump_Shotgun", true},
            {"OGPump_Shotgun", true},
            {"BottomlessChugJug", true},
            {"BurstAR", true},
            {"DrumGun", true},
            {"SkyesAR", true},
            {"Grappler", true},
            {"StingerSMG", true},

            // Heisted weapons
            {"HeistedBreacherShotgun", true},
            {"HeistedAccelerantShotgun", true},
            {"HeistedExplosiveAR", true},
            {"HeistedBlinkMagSMG", true},
            {"HeistedRunGunSMG", true},

            // Standard weapons
            {"TacticalShotgun", true},
            {"LeverActionShotgun", true},
            {"HeavyShotgun", true},
            {"RangerShotgun", true},
            {"AssaultRifle", true},
            {"ScarAssaultRifle", true},
            {"HammerAssaultRifle", true},
            {"HeavyAssaultRifle", true},
            {"InfantryRifle", true},
            {"SubmachineGun", true},
            {"TacticalSubmachineGun", true},
            {"BoltActionSniperRifle", true},
            {"HuntingRifle", true},
            {"Pistol", true},
            {"Revolver", true},
            {"RocketLauncher", true},
            {"CrashPad", true},

            // Special weapons
            {"FireflyJar", true},
            {"SovereignShotgun", true},
            {"StrikerAR", true},
            {"HyperSMG", true},
            {"StrikerBurstRifle", true},
            {"DualMicroSMGs", true},
            {"MonarchPistol", true},
            {"DualPistols", true},
            {"RapidFireSMG", true},
            {"SuppressedSMG", true},
            {"SuppressedAssaultRifle", true},
            {"HeavySniperRifle", true},
            {"SemiAutomaticSniperRifle", true},
            {"GrenadeLauncher", true},
            {"RemoteExplosives", true},
            {"RegularGrenades", true},
            {"StinkBomb", true},
            {"BandageBazooka", true},
            {"BoogieBomb", true},
            {"Clinger", true}
        };

        // Initialize ammo
        ammo.items = {
            {"AmmoLight", true},
            {"AmmoMedium", true},
            {"AmmoHeavy", true},
            {"AmmoShells", true},
            {"AmmoRockets", true}
        };

        // Initialize others
        others.enabled = true;
        others.items = {
            {"Chest", true},
            {"Vehicle", true},
            {"Llama", true},
            {"SupplyDrop", true}
        };
    }
};

// =============================================================================
// RADAR SYSTEM
// =============================================================================

struct RadarSettings {
    bool enabled = true;
    bool showDistance = true;

    // Position and size
    int positionX = 76;
    int positionY = 325;
    int circleSize = 120;
    int rectangleSize = 200;
    RadarType type = RadarType::Circle;

    // Colors
    bool useVisibleColor = true;
    bool useClosestColor = true;
    bool useAimingAtMeColor = true;
    ColorRGB visibleColor = ColorRGB(0.0f, 1.0f, 0.0f);
    ColorRGB nonVisibleColor = ColorRGB(1.0f, 1.0f, 1.0f);
    ColorRGB knockedVisibleColor = ColorRGB(0.0f, 0.0f, 1.0f);
    ColorRGB knockedNonVisibleColor = ColorRGB(1.0f, 1.0f, 1.0f);

    // Settings
    int maxDistance = 100;
    FontSettings distanceFont;
};

// =============================================================================
// HOTKEY SYSTEM
// =============================================================================

struct HotkeyBinding {
    int key = 0;
    bool isToggle = true; // true = toggle, false = hold

    HotkeyBinding() = default;
    HotkeyBinding(int keyCode, bool toggle = true) : key(keyCode), isToggle(toggle) {}
};

struct GlobalHotkeys {
    HotkeyBinding toggle = HotkeyBinding(VK_F6);
    HotkeyBinding togglePlayers = HotkeyBinding(VK_F7);
    HotkeyBinding toggleItems = HotkeyBinding(VK_F8);
    HotkeyBinding toggleRadar = HotkeyBinding(VK_F9);
    HotkeyBinding panicMode = HotkeyBinding(VK_F10);
    HotkeyBinding menu = HotkeyBinding(VK_INSERT);

    HotkeyBinding holdTrigger = HotkeyBinding(VK_RBUTTON, false);
    HotkeyBinding holdPrimary = HotkeyBinding(VK_RBUTTON, false);
    HotkeyBinding holdSecondary = HotkeyBinding(VK_RBUTTON, false);
    HotkeyBinding switchToHead = HotkeyBinding(VK_SHIFT, false);
};

struct FeatureHotkeys {
    // Aimbot
    HotkeyBinding aimbotEnable;
    HotkeyBinding aimbotAimLock;
    HotkeyBinding aimbotPrediction;
    HotkeyBinding aimbotSaveTarget;
    HotkeyBinding aimbotVisibilityCheck;
    HotkeyBinding aimbotHumanizedSmooth;
    HotkeyBinding aimbotIgnoreDowned;
    HotkeyBinding aimbotPlayerAi;
    HotkeyBinding aimbotWeaponOnly;
    HotkeyBinding aimbotDrawFov;
    HotkeyBinding aimbotDrawCrosshair;
    HotkeyBinding aimbotDrawTarget;

    // Player ESP
    HotkeyBinding playerEspEnable;
    HotkeyBinding playerEspBox;
    HotkeyBinding playerEspSkeleton;
    HotkeyBinding playerEspHeadCircle;
    HotkeyBinding playerEspLines;
    HotkeyBinding playerEspDistance;
    HotkeyBinding playerEspNickname;
    HotkeyBinding playerEspPlatform;
    HotkeyBinding playerEspKills;
    HotkeyBinding playerEspLevel;
    HotkeyBinding playerEspRank;
    HotkeyBinding playerEspIgnoreDowned;

    // Item ESP
    HotkeyBinding itemEspEnable;
    HotkeyBinding itemConsumableEnable;
    HotkeyBinding itemWeaponEnable;
    HotkeyBinding itemAmmoEnable;
    HotkeyBinding itemOtherEnable;

    // Radar
    HotkeyBinding radarEnable;
    HotkeyBinding radarDistance;
    HotkeyBinding radarVisibleColor;
    HotkeyBinding radarClosestColor;
    HotkeyBinding radarAimingAtMeColor;
};

struct HotkeySettings {
    GlobalHotkeys global;
    FeatureHotkeys features;
};

// =============================================================================
// CONFIGURATION SYSTEM
// =============================================================================

struct ConfigurationSettings {
    int currentConfig = 0;
    int previousConfig = 0;
    int selectedWeapon = 0;
    GameMode gameMode = GameMode::BattleRoyale;
    Language language = Language::English;
    bool showFeatureDefinitions = true;
    bool autoSave = true;
    int autoSaveInterval = 300; // seconds
};

// =============================================================================
// LOCALIZATION SYSTEM
// =============================================================================

struct LocalizationManager {
    static std::unordered_map<std::string, std::unordered_map<Language, std::string>> translations;

    static const std::string& GetText(const std::string& key, Language lang = Language::English) {
        auto keyIt = translations.find(key);
        if (keyIt != translations.end()) {
            auto langIt = keyIt->second.find(lang);
            if (langIt != keyIt->second.end()) {
                return langIt->second;
            }
        }
        // Fallback to English or return key if not found
        static std::string fallback = key;
        return fallback;
    }

    static void InitializeTranslations() {
        // ========== AIMBOT TRANSLATIONS ==========
        translations["aimbot.title"] = {
            {Language::English, "Aimbot"},
            {Language::French, "Aimbot"},
            {Language::Spanish, "Aimbot"}
        };

        translations["aimbot.enable"] = {
            {Language::English, "Enable Aimbot"},
            {Language::French, "Activer l'Aimbot"},
            {Language::Spanish, "Activar Aimbot"}
        };
        translations["aimbot.enable.desc"] = {
            {Language::English, "Activate automated aiming assistance"},
            {Language::French, "Activer l'assistance de visée automatisée"},
            {Language::Spanish, "Activar la asistencia de puntería automática"}
        };

        translations["aimbot.save_target"] = {
            {Language::English, "Save Target"},
            {Language::French, "Sauvegarder la Cible"},
            {Language::Spanish, "Guardar Objetivo"}
        };
        translations["aimbot.save_target.desc"] = {
            {Language::English, "Remember the last targeted enemy"},
            {Language::French, "Mémoriser le dernier ennemi ciblé"},
            {Language::Spanish, "Recordar el último enemigo apuntado"}
        };

        translations["aimbot.aim_lock"] = {
            {Language::English, "Aim Lock"},
            {Language::French, "Verrouillage de Visee"},
            {Language::Spanish, "Bloqueo de Punteria"}
        };
        translations["aimbot.aim_lock.desc"] = {
            {Language::English, "Keep the crosshair locked on the current target"},
            {Language::French, "Maintenir le réticule verrouillé sur la cible actuelle"},
            {Language::Spanish, "Mantener la mira bloqueada en el objetivo actual"}
        };

        translations["aimbot.visibility_check"] = {
            {Language::English, "Visibility Check"},
            {Language::French, "Verification de Visibilite"},
            {Language::Spanish, "Verificacion de Visibilidad"}
        };
        translations["aimbot.visibility_check.desc"] = {
            {Language::English, "Only aim at targets that are visible"},
            {Language::French, "Viser uniquement les cibles visibles"},
            {Language::Spanish, "Apuntar solo a objetivos visibles"}
        };

        translations["aimbot.humanized_smooth"] = {
            {Language::English, "Humanized Smooth"},
            {Language::French, "Lissage Humanise"},
            {Language::Spanish, "Suavizado Humanizado"}
        };
        translations["aimbot.humanized_smooth.desc"] = {
            {Language::English, "Add smooth movement to look more human"},
            {Language::French, "Ajouter un mouvement fluide pour paraître plus humain"},
            {Language::Spanish, "Añade movimiento suave para parecer más humano"}
        };

        translations["aimbot.weapon_only"] = {
            {Language::English, "Weapon Only"},
            {Language::French, "Arme Uniquement"},
            {Language::Spanish, "Solo Arma"}
        };
        translations["aimbot.weapon_only.desc"] = {
            {Language::English, "Activate only when holding a weapon"},
            {Language::French, "Activer uniquement lorsque vous tenez une arme"},
            {Language::Spanish, "Activar solo cuando se sostiene un arma"}
        };

        translations["aimbot.ignore_downed"] = {
            {Language::English, "Ignore Downed"},
            {Language::French, "Ignorer les Abattus"},
            {Language::Spanish, "Ignorar Derribados"}
        };
        translations["aimbot.ignore_downed.desc"] = {
            {Language::English, "Do not target downed players"},
            {Language::French, "Ne pas cibler les joueurs à terre"},
            {Language::Spanish, "No apuntar a jugadores derribados"}
        };

        translations["aimbot.predict"] = {
            {Language::English, "Predict Movement"},
            {Language::French, "Prédire le Mouvement"},
            {Language::Spanish, "Predecir Movimiento"}
        };
        translations["aimbot.predict.desc"] = {
            {Language::English, "Predict enemy movement for improved accuracy"},
            {Language::French, "Prédire le mouvement de l'ennemi pour une meilleure précision"},
            {Language::Spanish, "Predecir el movimiento del enemigo para mejorar la precisión"}
        };

        translations["aimbot.player_ai"] = {
            {Language::English, "Player AI"},
            {Language::French, "IA Joueur"},
            {Language::Spanish, "IA Jugador"}
        };
        translations["aimbot.player_ai.desc"] = {
            {Language::English, "Include AI players as valid targets"},
            {Language::French, "Inclure les joueurs IA comme cibles valides"},
            {Language::Spanish, "Incluir jugadores de IA como objetivos válidos"}
        };

        translations["aimbot.per_weapon"] = {
            {Language::English, "Per Weapon Settings"},
            {Language::French, "Paramètres par Arme"},
            {Language::Spanish, "Configuración por Arma"}
        };
        translations["aimbot.per_weapon.desc"] = {
            {Language::English, "Use separate settings for each weapon type"},
            {Language::French, "Utiliser des paramètres séparés pour chaque type d'arme"},
            {Language::Spanish, "Usar configuraciones separadas por tipo de arma"}
        };

        translations["aimbot.hitbox"] = {
            {Language::English, "Hitbox"},
            {Language::French, "Zone de Frappe"},
            {Language::Spanish, "Zona de Golpe"}
        };
        translations["aimbot.hitbox.desc"] = {
            {Language::English, "Choose the target body part"},
            {Language::French, "Choisir la partie du corps à viser"},
            {Language::Spanish, "Elegir la parte del cuerpo a apuntar"}
        };

        translations["aimbot.fov"] = {
            {Language::English, "FOV"},
            {Language::French, "Champ de Vision"},
            {Language::Spanish, "Campo de Vision"}
        };
        translations["aimbot.fov.desc"] = {
            {Language::English, "Adjust the aimbot field of view"},
            {Language::French, "Ajuster le champ de vision de l'aimbot"},
            {Language::Spanish, "Ajustar el campo de visión del aimbot"}
        };

        translations["aimbot.smooth"] = {
            {Language::English, "Smooth"},
            {Language::French, "Lissage"},
            {Language::Spanish, "Suavizado"}
        };
        translations["aimbot.smooth.desc"] = {
            {Language::English, "Control how fast the aim moves to the target"},
            {Language::French, "Contrôler la vitesse de déplacement du viseur vers la cible"},
            {Language::Spanish, "Controlar la velocidad del movimiento hacia el objetivo"}
        };

        translations["aimbot.max_distance"] = {
            {Language::English, "Max Distance"},
            {Language::French, "Distance Max"},
            {Language::Spanish, "Distancia Máxima"}
        };
        translations["aimbot.max_distance.desc"] = {
            {Language::English, "Maximum range to affect targets"},
            {Language::French, "Portée maximale pour affecter les cibles"},
            {Language::Spanish, "Alcance máximo para afectar objetivos"}
        };

        // ========== AIMBOT VISUALS ==========
        translations["aimbot.visuals.title"] = {
            {Language::English, "Aimbot Visuals"},
            {Language::French, "Visuels Aimbot"},
            {Language::Spanish, "Visuales Aimbot"}
        };

        translations["aimbot.draw_fov"] = {
            {Language::English, "Draw FOV"},
            {Language::French, "Afficher FOV"},
            {Language::Spanish, "Mostrar FOV"}
        };
        translations["aimbot.draw_fov.desc"] = {
            {Language::English, "Show a circle indicating the aimbot area"},
            {Language::French, "Afficher un cercle indiquant la zone de l'aimbot"},
            {Language::Spanish, "Mostrar un círculo indicando el área del aimbot"}
        };

        translations["aimbot.draw_fov_filled"] = {
            {Language::English, "FOV Filled"},
            {Language::French, "FOV Rempli"},
            {Language::Spanish, "FOV Relleno"}
        };
        translations["aimbot.draw_fov_filled.desc"] = {
            {Language::English, "Fill the FOV circle"},
            {Language::French, "Remplir le cercle du FOV"},
            {Language::Spanish, "Rellenar el círculo de FOV"}
        };

        translations["aimbot.draw_fov_outline"] = {
            {Language::English, "FOV Outline"},
            {Language::French, "Contour du FOV"},
            {Language::Spanish, "Contorno del FOV"}
        };
        translations["aimbot.draw_fov_outline.desc"] = {
            {Language::English, "Draw an outline around the FOV"},
            {Language::French, "Dessiner un contour autour du FOV"},
            {Language::Spanish, "Dibujar un contorno alrededor del FOV"}
        };

        translations["aimbot.draw_fov_rgb"] = {
            {Language::English, "FOV RGB Mode"},
            {Language::French, "Mode RVB FOV"},
            {Language::Spanish, "Modo RGB FOV"}
        };
        translations["aimbot.draw_fov_rgb.desc"] = {
            {Language::English, "Cycle colors across the FOV"},
            {Language::French, "Faire défiler les couleurs sur le FOV"},
            {Language::Spanish, "Ciclar colores en el FOV"}
        };

        translations["aimbot.draw_crosshair"] = {
            {Language::English, "Draw Crosshair"},
            {Language::French, "Afficher Réticule"},
            {Language::Spanish, "Mostrar Mira"}
        };
        translations["aimbot.draw_crosshair.desc"] = {
            {Language::English, "Display a custom crosshair on screen"},
            {Language::French, "Afficher un réticule personnalisé à l'écran"},
            {Language::Spanish, "Mostrar una mira personalizada en pantalla"}
        };

        translations["aimbot.draw_target"] = {
            {Language::English, "Draw Target"},
            {Language::French, "Afficher Cible"},
            {Language::Spanish, "Mostrar Objetivo"}
        };
        translations["aimbot.draw_target.desc"] = {
            {Language::English, "Draw an indicator to the current target"},
            {Language::French, "Dessiner un indicateur vers la cible actuelle"},
            {Language::Spanish, "Dibujar un indicador al objetivo actual"}
        };

        // ========== TRIGGERBOT TRANSLATIONS ==========
        translations["triggerbot.title"] = {
            {Language::English, "Triggerbot"},
            {Language::French, "Triggerbot"},
            {Language::Spanish, "Triggerbot"}
        };

        translations["triggerbot.enable"] = {
            {Language::English, "Enable Triggerbot"},
            {Language::French, "Activer Triggerbot"},
            {Language::Spanish, "Activar Triggerbot"}
        };
        translations["triggerbot.enable.desc"] = {
            {Language::English, "Automatically fire when an enemy is under the crosshair"},
            {Language::French, "Tirer automatiquement lorsqu'un ennemi est sous le réticule"},
            {Language::Spanish, "Disparar automáticamente cuando un enemigo está bajo la mira"}
        };

        translations["triggerbot.all_weapons"] = {
            {Language::English, "All Weapons"},
            {Language::French, "Toutes les Armes"},
            {Language::Spanish, "Todas las Armas"}
        };
        translations["triggerbot.all_weapons.desc"] = {
            {Language::English, "Apply to all weapons"},
            {Language::French, "S'applique à toutes les armes"},
            {Language::Spanish, "Aplicar a todas las armas"}
        };

        translations["triggerbot.shotguns_only"] = {
            {Language::English, "Shotguns Only"},
            {Language::French, "Fusils de Chasse Seulement"},
            {Language::Spanish, "Solo Escopetas"}
        };
        translations["triggerbot.shotguns_only.desc"] = {
            {Language::English, "Only active with shotguns"},
            {Language::French, "Actif uniquement avec les fusils de chasse"},
            {Language::Spanish, "Activo solo con escopetas"}
        };

        translations["triggerbot.delay"] = {
            {Language::English, "Delay"},
            {Language::French, "Délai"},
            {Language::Spanish, "Retraso"}
        };
        translations["triggerbot.delay.desc"] = {
            {Language::English, "Delay before firing (ms)"},
            {Language::French, "Délai avant de tirer (ms)"},
            {Language::Spanish, "Retraso antes de disparar (ms)"}
        };

        // ========== PLAYER ESP TRANSLATIONS ==========
        translations["esp.players.title"] = {
            {Language::English, "Player ESP"},
            {Language::French, "ESP Joueurs"},
            {Language::Spanish, "ESP Jugadores"}
        };

        translations["esp.players.enable"] = {
            {Language::English, "Enable Player ESP"},
            {Language::French, "Activer ESP Joueurs"},
            {Language::Spanish, "Activar ESP Jugadores"}
        };
        translations["esp.players.enable.desc"] = {
            {Language::English, "Show information and overlays for players"},
            {Language::French, "Afficher des informations et des superpositions pour les joueurs"},
            {Language::Spanish, "Mostrar información y superposiciones para jugadores"}
        };

        translations["esp.players.team_check.desc"] = {
            {Language::English, "Ignore teammates when displaying ESP"},
            {Language::French, "Ignorer les coéquipiers dans l'affichage ESP"},
            {Language::Spanish, "Ignorar compañeros en el ESP"}
        };
        translations["esp.players.ignore_downed.desc"] = {
            {Language::English, "Do not render downed players"},
            {Language::French, "Ne pas afficher les joueurs à terre"},
            {Language::Spanish, "No mostrar jugadores derribados"}
        };
        translations["esp.players.max_distance"] = {
            {Language::English, "Max Distance"},
            {Language::French, "Distance Max"},
            {Language::Spanish, "Distancia Maxima"}
        };
        translations["esp.players.max_distance.desc"] = {
            {Language::English, "Maximum distance to display player ESP"},
            {Language::French, "Distance maximale d'affichage de l'ESP joueur"},
            {Language::Spanish, "Distancia máxima para mostrar el ESP del jugador"}
        };

        translations["esp.players.team_check"] = {
            {Language::English, "Team Check"},
            {Language::French, "Verification Equipe"},
            {Language::Spanish, "Verificacion Equipo"}
        };

        translations["esp.players.ignore_downed"] = {
            {Language::English, "Ignore Downed"},
            {Language::French, "Ignorer Abattus"},
            {Language::Spanish, "Ignorar Derribados"}
        };

        translations["esp.players.box"] = {
            {Language::English, "Box"},
            {Language::French, "Boîte"},
            {Language::Spanish, "Caja"}
        };
        translations["esp.players.box.desc"] = {
            {Language::English, "Draw a box around players"},
            {Language::French, "Dessiner une boîte autour des joueurs"},
            {Language::Spanish, "Dibujar una caja alrededor de los jugadores"}
        };

        translations["esp.players.skeleton"] = {
            {Language::English, "Skeleton"},
            {Language::French, "Squelette"},
            {Language::Spanish, "Esqueleto"}
        };
        translations["esp.players.skeleton.desc"] = {
            {Language::English, "Draw skeleton lines between player bones"},
            {Language::French, "Dessiner des lignes de squelette entre les os"},
            {Language::Spanish, "Dibujar líneas de esqueleto entre huesos"}
        };

        translations["esp.players.head_circle"] = {
            {Language::English, "Head Circle"},
            {Language::French, "Cercle de Tete"},
            {Language::Spanish, "Circulo de Cabeza"}
        };
        translations["esp.players.head_circle.desc"] = {
            {Language::English, "Draw a circle around the head"},
            {Language::French, "Dessiner un cercle autour de la tête"},
            {Language::Spanish, "Dibujar un círculo alrededor de la cabeza"}
        };

        translations["esp.players.lines"] = {
            {Language::English, "Lines"},
            {Language::French, "Lignes"},
            {Language::Spanish, "Lineas"}
        };
        translations["esp.players.lines.desc"] = {
            {Language::English, "Draw lines from screen edge to players"},
            {Language::French, "Dessiner des lignes du bord de l'écran vers les joueurs"},
            {Language::Spanish, "Dibujar líneas desde el borde de la pantalla a los jugadores"}
        };

        translations["esp.players.nickname"] = {
            {Language::English, "Nickname"},
            {Language::French, "Pseudonyme"},
            {Language::Spanish, "Apodo"}
        };
        translations["esp.players.nickname.desc"] = {
            {Language::English, "Show the player nickname"},
            {Language::French, "Afficher le pseudonyme du joueur"},
            {Language::Spanish, "Mostrar el apodo del jugador"}
        };

        translations["esp.players.distance"] = {
            {Language::English, "Distance"},
            {Language::French, "Distance"},
            {Language::Spanish, "Distancia"}
        };
        translations["esp.players.distance.desc"] = {
            {Language::English, "Show distance to players"},
            {Language::French, "Afficher la distance aux joueurs"},
            {Language::Spanish, "Mostrar la distancia a los jugadores"}
        };

        translations["esp.players.platform"] = {
            {Language::English, "Platform"},
            {Language::French, "Plateforme"},
            {Language::Spanish, "Plataforma"}
        };

        translations["esp.players.kills"] = {
            {Language::English, "Kills"},
            {Language::French, "Éliminations"},
            {Language::Spanish, "Eliminaciones"}
        };

        translations["esp.players.level"] = {
            {Language::English, "Level"},
            {Language::French, "Niveau"},
            {Language::Spanish, "Nivel"}
        };

        translations["esp.players.rank"] = {
            {Language::English, "Rank"},
            {Language::French, "Rang"},
            {Language::Spanish, "Rango"}
        };

        translations["esp.players.weapon"] = {
            {Language::English, "Weapon"},
            {Language::French, "Arme"},
            {Language::Spanish, "Arma"}
        };

        // ========== ITEM ESP TRANSLATIONS ==========
        translations["esp.items.title"] = {
            {Language::English, "Item ESP"},
            {Language::French, "ESP Objets"},
            {Language::Spanish, "ESP Objetos"}
        };

        translations["esp.items.consumables"] = {
            {Language::English, "Consumables"},
            {Language::French, "Consommables"},
            {Language::Spanish, "Consumibles"}
        };
        translations["esp.items.consumables.desc"] = {
            {Language::English, "Show consumable items like heals and shields"},
            {Language::French, "Afficher les consommables comme soins et boucliers"},
            {Language::Spanish, "Mostrar consumibles como curas y escudos"}
        };

        translations["esp.items.weapons"] = {
            {Language::English, "Weapons"},
            {Language::French, "Armes"},
            {Language::Spanish, "Armas"}
        };
        translations["esp.items.weapons.desc"] = {
            {Language::English, "Show weapons on the ground"},
            {Language::French, "Afficher les armes au sol"},
            {Language::Spanish, "Mostrar armas en el suelo"}
        };

        translations["esp.items.ammo"] = {
            {Language::English, "Ammunition"},
            {Language::French, "Munitions"},
            {Language::Spanish, "Municion"}
        };
        translations["esp.items.ammo.desc"] = {
            {Language::English, "Show ammunition items"},
            {Language::French, "Afficher les munitions"},
            {Language::Spanish, "Mostrar munición"}
        };

        translations["esp.items.others"] = {
            {Language::English, "Others"},
            {Language::French, "Autres"},
            {Language::Spanish, "Otros"}
        };
        translations["esp.items.others.desc"] = {
            {Language::English, "Show other entities like chests, vehicles, llamas"},
            {Language::French, "Afficher autres entités comme coffres, véhicules, lamas"},
            {Language::Spanish, "Mostrar otros como cofres, vehículos, llamas"}
        };

        translations["esp.items.rarity_filter"] = {
            {Language::English, "Rarity Filter"},
            {Language::French, "Filtre de Rareté"},
            {Language::Spanish, "Filtro de Rareza"}
        };
        translations["esp.items.rarity_filter.desc"] = {
            {Language::English, "Filter items by rarity tiers"},
            {Language::French, "Filtrer les objets par rareté"},
            {Language::Spanish, "Filtrar objetos por rareza"}
        };

        translations["esp.items.show_name"] = {
            {Language::English, "Show Name"},
            {Language::French, "Afficher Nom"},
            {Language::Spanish, "Mostrar Nombre"}
        };
        translations["esp.items.show_name.desc"] = {
            {Language::English, "Display item names"},
            {Language::French, "Afficher les noms des objets"},
            {Language::Spanish, "Mostrar nombres de objetos"}
        };

        translations["esp.items.show_distance"] = {
            {Language::English, "Show Distance"},
            {Language::French, "Afficher Distance"},
            {Language::Spanish, "Mostrar Distancia"}
        };
        translations["esp.items.show_distance.desc"] = {
            {Language::English, "Display distance to the item"},
            {Language::French, "Afficher la distance à l'objet"},
            {Language::Spanish, "Mostrar distancia al objeto"}
        };

        translations["esp.items.show_icons"] = {
            {Language::English, "Show Icons"},
            {Language::French, "Afficher Icones"},
            {Language::Spanish, "Mostrar Iconos"}
        };
        translations["esp.items.show_icons.desc"] = {
            {Language::English, "Display item icons"},
            {Language::French, "Afficher les icônes d'objets"},
            {Language::Spanish, "Mostrar iconos de objetos"}
        };

        // ========== RADAR TRANSLATIONS ==========
        translations["radar.title"] = {
            {Language::English, "Radar"},
            {Language::French, "Radar"},
            {Language::Spanish, "Radar"}
        };

        translations["radar.enable"] = {
            {Language::English, "Enable Radar"},
            {Language::French, "Activer Radar"},
            {Language::Spanish, "Activar Radar"}
        };
        translations["radar.enable.desc"] = {
            {Language::English, "Show a radar view for nearby players"},
            {Language::French, "Afficher un radar des joueurs proches"},
            {Language::Spanish, "Mostrar un radar de jugadores cercanos"}
        };

        translations["radar.show_distance"] = {
            {Language::English, "Show Distance"},
            {Language::French, "Afficher Distance"},
            {Language::Spanish, "Mostrar Distancia"}
        };
        translations["radar.show_distance.desc"] = {
            {Language::English, "Display player distance on radar"},
            {Language::French, "Afficher la distance sur le radar"},
            {Language::Spanish, "Mostrar distancia en el radar"}
        };

        translations["radar.position"] = {
            {Language::English, "Position"},
            {Language::French, "Position"},
            {Language::Spanish, "Posicion"}
        };

        translations["radar.size"] = {
            {Language::English, "Size"},
            {Language::French, "Taille"},
            {Language::Spanish, "Tamano"}
        };

        translations["radar.type"] = {
            {Language::English, "Type"},
            {Language::French, "Type"},
            {Language::Spanish, "Tipo"}
        };
        translations["radar.type.desc"] = {
            {Language::English, "Choose radar shape"},
            {Language::French, "Choisir la forme du radar"},
            {Language::Spanish, "Elegir la forma del radar"}
        };

        translations["radar.max_distance"] = {
            {Language::English, "Max Distance"},
            {Language::French, "Distance Max"},
            {Language::Spanish, "Distancia Maxima"}
        };
        translations["radar.max_distance.desc"] = {
            {Language::English, "Maximum radar range"},
            {Language::French, "Portée maximale du radar"},
            {Language::Spanish, "Alcance máximo del radar"}
        };

        // ========== CONFIGURATION TRANSLATIONS ==========
        translations["config.title"] = {
            {Language::English, "Configuration"},
            {Language::French, "Configuration"},
            {Language::Spanish, "Configuración"}
        };

        translations["config.current"] = {
            {Language::English, "Current Config"},
            {Language::French, "Config Actuelle"},
            {Language::Spanish, "Config Actual"}
        };

        translations["config.game_mode"] = {
            {Language::English, "Game Mode"},
            {Language::French, "Mode de Jeu"},
            {Language::Spanish, "Modo de Juego"}
        };

        translations["config.language"] = {
            {Language::English, "Language"},
            {Language::French, "Langue"},
            {Language::Spanish, "Idioma"}
        };

        translations["config.auto_save"] = {
            {Language::English, "Auto Save"},
            {Language::French, "Sauvegarde Auto"},
            {Language::Spanish, "Guardado Automático"}
        };

        translations["config.load"] = {
            {Language::English, "Load"},
            {Language::French, "Charger"},
            {Language::Spanish, "Cargar"}
        };

        translations["config.save"] = {
            {Language::English, "Save"},
            {Language::French, "Sauvegarder"},
            {Language::Spanish, "Guardar"}
        };

        translations["config.reset"] = {
            {Language::English, "Reset to Defaults"},
            {Language::French, "Réinitialiser"},
            {Language::Spanish, "Restablecer"}
        };

        // ========== HOTKEYS TRANSLATIONS ==========
        translations["hotkeys.title"] = {
            {Language::English, "Hotkeys"},
            {Language::French, "Raccourcis"},
            {Language::Spanish, "Teclas Rapidas"}
        };

        translations["hotkeys.toggle"] = {
            {Language::English, "Toggle"},
            {Language::French, "Basculer"},
            {Language::Spanish, "Alternar"}
        };

        translations["hotkeys.hold"] = {
            {Language::English, "Hold"},
            {Language::French, "Maintenir"},
            {Language::Spanish, "Mantener"}
        };

        translations["hotkeys.panic_mode"] = {
            {Language::English, "Panic Mode"},
            {Language::French, "Mode Panique"},
            {Language::Spanish, "Modo Pánico"}
        };

        translations["hotkeys.menu"] = {
            {Language::English, "Menu"},
            {Language::French, "Menu"},
            {Language::Spanish, "Menú"}
        };

        // ========== VISUAL SETTINGS TRANSLATIONS ==========
        translations["visuals.colors"] = {
            {Language::English, "Colors"},
            {Language::French, "Couleurs"},
            {Language::Spanish, "Colores"}
        };

        translations["visuals.visible"] = {
            {Language::English, "Visible"},
            {Language::French, "Visible"},
            {Language::Spanish, "Visible"}
        };

        translations["visuals.non_visible"] = {
            {Language::English, "Non-Visible"},
            {Language::French, "Non-Visible"},
            {Language::Spanish, "No Visible"}
        };

        translations["visuals.knocked"] = {
            {Language::English, "Knocked"},
            {Language::French, "Abattu"},
            {Language::Spanish, "Derribado"}
        };

        translations["visuals.thickness"] = {
            {Language::English, "Thickness"},
            {Language::French, "Epaisseur"},
            {Language::Spanish, "Grosor"}
        };
        translations["visuals.thickness.desc"] = {
            {Language::English, "Control line thickness"},
            {Language::French, "Contrôler l'épaisseur des lignes"},
            {Language::Spanish, "Controlar el grosor de las líneas"}
        };

        translations["visuals.font_size"] = {
            {Language::English, "Font Size"},
            {Language::French, "Taille de Police"},
            {Language::Spanish, "Tamaño de Fuente"}
        };
        translations["visuals.font_size.desc"] = {
            {Language::English, "Adjust text size"},
            {Language::French, "Ajuster la taille du texte"},
            {Language::Spanish, "Ajustar el tamaño del texto"}
        };

        translations["visuals.icon_size"] = {
            {Language::English, "Icon Size"},
            {Language::French, "Taille Icône"},
            {Language::Spanish, "Tamaño de Icono"}
        };
        translations["visuals.icon_size.desc"] = {
            {Language::English, "Adjust icon size"},
            {Language::French, "Ajuster la taille des icônes"},
            {Language::Spanish, "Ajustar el tamaño de los iconos"}
        };

        // Additional general visuals controls
        translations["visuals.box_type"] = {
            {Language::English, "Box Type"},
            {Language::French, "Type de Boîte"},
            {Language::Spanish, "Tipo de Caja"}
        };
        translations["visuals.box_type.desc"] = {
            {Language::English, "Choose the bounding box style"},
            {Language::French, "Choisir le style de la boîte"},
            {Language::Spanish, "Elegir el estilo de la caja"}
        };
        translations["visuals.rounding"] = {
            {Language::English, "Rounding"},
            {Language::French, "Arrondi"},
            {Language::Spanish, "Redondeo"}
        };
        translations["visuals.rounding.desc"] = {
            {Language::English, "Adjust corner roundness"},
            {Language::French, "Ajuster l'arrondi des coins"},
            {Language::Spanish, "Ajustar el redondeo de las esquinas"}
        };
        translations["visuals.curved"] = {
            {Language::English, "Curved"},
            {Language::French, "Courbé"},
            {Language::Spanish, "Curvado"}
        };
        translations["visuals.curved.desc"] = {
            {Language::English, "Use curved lines instead of straight"},
            {Language::French, "Utiliser des lignes courbées au lieu de droites"},
            {Language::Spanish, "Usar líneas curvas en lugar de rectas"}
        };
        translations["visuals.bold"] = {
            {Language::English, "Bold"},
            {Language::French, "Gras"},
            {Language::Spanish, "Negrita"}
        };
        translations["visuals.bold.desc"] = {
            {Language::English, "Render text in bold style"},
            {Language::French, "Afficher le texte en gras"},
            {Language::Spanish, "Mostrar el texto en negrita"}
        };
        translations["visuals.italic"] = {
            {Language::English, "Italic"},
            {Language::French, "Italique"},
            {Language::Spanish, "Cursiva"}
        };
        translations["visuals.italic.desc"] = {
            {Language::English, "Render text in italic style"},
            {Language::French, "Afficher le texte en italique"},
            {Language::Spanish, "Mostrar el texto en cursiva"}
        };
        translations["visuals.outline"] = {
            {Language::English, "Outline"},
            {Language::French, "Contour"},
            {Language::Spanish, "Contorno"}
        };
        translations["visuals.outline.desc"] = {
            {Language::English, "Draw an outline around text"},
            {Language::French, "Dessiner un contour autour du texte"},
            {Language::Spanish, "Dibujar un contorno alrededor del texto"}
        };
        translations["visuals.outline_thickness"] = {
            {Language::English, "Outline Thickness"},
            {Language::French, "Épaisseur du Contour"},
            {Language::Spanish, "Grosor del Contorno"}
        };
        translations["visuals.outline_thickness.desc"] = {
            {Language::English, "Adjust the outline thickness"},
            {Language::French, "Ajuster l'épaisseur du contour"},
            {Language::Spanish, "Ajustar el grosor del contorno"}
        };
        translations["visuals.line_position"] = {
            {Language::English, "Line Position"},
            {Language::French, "Position de Ligne"},
            {Language::Spanish, "Posición de Línea"}
        };
        translations["visuals.line_position.desc"] = {
            {Language::English, "Where to start snaplines on screen"},
            {Language::French, "Où commencer les lignes sur l'écran"},
            {Language::Spanish, "Dónde empezar las líneas en la pantalla"}
        };
        translations["visuals.filled"] = {
            {Language::English, "Filled"},
            {Language::French, "Rempli"},
            {Language::Spanish, "Relleno"}
        };
        translations["visuals.filled.desc"] = {
            {Language::English, "Fill the shape instead of only drawing borders"},
            {Language::French, "Remplir la forme au lieu de dessiner uniquement les bords"},
            {Language::Spanish, "Rellenar la forma en lugar de solo dibujar bordes"}
        };

        // Aimbot UI additions
        translations["aimbot.crosshair_type"] = {
            {Language::English, "Crosshair Type"},
            {Language::French, "Type de Réticule"},
            {Language::Spanish, "Tipo de Mira"}
        };
        translations["aimbot.crosshair_type.desc"] = {
            {Language::English, "Select the crosshair shape"},
            {Language::French, "Sélectionner la forme du réticule"},
            {Language::Spanish, "Seleccionar la forma de la mira"}
        };
        translations["aimbot.crosshair_size"] = {
            {Language::English, "Crosshair Size"},
            {Language::French, "Taille du Réticule"},
            {Language::Spanish, "Tamaño de la Mira"}
        };
        translations["aimbot.crosshair_size.desc"] = {
            {Language::English, "Adjust the crosshair size"},
            {Language::French, "Ajuster la taille du réticule"},
            {Language::Spanish, "Ajustar el tamaño de la mira"}
        };
        translations["aimbot.target_type"] = {
            {Language::English, "Target Indicator"},
            {Language::French, "Indicateur de Cible"},
            {Language::Spanish, "Indicador de Objetivo"}
        };
        translations["aimbot.target_type.desc"] = {
            {Language::English, "Select the target indicator shape"},
            {Language::French, "Sélectionner la forme de l'indicateur"},
            {Language::Spanish, "Seleccionar la forma del indicador"}
        };

        // Radar additions
        translations["radar.position_x"] = {
            {Language::English, "Position X"},
            {Language::French, "Position X"},
            {Language::Spanish, "Posición X"}
        };
        translations["radar.position_x.desc"] = {
            {Language::English, "Horizontal offset for radar"},
            {Language::French, "Décalage horizontal du radar"},
            {Language::Spanish, "Desplazamiento horizontal del radar"}
        };
        translations["radar.position_y"] = {
            {Language::English, "Position Y"},
            {Language::French, "Position Y"},
            {Language::Spanish, "Posición Y"}
        };
        translations["radar.position_y.desc"] = {
            {Language::English, "Vertical offset for radar"},
            {Language::French, "Décalage vertical du radar"},
            {Language::Spanish, "Desplazamiento vertical del radar"}
        };
        translations["radar.circle_size"] = {
            {Language::English, "Circle Size"},
            {Language::French, "Taille du Cercle"},
            {Language::Spanish, "Tamaño del Círculo"}
        };
        translations["radar.circle_size.desc"] = {
            {Language::English, "Adjust the circle radar size"},
            {Language::French, "Ajuster la taille du radar circulaire"},
            {Language::Spanish, "Ajustar el tamaño del radar circular"}
        };
        translations["radar.rectangle_size"] = {
            {Language::English, "Rectangle Size"},
            {Language::French, "Taille du Rectangle"},
            {Language::Spanish, "Tamaño del Rectángulo"}
        };
        translations["radar.rectangle_size.desc"] = {
            {Language::English, "Adjust the rectangle radar size"},
            {Language::French, "Ajuster la taille du radar rectangulaire"},
            {Language::Spanish, "Ajustar el tamaño del radar rectangular"}
        };
        translations["radar.highlight_visible"] = {
            {Language::English, "Highlight Visible Players"},
            {Language::French, "Surligner Joueurs Visibles"},
            {Language::Spanish, "Resaltar Jugadores Visibles"}
        };
        translations["radar.highlight_visible.desc"] = {
            {Language::English, "Use a special color for visible players"},
            {Language::French, "Utiliser une couleur spéciale pour les joueurs visibles"},
            {Language::Spanish, "Usar un color especial para jugadores visibles"}
        };
        translations["radar.highlight_closest"] = {
            {Language::English, "Highlight Closest Player"},
            {Language::French, "Surligner le Joueur le Plus Proche"},
            {Language::Spanish, "Resaltar Jugador Más Cercano"}
        };
        translations["radar.highlight_closest.desc"] = {
            {Language::English, "Use a special color for the closest player"},
            {Language::French, "Utiliser une couleur spéciale pour le joueur le plus proche"},
            {Language::Spanish, "Usar un color especial para el jugador más cercano"}
        };
        translations["radar.highlight_aiming_at_you"] = {
            {Language::English, "Highlight Players Aiming At You"},
            {Language::French, "Surligner les Joueurs qui Visent"},
            {Language::Spanish, "Resaltar Jugadores Apuntándote"}
        };
        translations["radar.highlight_aiming_at_you.desc"] = {
            {Language::English, "Use a special color for threats aiming at you"},
            {Language::French, "Utiliser une couleur spéciale pour les menaces qui vous visent"},
            {Language::Spanish, "Usar un color especial para amenazas que te apuntan"}
        };

        // UI / Config / Fonts / Hotkeys
        translations["ui.language"] = {
            {Language::English, "Language"},
            {Language::French, "Langue"},
            {Language::Spanish, "Idioma"}
        };
        translations["ui.notifications.enable"] = {
            {Language::English, "Enable Notifications"},
            {Language::French, "Activer les Notifications"},
            {Language::Spanish, "Activar Notificaciones"}
        };
        translations["ui.notifications.enable.desc"] = {
            {Language::English, "Show notifications for settings changes"},
            {Language::French, "Afficher des notifications lors des changements"},
            {Language::Spanish, "Mostrar notificaciones de cambios"}
        };
        translations["ui.notification_duration"] = {
            {Language::English, "Notification Duration"},
            {Language::French, "Durée des Notifications"},
            {Language::Spanish, "Duración de Notificaciones"}
        };
        translations["ui.notification_duration.desc"] = {
            {Language::English, "How long notifications appear"},
            {Language::French, "Durée d'affichage des notifications"},
            {Language::Spanish, "Cuánto tiempo aparecen las notificaciones"}
        };
        translations["ui.show_feature_descriptions"] = {
            {Language::English, "Show Feature Descriptions"},
            {Language::French, "Afficher les Descriptions"},
            {Language::Spanish, "Mostrar Descripciones"}
        };
        translations["ui.search"] = {
            {Language::English, "Search"},
            {Language::French, "Rechercher"},
            {Language::Spanish, "Buscar"}
        };
        translations["ui.delete"] = {
            {Language::English, "Delete"},
            {Language::French, "Supprimer"},
            {Language::Spanish, "Eliminar"}
        };
        translations["ui.cancel"] = {
            {Language::English, "Cancel"},
            {Language::French, "Annuler"},
            {Language::Spanish, "Cancelar"}
        };
        translations["configs.new"] = {
            {Language::English, "New Config"},
            {Language::French, "Nouvelle Config"},
            {Language::Spanish, "Nueva Config"}
        };
        translations["fonts.default"] = {
            {Language::English, "Default Font"},
            {Language::French, "Police par Défaut"},
            {Language::Spanish, "Fuente Predeterminada"}
        };
        translations["fonts.player_esp"] = {
            {Language::English, "Player ESP Font"},
            {Language::French, "Police ESP Joueurs"},
            {Language::Spanish, "Fuente ESP Jugadores"}
        };
        translations["fonts.item_esp"] = {
            {Language::English, "Item ESP Font"},
            {Language::French, "Police ESP Objets"},
            {Language::Spanish, "Fuente ESP Objetos"}
        };
        translations["fonts.radar"] = {
            {Language::English, "Radar Font"},
            {Language::French, "Police Radar"},
            {Language::Spanish, "Fuente Radar"}
        };
        translations["fonts.aimbot"] = {
            {Language::English, "Aimbot Font"},
            {Language::French, "Police Aimbot"},
            {Language::Spanish, "Fuente Aimbot"}
        };
        translations["fonts.ui"] = {
            {Language::English, "UI Font"},
            {Language::French, "Police UI"},
            {Language::Spanish, "Fuente UI"}
        };
        translations["game.mode"] = {
            {Language::English, "Game Mode"},
            {Language::French, "Mode de Jeu"},
            {Language::Spanish, "Modo de Juego"}
        };
        translations["hotkeys.draw_fov"] = {
            {Language::English, "Draw FOV"},
            {Language::French, "Afficher FOV"},
            {Language::Spanish, "Mostrar FOV"}
        };
        translations["hotkeys.draw_crosshair"] = {
            {Language::English, "Crosshair"},
            {Language::French, "Réticule"},
            {Language::Spanish, "Mira"}
        };
        translations["hotkeys.draw_target"] = {
            {Language::English, "Target Line"},
            {Language::French, "Ligne de Cible"},
            {Language::Spanish, "Línea de Objetivo"}
        };
        translations["hotkeys.aimbot_toggle"] = {
            {Language::English, "Aimbot Toggle"},
            {Language::French, "Basculer Aimbot"},
            {Language::Spanish, "Alternar Aimbot"}
        };
        translations["hotkeys.aim_lock"] = {
            {Language::English, "Aim Lock"},
            {Language::French, "Verrouillage"},
            {Language::Spanish, "Bloqueo"}
        };

        // Item specific entries used in GUI
        translations["items.consumables.bandages"] = {
            {Language::English, "Bandages"},
            {Language::French, "Bandages"},
            {Language::Spanish, "Vendajes"}
        };
        translations["items.consumables.bandages.desc"] = {
            {Language::English, "Show bandages"},
            {Language::French, "Afficher les bandages"},
            {Language::Spanish, "Mostrar vendajes"}
        };
        translations["items.consumables.medkit"] = {
            {Language::English, "Medkit"},
            {Language::French, "Kit Médical"},
            {Language::Spanish, "Botiquín"}
        };
        translations["items.consumables.medkit.desc"] = {
            {Language::English, "Show medkits"},
            {Language::French, "Afficher les kits médicaux"},
            {Language::Spanish, "Mostrar botiquines"}
        };
        translations["items.consumables.small_shield"] = {
            {Language::English, "Small Shield"},
            {Language::French, "Petit Bouclier"},
            {Language::Spanish, "Escudo Pequeño"}
        };
        translations["items.consumables.small_shield.desc"] = {
            {Language::English, "Show small shields"},
            {Language::French, "Afficher les petits boucliers"},
            {Language::Spanish, "Mostrar escudos pequeños"}
        };
        translations["items.consumables.big_shield"] = {
            {Language::English, "Big Shield"},
            {Language::French, "Gros Bouclier"},
            {Language::Spanish, "Escudo Grande"}
        };
        translations["items.consumables.big_shield.desc"] = {
            {Language::English, "Show big shields"},
            {Language::French, "Afficher les gros boucliers"},
            {Language::Spanish, "Mostrar escudos grandes"}
        };
        translations["items.consumables.chug_splash"] = {
            {Language::English, "Chug Splash"},
            {Language::French, "Chug Splash"},
            {Language::Spanish, "Chug Splash"}
        };
        translations["items.consumables.chug_splash.desc"] = {
            {Language::English, "Show chug splashes"},
            {Language::French, "Afficher les chug splashes"},
            {Language::Spanish, "Mostrar chug splashes"}
        };
        translations["items.consumables.nitro_splash"] = {
            {Language::English, "Nitro Splash"},
            {Language::French, "Nitro Splash"},
            {Language::Spanish, "Nitro Splash"}
        };
        translations["items.consumables.nitro_splash.desc"] = {
            {Language::English, "Show nitro splashes"},
            {Language::French, "Afficher les nitro splashes"},
            {Language::Spanish, "Mostrar nitro splashes"}
        };
        translations["items.consumables.flowberry_fizz"] = {
            {Language::English, "FlowBerry Fizz"},
            {Language::French, "FlowBerry Fizz"},
            {Language::Spanish, "FlowBerry Fizz"}
        };
        translations["items.consumables.flowberry_fizz.desc"] = {
            {Language::English, "Show FlowBerry Fizz"},
            {Language::French, "Afficher FlowBerry Fizz"},
            {Language::Spanish, "Mostrar FlowBerry Fizz"}
        };
        translations["items.consumables.nuka_cola"] = {
            {Language::English, "Nuka-Cola"},
            {Language::French, "Nuka-Cola"},
            {Language::Spanish, "Nuka-Cola"}
        };
        translations["items.consumables.nuka_cola.desc"] = {
            {Language::English, "Show Nuka-Cola"},
            {Language::French, "Afficher Nuka-Cola"},
            {Language::Spanish, "Mostrar Nuka-Cola"}
        };

        // Ammo item names
        translations["items.ammo.light"] = {
            {Language::English, "Light Ammo"},
            {Language::French, "Munitions Légères"},
            {Language::Spanish, "Munición Ligera"}
        };
        translations["items.ammo.light.desc"] = {
            {Language::English, "Show light ammo"},
            {Language::French, "Afficher munitions légères"},
            {Language::Spanish, "Mostrar munición ligera"}
        };
        translations["items.ammo.medium"] = {
            {Language::English, "Medium Ammo"},
            {Language::French, "Munitions Moyennes"},
            {Language::Spanish, "Munición Media"}
        };
        translations["items.ammo.medium.desc"] = {
            {Language::English, "Show medium ammo"},
            {Language::French, "Afficher munitions moyennes"},
            {Language::Spanish, "Mostrar munición media"}
        };
        translations["items.ammo.heavy"] = {
            {Language::English, "Heavy Ammo"},
            {Language::French, "Munitions Lourdes"},
            {Language::Spanish, "Munición Pesada"}
        };
        translations["items.ammo.heavy.desc"] = {
            {Language::English, "Show heavy ammo"},
            {Language::French, "Afficher munitions lourdes"},
            {Language::Spanish, "Mostrar munición pesada"}
        };
        translations["items.ammo.shells"] = {
            {Language::English, "Shells"},
            {Language::French, "Cartouches"},
            {Language::Spanish, "Cartuchos"}
        };
        translations["items.ammo.shells.desc"] = {
            {Language::English, "Show shells"},
            {Language::French, "Afficher cartouches"},
            {Language::Spanish, "Mostrar cartuchos"}
        };
        translations["items.ammo.rockets"] = {
            {Language::English, "Rockets"},
            {Language::French, "Roquettes"},
            {Language::Spanish, "Cohetes"}
        };
        translations["items.ammo.rockets.desc"] = {
            {Language::English, "Show rockets"},
            {Language::French, "Afficher roquettes"},
            {Language::Spanish, "Mostrar cohetes"}
        };

        // Other objects
        translations["items.others.chests"] = {
            {Language::English, "Chests"},
            {Language::French, "Coffres"},
            {Language::Spanish, "Cofres"}
        };
        translations["items.others.chests.desc"] = {
            {Language::English, "Show chests"},
            {Language::French, "Afficher coffres"},
            {Language::Spanish, "Mostrar cofres"}
        };
        translations["items.others.vehicles"] = {
            {Language::English, "Vehicles"},
            {Language::French, "Véhicules"},
            {Language::Spanish, "Vehículos"}
        };
        translations["items.others.vehicles.desc"] = {
            {Language::English, "Show vehicles"},
            {Language::French, "Afficher véhicules"},
            {Language::Spanish, "Mostrar vehículos"}
        };
        translations["items.others.llamas"] = {
            {Language::English, "Llamas"},
            {Language::French, "Lamas"},
            {Language::Spanish, "Llamas"}
        };
        translations["items.others.llamas.desc"] = {
            {Language::English, "Show llamas"},
            {Language::French, "Afficher lamas"},
            {Language::Spanish, "Mostrar llamas"}
        };
        translations["items.others.supply_drops"] = {
            {Language::English, "Supply Drops"},
            {Language::French, "Caisses de Ravitaillement"},
            {Language::Spanish, "Suministros"}
        };
        translations["items.others.supply_drops.desc"] = {
            {Language::English, "Show supply drops"},
            {Language::French, "Afficher caisses de ravitaillement"},
            {Language::Spanish, "Mostrar suministros"}
        };

        // Weapon names (subset used)
        translations["items.weapons.new_pump_shotgun"] = {
            {Language::English, "New Pump Shotgun"},
            {Language::French, "Fusil à Pompe (Nouveau)"},
            {Language::Spanish, "Escopeta de Bomba Nueva"}
        };
        translations["items.weapons.og_pump_shotgun"] = {
            {Language::English, "OG Pump Shotgun"},
            {Language::French, "Fusil à Pompe OG"},
            {Language::Spanish, "Escopeta de Bomba OG"}
        };
        translations["items.weapons.submachine_gun"] = {
            {Language::English, "Submachine Gun"},
            {Language::French, "Pistolet Mitrailleur"},
            {Language::Spanish, "Subfusil"}
        };
        translations["items.weapons.dual_pistols"] = {
            {Language::English, "Dual Pistols"},
            {Language::French, "Pistolets Doubles"},
            {Language::Spanish, "Pistolas Dobles"}
        };
        translations["items.weapons.rapid_fire_smg"] = {
            {Language::English, "Rapid Fire SMG"},
            {Language::French, "PM Tir Rapide"},
            {Language::Spanish, "SMG de Fuego Rápido"}
        };
        translations["items.weapons.suppressed_smg"] = {
            {Language::English, "Suppressed SMG"},
            {Language::French, "PM Silencieux"},
            {Language::Spanish, "SMG Silenciado"}
        };
        translations["items.weapons.suppressed_assault_rifle"] = {
            {Language::English, "Suppressed Assault Rifle"},
            {Language::French, "Fusil d'Assaut Silencieux"},
            {Language::Spanish, "Rifle de Asalto Silenciado"}
        };
        translations["items.weapons.heavy_sniper_rifle"] = {
            {Language::English, "Heavy Sniper Rifle"},
            {Language::French, "Fusil de Précision Lourd"},
            {Language::Spanish, "Rifle de Francotirador Pesado"}
        };
        translations["items.weapons.semi_auto_sniper_rifle"] = {
            {Language::English, "Semi-Auto Sniper Rifle"},
            {Language::French, "Fusil de Précision Semi-Automatique"},
            {Language::Spanish, "Rifle de Francotirador Semiautomático"}
        };
        translations["items.weapons.grenade_launcher"] = {
            {Language::English, "Grenade Launcher"},
            {Language::French, "Lance-Grenades"},
            {Language::Spanish, "Lanzagranadas"}
        };
        translations["items.weapons.remote_explosives"] = {
            {Language::English, "Remote Explosives"},
            {Language::French, "Explosifs à Distance"},
            {Language::Spanish, "Explosivos Remotos"}
        };
        translations["items.weapons.regular_grenades"] = {
            {Language::English, "Regular Grenades"},
            {Language::French, "Grenades"},
            {Language::Spanish, "Granadas"}
        };
        translations["items.weapons.ranger_pistol"] = {
            {Language::English, "Ranger Pistol"},
            {Language::French, "Pistolet Ranger"},
            {Language::Spanish, "Pistola Ranger"}
        };
        translations["items.weapons.harbinger_smg"] = {
            {Language::English, "Harbinger SMG"},
            {Language::French, "PM Harbinger"},
            {Language::Spanish, "SMG Harbinger"}
        };
        translations["items.weapons.thunder_burst_smg"] = {
            {Language::English, "Thunder Burst SMG"},
            {Language::French, "PM Rafale Tonnerre"},
            {Language::Spanish, "SMG Ráfaga Trueno"}
        };
        translations["items.weapons.warforged_assault_rifle"] = {
            {Language::English, "Warforged Assault Rifle"},
            {Language::French, "Fusil d'Assaut Forgé"},
            {Language::Spanish, "Rifle de Asalto Forjado"}
        };
        translations["items.weapons.tactical_assault_rifle"] = {
            {Language::English, "Tactical Assault Rifle"},
            {Language::French, "Fusil d'Assaut Tactique"},
            {Language::Spanish, "Rifle de Asalto Táctico"}
        };
        translations["items.weapons.combat_assault_rifle"] = {
            {Language::English, "Combat Assault Rifle"},
            {Language::French, "Fusil d'Assaut de Combat"},
            {Language::Spanish, "Rifle de Asalto de Combate"}
        };
        translations["items.weapons.combat_shotgun"] = {
            {Language::English, "Combat Shotgun"},
            {Language::French, "Fusil à Pompe de Combat"},
            {Language::Spanish, "Escopeta de Combate"}
        };
        translations["items.weapons.gatekeeper_shotgun"] = {
            {Language::English, "Gatekeeper Shotgun"},
            {Language::French, "Fusil à Pompe Gatekeeper"},
            {Language::Spanish, "Escopeta Gatekeeper"}
        };
        translations["items.weapons.hammer_pump_shotgun"] = {
            {Language::English, "Hammer Pump Shotgun"},
            {Language::French, "Fusil à Pompe Marteau"},
            {Language::Spanish, "Escopeta de Bomba Martillo"}
        };
        translations["items.weapons.huntress_dmr"] = {
            {Language::English, "Huntress DMR"},
            {Language::French, "DMR Chasseresse"},
            {Language::Spanish, "DMR Cazadora"}
        };
        translations["items.weapons.heavy_impact_sniper_rifle"] = {
            {Language::English, "Heavy Impact Sniper"},
            {Language::French, "Sniper à Impact Lourd"},
            {Language::Spanish, "Francotirador de Impacto Pesado"}
        };

        // ========== RARITY TRANSLATIONS ==========
        translations["rarity.common"] = {
            {Language::English, "Common"},
            {Language::French, "Commun"},
            {Language::Spanish, "Comun"}
        };

        translations["rarity.uncommon"] = {
            {Language::English, "Uncommon"},
            {Language::French, "Peu Commun"},
            {Language::Spanish, "Poco Comun"}
        };

        translations["rarity.rare"] = {
            {Language::English, "Rare"},
            {Language::French, "Rare"},
            {Language::Spanish, "Raro"}
        };

        translations["rarity.epic"] = {
            {Language::English, "Epic"},
            {Language::French, "Epique"},
            {Language::Spanish, "Epico"}
        };

        translations["rarity.legendary"] = {
            {Language::English, "Legendary"},
            {Language::French, "Legendaire"},
            {Language::Spanish, "Legendario"}
        };

        translations["rarity.mythic"] = {
            {Language::English, "Mythic"},
            {Language::French, "Mythique"},
            {Language::Spanish, "Mitico"}
        };

        // ========== GAME MODE TRANSLATIONS ==========
        translations["gamemode.battle_royale"] = {
            {Language::English, "Battle Royale"},
            {Language::French, "Battle Royale"},
            {Language::Spanish, "Battle Royale"}
        };

        translations["gamemode.zero_build"] = {
            {Language::English, "Zero Build"},
            {Language::French, "Zero Build"},
            {Language::Spanish, "Zero Build"}
        };

        translations["gamemode.arena"] = {
            {Language::English, "Arena"},
            {Language::French, "Arene"},
            {Language::Spanish, "Arena"}
        };

        translations["gamemode.creative"] = {
            {Language::English, "Creative"},
            {Language::French, "Creatif"},
            {Language::Spanish, "Creativo"}
        };

        // ========== HITBOX TRANSLATIONS ==========
        translations["hitbox.head"] = {
            {Language::English, "Head"},
            {Language::French, "Tete"},
            {Language::Spanish, "Cabeza"}
        };

        translations["hitbox.neck"] = {
            {Language::English, "Neck"},
            {Language::French, "Cou"},
            {Language::Spanish, "Cuello"}
        };

        translations["hitbox.chest"] = {
            {Language::English, "Chest"},
            {Language::French, "Poitrine"},
            {Language::Spanish, "Pecho"}
        };

        translations["hitbox.body"] = {
            {Language::English, "Body"},
            {Language::French, "Corps"},
            {Language::Spanish, "Cuerpo"}
        };

        // ========== STATUS TRANSLATIONS ==========
        translations["status.enabled"] = {
            {Language::English, "Enabled"},
            {Language::French, "Activ�"},
            {Language::Spanish, "Activado"}
        };

        translations["status.disabled"] = {
            {Language::English, "Disabled"},
            {Language::French, "Desactive"},
            {Language::Spanish, "Desactivado"}
        };

        translations["status.loading"] = {
            {Language::English, "Loading..."},
            {Language::French, "Chargement..."},
            {Language::Spanish, "Cargando..."}
        };

        translations["status.saving"] = {
            {Language::English, "Saving..."},
            {Language::French, "Sauvegarde..."},
            {Language::Spanish, "Guardando..."}
        };

        // ========== UNITS TRANSLATIONS ==========
        translations["units.meters"] = {
            {Language::English, "m"},
            {Language::French, "m"},
            {Language::Spanish, "m"}
        };

        translations["units.pixels"] = {
            {Language::English, "px"},
            {Language::French, "px"},
            {Language::Spanish, "px"}
        };

        translations["units.degrees"] = {
            {Language::English, "deg"},
            {Language::French, "deg"},
            {Language::Spanish, "deg"}
        };

        translations["units.milliseconds"] = {
            {Language::English, "ms"},
            {Language::French, "ms"},
            {Language::Spanish, "ms"}
        };

        // ========== BOX TYPE TRANSLATIONS ==========
        translations["box_type.corner"] = {
            {Language::English, "Corner"},
            {Language::French, "Coin"},
            {Language::Spanish, "Esquina"}
        };

        translations["box_type.full"] = {
            {Language::English, "Full"},
            {Language::French, "Complet"},
            {Language::Spanish, "Completo"}
        };

        translations["box_type.rounded"] = {
            {Language::English, "Rounded"},
            {Language::French, "Arrondi"},
            {Language::Spanish, "Redondeado"}
        };

        // ========== LINE POSITION TRANSLATIONS ==========
        translations["line_position.top"] = {
            {Language::English, "Top"},
            {Language::French, "Haut"},
            {Language::Spanish, "Arriba"}
        };

        translations["line_position.center"] = {
            {Language::English, "Center"},
            {Language::French, "Centre"},
            {Language::Spanish, "Centro"}
        };

        translations["line_position.bottom"] = {
            {Language::English, "Bottom"},
            {Language::French, "Bas"},
            {Language::Spanish, "Abajo"}
        };

        // ========== CROSSHAIR TYPE TRANSLATIONS ==========
        translations["crosshair_type.cross"] = {
            {Language::English, "Cross"},
            {Language::French, "Croix"},
            {Language::Spanish, "Cruz"}
        };

        translations["crosshair_type.circle"] = {
            {Language::English, "Circle"},
            {Language::French, "Cercle"},
            {Language::Spanish, "Circulo"}
        };

        translations["crosshair_type.dot"] = {
            {Language::English, "Dot"},
            {Language::French, "Point"},
            {Language::Spanish, "Punto"}
        };

        // ========== TARGET INDICATOR TRANSLATIONS ==========
        translations["target_type.circle"] = {
            {Language::English, "Circle"},
            {Language::French, "Cercle"},
            {Language::Spanish, "Circulo"}
        };

        translations["target_type.square"] = {
            {Language::English, "Square"},
            {Language::French, "Carre"},
            {Language::Spanish, "Cuadrado"}
        };

        translations["target_type.triangle"] = {
            {Language::English, "Triangle"},
            {Language::French, "Triangle"},
            {Language::Spanish, "Triangulo"}
        };

        // ========== RADAR TYPE TRANSLATIONS ==========
        translations["radar_type.circle"] = {
            {Language::English, "Circle"},
            {Language::French, "Cercle"},
            {Language::Spanish, "C�rculo"}
        };

        translations["radar_type.rectangle"] = {
            {Language::English, "Rectangle"},
            {Language::French, "Rectangle"},
            {Language::Spanish, "Rectangulo"}
        };

        // ========== FONT FAMILY TRANSLATIONS ==========
        translations["font.arial"] = {
            {Language::English, "Arial"},
            {Language::French, "Arial"},
            {Language::Spanish, "Arial"}
        };

        translations["font.calibri"] = {
            {Language::English, "Calibri"},
            {Language::French, "Calibri"},
            {Language::Spanish, "Calibri"}
        };

        translations["font.verdana"] = {
            {Language::English, "Verdana"},
            {Language::French, "Verdana"},
            {Language::Spanish, "Verdana"}
        };

        translations["font.times_new_roman"] = {
            {Language::English, "Times New Roman"},
            {Language::French, "Times New Roman"},
            {Language::Spanish, "Times New Roman"}
        };

        // ========== GENERAL UI TRANSLATIONS ==========
        translations["ui.apply"] = {
            {Language::English, "Apply"},
            {Language::French, "Appliquer"},
            {Language::Spanish, "Aplicar"}
        };

        translations["ui.cancel"] = {
            {Language::English, "Cancel"},
            {Language::French, "Annuler"},
            {Language::Spanish, "Cancelar"}
        };

        translations["ui.ok"] = {
            {Language::English, "OK"},
            {Language::French, "OK"},
            {Language::Spanish, "OK"}
        };

        translations["ui.yes"] = {
            {Language::English, "Yes"},
            {Language::French, "Oui"},
            {Language::Spanish, "S�"}
        };

        translations["ui.no"] = {
            {Language::English, "No"},
            {Language::French, "Non"},
            {Language::Spanish, "No"}
        };

        translations["ui.browse"] = {
            {Language::English, "Browse..."},
            {Language::French, "Parcourir..."},
            {Language::Spanish, "Examinar..."}
        };

        translations["ui.default"] = {
            {Language::English, "Default"},
            {Language::French, "Par Defaut"},
            {Language::Spanish, "Predeterminado"}
        };

        translations["ui.custom"] = {
            {Language::English, "Custom"},
            {Language::French, "Personnalis�"},
            {Language::Spanish, "Personalizado"}
        };

        // ========== ERROR/WARNING TRANSLATIONS ==========
        translations["error.config_load_failed"] = {
            {Language::English, "Failed to load configuration"},
            {Language::French, "Echec du chargement de la configuration"},
            {Language::Spanish, "Error al cargar la configuracion"}
        };

        translations["error.config_save_failed"] = {
            {Language::English, "Failed to save configuration"},
            {Language::French, "Echec de la sauvegarde de la configuration"},
            {Language::Spanish, "Error al guardar la configuracion"}
        };

        translations["warning.restart_required"] = {
            {Language::English, "Restart required for changes to take effect"},
            {Language::French, "Redemarrage requis pour appliquer les changements"},
            {Language::Spanish, "Reinicio requerido para aplicar cambios"}
        };

        translations["warning.unsaved_changes"] = {
            {Language::English, "You have unsaved changes"},
            {Language::French, "Vous avez des modifications non sauvegardees"},
            {Language::Spanish, "Tienes cambios sin guardar"}
        };

        // ========== PERFORMANCE TRANSLATIONS ==========
        translations["performance.fps"] = {
            {Language::English, "FPS"},
            {Language::French, "IPS"},
            {Language::Spanish, "FPS"}
        };

        translations["performance.memory_usage"] = {
            {Language::English, "Memory Usage"},
            {Language::French, "Utilisation M�moire"},
            {Language::Spanish, "Uso de Memoria"}
        };

        translations["performance.optimization"] = {
            {Language::English, "Performance Optimization"},
            {Language::French, "Optimisation Performance"},
            {Language::Spanish, "Optimizaci�n Rendimiento"}
        };

        translations["ui.font_settings"] = {
            {Language::English, "Font Settings"},
            {Language::French, "Paramètres de Police"},
            {Language::Spanish, "Ajustes de Fuente"}
        };
        translations["ui.animation_settings"] = {
            {Language::English, "Animation Settings"},
            {Language::French, "Paramètres d'Animation"},
            {Language::Spanish, "Ajustes de Animación"}
        };
        translations["ui.animation_speed"] = {
            {Language::English, "Animation Speed"},
            {Language::French, "Vitesse d'Animation"},
            {Language::Spanish, "Velocidad de Animación"}
        };
        translations["ui.animation_speed.desc"] = {
            {Language::English, "Adjust animation speed"},
            {Language::French, "Ajuster la vitesse de l'animation"},
            {Language::Spanish, "Ajustar la velocidad de la animación"}
        };
        translations["ui.about"] = {
            {Language::English, "About"},
            {Language::French, "À propos"},
            {Language::Spanish, "Acerca de"}
        };
    }
};

// =============================================================================
// UTILITY FUNCTIONS AND HELPERS
// =============================================================================

class SettingsValidator {
public:
    static bool ValidateRange(float value, float min, float max) {
        return value >= min && value <= max;
    }

    static bool ValidateRange(int value, int min, int max) {
        return value >= min && value <= max;
    }

    static void ClampValue(float& value, float min, float max) {
        if (value < min) value = min;
        if (value > max) value = max;
    }

    static void ClampValue(int& value, int min, int max) {
        if (value < min) value = min;
        if (value > max) value = max;
    }
};

class SettingsManager {
public:
    // Per-weapon settings helpers
    template<typename T>
    static T& GetWeaponSetting(std::unordered_map<int, T>& weaponMap, int weaponId, const T& defaultSettings) {
        auto it = weaponMap.find(weaponId);
        if (it != weaponMap.end()) {
            return it->second;
        }
        // Create new entry with default settings
        weaponMap[weaponId] = defaultSettings;
        return weaponMap[weaponId];
    }

};

// =============================================================================
// MAIN SETTINGS CONTAINER (Updated)
// =============================================================================

class GameSettings {
public:
    AimbotSettings aimbot;
    TriggerbotSettings triggerbot;
    PlayerESPSettings playerESP;
    ItemESPSettings itemESP;
    RadarSettings radar;
    HotkeySettings hotkeys;
    ConfigurationSettings config;

    // Singleton pattern
    static GameSettings& Instance() {
        static GameSettings instance;
        return instance;
    }

    void ResetToDefaults();

    // Language management
    void SetLanguage(Language lang) {
        config.language = lang;
    }

    Language GetLanguage() const {
        return config.language;
    }

    std::string GetLocalizedText(const std::string& key) const {
        return LocalizationManager::GetText(key, config.language);
    }

private:
    GameSettings() {
        InitializeDefaults();
        LocalizationManager::InitializeTranslations();
    }

    void InitializeDefaults() {
        itemESP.InitializeDefaults();
    }
};

// =============================================================================
// CONVENIENT ACCESS MACROS
// =============================================================================

#define GAME_SETTINGS GameSettings::Instance()
#define AIMBOT_SETTINGS GAME_SETTINGS.aimbot
#define TRIGGERBOT_SETTINGS GAME_SETTINGS.triggerbot
#define PLAYER_ESP_SETTINGS GAME_SETTINGS.playerESP
#define ITEM_ESP_SETTINGS GAME_SETTINGS.itemESP
#define RADAR_SETTINGS GAME_SETTINGS.radar
#define HOTKEY_SETTINGS GAME_SETTINGS.hotkeys
#define CONFIG_SETTINGS GAME_SETTINGS.config

#define GET_TEXT(key) GAME_SETTINGS.GetLocalizedText(key)

// =============================================================================
// LEGACY COMPATIBILITY (can be removed after migration)
// =============================================================================

// Legacy aliases for backward compatibility during transition
extern AimbotCore& Aimbot;
extern TriggerbotCore& Triggerbot;
extern PlayerESPSettings& Players;
extern ItemESPSettings& Items;
extern RadarSettings& Radar;
extern HotkeySettings& Keys;
extern ConfigurationSettings& ConfigsMenu;

// Legacy arrays for weapon-specific settings
extern AimbotCore aimbot[static_cast<int>(WeaponTypes::WeaponCount)];
extern TriggerbotCore triggerbot[static_cast<int>(WeaponTypes::WeaponCount)];