#pragma once

// Custom icon definitions for icons that are not defined in font_defines.h
// These are used in the UI components

// Define missing icons
#define ICON_MS_BODY_PART ICON_MS_SKELETON
#define ICON_MS_BULLET ICON_MS_CIRCLE
#define ICON_MS_CIRCLE_FILLED ICON_MS_CIRCLE
#define ICON_MS_CIRCLE_OUTLINE ICON_MS_CIRCLE
#define ICON_MS_CROSSHAIRS ICON_MS_TARGET
#define ICON_MS_CROP_SQUARE ICON_MS_SQUARE
#define ICON_MS_DANGEROUS ICON_MS_WARNING
#define ICON_MS_DIRECTIONS_CAR ICON_MS_CAR_CRASH
#define ICON_MS_FOLDER ICON_MS_FOLDER_OPEN
#define ICON_MS_FORMAT_SIZE ICON_MS_TEXT_FIELDS
#define ICON_MS_GAVEL ICON_MS_GAVEL
#define ICON_MS_HEALING ICON_MS_MEDICAL_SERVICES
#define ICON_MS_INFO ICON_MS_INFO
#define ICON_MS_INVENTORY ICON_MS_INVENTORY_2
#define ICON_MS_LINE_WEIGHT ICON_MS_LINE_WEIGHT
#define ICON_MS_LOCAL_DRINK ICON_MS_LOCAL_DRINK
#define ICON_MS_NOTIFICATIONS ICON_MS_NOTIFICATIONS
#define ICON_MS_PALETTE ICON_MS_PALETTE
#define ICON_MS_PERSON ICON_MS_PERSON
#define ICON_MS_PERSON_OFF ICON_MS_PERSON_OFF
#define ICON_MS_PERSON_SETTINGS ICON_MS_MANAGE_ACCOUNTS
#define ICON_MS_PETS ICON_MS_PETS
#define ICON_MS_RADAR ICON_MS_RADAR
#define ICON_MS_ROCKET ICON_MS_ROCKET
#define ICON_MS_SAVE ICON_MS_SAVE
#define ICON_MS_SCIENCE ICON_MS_SCIENCE
#define ICON_MS_SETTINGS ICON_MS_SETTINGS
#define ICON_MS_SHIELD ICON_MS_SHIELD
#define ICON_MS_SMART_TOY ICON_MS_SMART_TOY
#define ICON_MS_SPEED ICON_MS_SPEED
#define ICON_MS_SPORTS_ESPORTS ICON_MS_SPORTS_ESPORTS
#define ICON_MS_SQUARE_FILLED ICON_MS_SQUARE
#define ICON_MS_STRAIGHTEN ICON_MS_STRAIGHTEN
#define ICON_MS_TARGET ICON_MS_TARGET
#define ICON_MS_TIMER ICON_MS_TIMER
#define ICON_MS_TRANSLATE ICON_MS_TRANSLATE
#define ICON_MS_TRENDING_FLAT ICON_MS_TRENDING_FLAT
#define ICON_MS_TRENDING_UP ICON_MS_TRENDING_UP
#define ICON_MS_TUNE ICON_MS_TUNE
#define ICON_MS_VISIBILITY ICON_MS_VISIBILITY
#define ICON_MS_VISIBILITY_ON ICON_MS_VISIBILITY
#define ICON_MS_WATER_DROP ICON_MS_WATER_DROP
#define ICON_MS_WEAPONS ICON_MS_SWORDS
#define ICON_MS_ZOOM_IN ICON_MS_ZOOM_IN

// Additional icon definitions needed for CheckboxComponent
#define ICON_COMPONENTS_FILL ICON_MS_SETTINGS
#define ICON_DELETE_2_LINE ICON_MS_DELETE
#define ICON_SAVE_2_LINE ICON_MS_SAVE
#define ICON_KEYBOARD_LINE ICON_MS_KEYBOARD
#define ICON_DOWN_LINE ICON_MS_ARROW_DROP_DOWN
#define ICON_UP_LINE ICON_MS_ARROW_DROP_UP
