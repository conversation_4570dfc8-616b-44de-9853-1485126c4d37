#pragma once
#define IMGUI_DEFINE_MATH_OPERATORS
#include <Windows.h>
#include <cstdint>
#include <string>
#include <locale>
#include <cmath>
#include <vector>
#include <algorithm>
#include <d3d9types.h>
#include "../Kernel Driver/Driver/Driver.h"

#define M_PI 3.14159265358979323846
#define M_PI_F ((float)(M_PI))
#define M_RADPI 57.295779513082f
#define RAD2DEG(x) ((float)(x) * (float)(180.f / M_PI_F))
#define DEG2RAD(x) ((float)(x) * (float)(M_PI / 180.f))

class Vector2 {
public:
	double x;
	double y;

	Vector2() : x(0.0), y(0.0) {}

	Vector2(double _x, double _y) : x(_x), y(_y) {}

	~Vector2() {}

	Vector2 operator-(const Vector2& other) const {
		return Vector2(x - other.x, y - other.y);
	}

	Vector2 operator+(const Vector2& other) const {
		return Vector2(x + other.x, y + other.y);
	}

	Vector2 operator*(double scalar) const {
		return Vector2(x * scalar, y * scalar);
	}

	double length() const {
		return std::sqrt(x * x + y * y);
	}

	Vector2 normalized() const {
		double len = length();
		if (len == 0) return Vector2(0, 0); // To avoid division by zero
		return Vector2(x / len, y / len);
	}
};


class Vector3 {
public:
	double x, y, z;

	Vector3() : x(0.0), y(0.0), z(0.0) {}

	Vector3(double _x, double _y, double _z) : x(_x), y(_y), z(_z) {}

	~Vector3() {}

	// Dot product calculation
	inline double Dot(Vector3 v) const {
		return x * v.x + y * v.y + z * v.z;
	}

	// Euclidean distance calculation
	inline double Distance(Vector3 v) const {
		return sqrt(pow(v.x - x, 2.0) + pow(v.y - y, 2.0) + pow(v.z - z, 2.0));
	}

	// Length of the vector
	inline double Length() const {
		return sqrt(x * x + y * y + z * z);
	}

	void Normalize() {
		double len = Length();
		if (len != 0) {
			x /= len;
			y /= len;
			z /= len;
		}
	}

	Vector3 Normalizer() const {
		float magnitude = sqrt(x * x + y * y + z * z);
		if (magnitude == 0) return Vector3(0, 0, 0); // Avoid division by zero
		return Vector3(x / magnitude, y / magnitude, z / magnitude);
	}

	// Vector addition
	Vector3 operator+(const Vector3& v) const {
		return Vector3(x + v.x, y + v.y, z + v.z);
	}

	// Vector subtraction
	Vector3 operator-(const Vector3& v) const {
		return Vector3(x - v.x, y - v.y, z - v.z);
	}

	// Scalar multiplication
	Vector3 operator*(double flNum) const {
		return Vector3(x * flNum, y * flNum, z * flNum);
	}

	// Scalar division
	Vector3 operator/(double flNum) const {
		return Vector3(x / flNum, y / flNum, z / flNum);
	}

	// Division assignment
	Vector3& operator/=(double flNum) {
		if (flNum != 0) {
			x /= flNum;
			y /= flNum;
			z /= flNum;
		}
		return *this;
	}

	// Addition assignment
	Vector3& operator+=(const Vector3& v) {
		x += v.x;
		y += v.y;
		z += v.z;
		return *this;
	}

	// Equality operator
	bool operator==(const Vector3& v) const {
		return x == v.x && y == v.y && z == v.z;
	}

	// Inequality operator
	bool operator!=(const Vector3& v) const {
		return !(*this == v);
	}
};

struct FQuat
{
	double x;
	double y;
	double z;
	double w;
};

struct FTransform
{
	FQuat Rotation; // 0x00(0x10)
	Vector3 Translation; // 0x10(0x0c)
	uint32_t pad0001; // 0x1c(0x04)
	Vector3 Scale3D; // 0x20(0x0c)
	uint32_t pad0002; // 0x2c(0x04)

	FTransform() : Rotation{ (0.f, 0.f, 0.f, 0.f) }, Translation(0.f, 0.f, 0.f), Scale3D(0.f, 0.f, 0.f), pad0001(0), pad0002(0) { }

	FTransform(const FQuat& rot, const Vector3& translation, const Vector3& scale)
	{
		Rotation = rot;
		Translation = translation;
		Scale3D = scale;

		pad0001 = 0;
		pad0002 = 0;
	}

	D3DMATRIX ToMatrixWithScale()
	{
		D3DMATRIX m;
		m._41 = Translation.x;
		m._42 = Translation.y;
		m._43 = Translation.z;

		float x2 = Rotation.x + Rotation.x;
		float y2 = Rotation.y + Rotation.y;
		float z2 = Rotation.z + Rotation.z;

		float xx2 = Rotation.x * x2;
		float yy2 = Rotation.y * y2;
		float zz2 = Rotation.z * z2;
		m._11 = (1.0f - (yy2 + zz2)) * Scale3D.x;
		m._22 = (1.0f - (xx2 + zz2)) * Scale3D.y;
		m._33 = (1.0f - (xx2 + yy2)) * Scale3D.z;

		float yz2 = Rotation.y * z2;
		float wx2 = Rotation.w * x2;
		m._32 = (yz2 - wx2) * Scale3D.z;
		m._23 = (yz2 + wx2) * Scale3D.y;

		float xy2 = Rotation.x * y2;
		float wz2 = Rotation.w * z2;
		m._21 = (xy2 - wz2) * Scale3D.y;
		m._12 = (xy2 + wz2) * Scale3D.x;

		float xz2 = Rotation.x * z2;
		float wy2 = Rotation.w * y2;
		m._31 = (xz2 + wy2) * Scale3D.z;
		m._13 = (xz2 - wy2) * Scale3D.x;

		m._14 = 0.0f;
		m._24 = 0.0f;
		m._34 = 0.0f;
		m._44 = 1.0f;

		return m;
	}
};
template<class T>
class TArray
{
public:
	int Size() const
	{
		return Count;
	}

	bool IsValid() const
	{
		if (Count > Max)
			return false;
		if (!Data)
			return false;
		return true;
	}

	uintptr_t GetAddress() const
	{
		return Data;
	}

	T operator[](int i)
	{
		return UseDriver::template read<T>(Data + i * sizeof(T));
	};


protected:
	uintptr_t Data;
	uint32_t Count;
	uint32_t Max;
};

struct Camera
{
	Vector3 Location;
	Vector3 Rotation;
	float FieldOfView;
	char Useless[0x18];
};

// Bone indices for targeting
enum EBoneIndex : int {
    Head = 110,
    Neck = 67,
    Chest = 66,
    Pelvis = 2,
    RightShoulder = 9,
    RightElbow = 10,
    RightWrist = 90,
    RightHip = 71,
    RightKnee = 72,
    RightAnkle = 75,
    LeftShoulder = 38,
    LeftElbow = 39,
    LeftWrist = 40,
    LeftHip = 78,
    LeftKnee = 79,
    LeftAnkle = 82,
    Bottom = 0,
    Root = 0
}; 