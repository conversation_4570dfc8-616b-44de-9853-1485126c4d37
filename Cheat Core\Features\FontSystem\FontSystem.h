#pragma once
#include <Windows.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <filesystem>
#include <chrono>
#include "../../../Menu UI/ImGui/imgui.h"

// Font information structure
struct FontInfo {
    std::string name;           // Display name of the font
    std::string filepath;       // Full path to the font file
    std::string filename;       // Just the filename
    bool isCustom;              // Whether this is a custom font or default
    bool isLoaded;              // Whether the font is currently loaded
    ImFont* fontPtr;            // Pointer to the loaded ImFont
    float size;                 // Font size
    std::chrono::system_clock::time_point lastModified; // Last modified time
};

// Font system class
class FontSystem {
public:
    FontSystem() = default;
    ~FontSystem() = default;
    FontSystem(const FontSystem&) = delete;
    FontSystem& operator=(const FontSystem&) = delete;
    
    static FontSystem& GetInstance();
    
    // Initialize the font system
    void Initialize();
    
    // Load default fonts
    void LoadDefaultFonts();
    
    // Load custom font from file
    bool LoadCustomFont(const std::string& filepath, float size = 16.0f);
    
    // Unload a custom font
    bool UnloadCustomFont(const std::string& name);
    
    // Get font by name
    ImFont* GetFont(const std::string& name);
    
    // Get all available fonts
    const std::vector<FontInfo>& GetAvailableFonts() const;
    
    // Get default fonts
    const std::vector<FontInfo>& GetDefaultFonts() const;
    
    // Get custom fonts
    const std::vector<FontInfo>& GetCustomFonts() const;
    
    // Check if font exists
    bool FontExists(const std::string& name) const;
    
    // Validate font file
    bool ValidateFontFile(const std::string& filepath) const;
    
    // Get font file size in MB
    float GetFontFileSize(const std::string& filepath) const;
    
    // Save font selections to config
    void SaveFontSelections();
    
    // Load font selections from config
    void LoadFontSelections();
    
    // Get font directory
    std::string GetFontDirectory() const;
    
    // Refresh fonts (reload from disk)
    void RefreshFonts();

private:
    // Font storage
    std::vector<FontInfo> defaultFonts;
    std::vector<FontInfo> customFonts;
    std::unordered_map<std::string, ImFont*> fontMap;
    
    // Configuration
    std::string fontDirectory;
    std::string configDirectory;
    
    // Font selection storage
    std::unordered_map<std::string, std::string> fontSelections; // feature -> fontName
    
    // Helper methods
    void CreateFontDirectory();
    bool LoadFontFromFile(const std::string& filepath, float size, bool isCustom = false);
    void LoadFontsFromConfig();
    void SaveFontsToConfig();
    std::string GetSystemFontPath(const std::string& fontName);
    bool IsValidFontFile(const std::string& filepath) const;
};

// Global font system instance
extern FontSystem g_FontSystem; 