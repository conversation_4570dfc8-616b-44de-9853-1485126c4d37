#pragma once
#include <string>
#include <vector>
#include <unordered_set>
#include <memory>
#include <iostream>
#include <algorithm>
#include "../ImGui/imgui.h"

/**
 * @brief Inline implementation of PremiumSystem to avoid linking issues
 * This is a temporary solution - use the separate .h/.cpp files for production
 */
class PremiumSystem {
public:
    static PremiumSystem& getInstance() {
        static PremiumSystem instance;
        return instance;
    }

    bool isPremiumUser() const {
        return isPremium;
    }

    void setPremiumUser(bool premium) {
        isPremium = premium;
    }

    bool isFeaturePremium(const std::string& featureName) const {
        return premiumFeatures.find(featureName) != premiumFeatures.end();
    }

    void addPremiumFeature(const std::string& featureName) {
        premiumFeatures.insert(featureName);
    }

    void removePremiumFeature(const std::string& featureName) {
        premiumFeatures.erase(featureName);
    }

    std::vector<std::string> getPremiumFeatures() const {
        std::vector<std::string> features;
        features.reserve(premiumFeatures.size());
        
        for (const auto& feature : premiumFeatures) {
            features.push_back(feature);
        }
        
        std::sort(features.begin(), features.end());
        return features;
    }

    bool shouldLockFeature(const std::string& featureName) const {
        return isFeaturePremium(featureName) && !isPremiumUser();
    }

    void initializeDefaultPremiumFeatures() {
        // Add default premium features
        addPremiumFeature("Silent Aimbot");
        addPremiumFeature("Freeze Player");
        addPremiumFeature("Aim Lock");
        addPremiumFeature("Prediction");
        addPremiumFeature("Save Target");
        addPremiumFeature("Humanized Smooth");
        
        // For demonstration, set to false - in a real app this would check license
        setPremiumUser(false);
    }

    void onUpgradeButtonClick() {
        std::cout << "Premium upgrade requested - opening upgrade page..." << std::endl;
        // In real implementation, you would open a browser or show a dialog
        // system("start https://your-premium-upgrade-url.com");
    }

private:
    PremiumSystem() = default;
    ~PremiumSystem() = default;
    
    // Non-copyable
    PremiumSystem(const PremiumSystem&) = delete;
    PremiumSystem& operator=(const PremiumSystem&) = delete;

    bool isPremium = false;
    std::unordered_set<std::string> premiumFeatures;
};

/**
 * @brief Animation state for premium UI elements
 */
struct PremiumAnimationState {
    float premiumBadgeOpacity = 0.0f;
    float premiumBadgeScale = 1.0f;
    float upgradeButtonOpacity = 0.0f;
    float upgradeButtonScale = 1.0f;
    float lockOverlayOpacity = 0.0f;
    float premiumGlow = 0.0f;
    float premiumPulse = 0.0f;
    float time = 0.0f;
};

/**
 * @brief Premium colors for consistent theming
 */
namespace PremiumColors {
    inline const ImVec4 PREMIUM_GOLD = ImVec4(1.0f, 0.84f, 0.0f, 1.0f);
    inline const ImVec4 PREMIUM_GOLD_LIGHT = ImVec4(1.0f, 0.92f, 0.4f, 1.0f);
    inline const ImVec4 PREMIUM_GOLD_DARK = ImVec4(0.8f, 0.67f, 0.0f, 1.0f);
    inline const ImVec4 LOCK_OVERLAY = ImVec4(0.0f, 0.0f, 0.0f, 0.7f);
    inline const ImVec4 UPGRADE_BUTTON = ImVec4(0.2f, 0.7f, 1.0f, 1.0f);
    inline const ImVec4 UPGRADE_BUTTON_HOVER = ImVec4(0.3f, 0.8f, 1.0f, 1.0f);
} 