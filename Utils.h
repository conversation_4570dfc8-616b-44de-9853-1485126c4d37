#pragma once
#include <WinUser.h>
#include <fstream>
#include <io.h>
#include <d3d11.h>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "Cheat Core/Framwork/Vectors.h"
#include "Cheat Core/GameClass/GameSettings.h"
#include "Cheat Core/GameClass/Offsets.h"

#pragma comment(lib, "urlmon.lib")

inline ImFont* iconsBig = nullptr;
inline ImFont* iconsSmall = nullptr;
inline ImFont* tab_title = nullptr;
inline ImFont* logofont = nullptr;
inline ID3D11ShaderResourceView* users = nullptr;


inline float GetAnimSpeed() { return ImGui::GetIO().DeltaTime * 10.f; };
inline float random_float(float min, float max)
{
    return min + float(rand() / float(RAND_MAX)) * (max - min);
}


inline const char* findSymbolAfterCaret(const char* description) {
    const char* position = strchr(description, '^');
    return (position && *(position + 1) != '\0') ? position + 1 : nullptr;
}

inline const char* getStringBeforeCaret(const char* str) {
    const char* pos = strchr(str, '^');

    if (pos) {
        size_t len = pos - str;
        if (len > 0) {
            char* result = new char[len + 1];
            strncpy_s(result, len + 1, str, len);
            result[len] = '\0';
            return result;
        }
    }
    return nullptr;
}
inline namespace c
{
    inline ImColor main_color(200, 200, 200, 255);
    inline ImColor second_color(200, 200, 200, 38);
    inline ImColor background_color(12, 12, 12, 25);
    inline ImColor stroke_color(255, 255, 255, 0);
    inline ImColor window_bg_color(8, 8, 8, 255);

    inline ImVec4 accent = ImColor(118, 187, 117);
    inline ImVec4 separator = ImColor(22, 23, 26);

    inline namespace anim
    {
        inline float speed;
    }

    inline namespace bg
    {
        inline ImVec4 background = ImColor(15, 15, 15);
        inline ImVec2 size = ImVec2(700, 600);
        inline float rounding = 8.f;
    }

    inline namespace child
    {
        inline ImVec4 background = ImColor(17, 17, 18);
        inline ImVec4 cap = ImColor(20, 21, 23);
        inline float rounding = 8.f;
    }

    namespace page
    {
        inline ImVec4 background_active = ImColor(31, 33, 38);
        inline ImVec4 background = ImColor(22, 23, 25);

        inline ImVec4 text_hov = ImColor(69, 74, 95);
        inline ImVec4 text = ImColor(68, 71, 85);

        inline float rounding = 4.f;
    }

    inline namespace elements
    {
        inline ImVec4 background_hovered = ImColor(31, 33, 38);
        inline ImVec4 background = ImColor(22, 23, 25);
        inline float rounding = 3.f;
    }

    inline namespace checkbox
    {
        inline ImVec4 mark = ImColor(0, 0, 0, 255);
    }

    inline namespace text
    {
        inline ImVec4 text_active = ImColor(255, 255, 255);
        inline ImVec4 text_hov = ImColor(69, 74, 95);
        inline ImVec4 text = ImColor(68, 71, 85);
    }
}

namespace utils
{
    inline ImColor GetColorWithAlpha(ImColor color, float alpha)
    {
        return ImColor(color.Value.x, color.Value.y, color.Value.z, alpha);
    }

    inline ImVec2 center_text(ImVec2 min, ImVec2 max, const char* text)
    {
        return min + (max - min) / 2 - ImGui::CalcTextSize(text) / 2;
    }

    inline ImColor GetDarkColor(const ImColor& color)
    {
        float r, g, b, a;
        r = color.Value.x;
        g = color.Value.y;
        b = color.Value.z;
        a = 255;

        float darkPercentage = 0.5f;
        float darkR = r * darkPercentage;
        float darkG = g * darkPercentage;
        float darkB = b * darkPercentage;

        return ImColor(darkR, darkG, darkB, a);
    }
}
inline bool FileExists(const std::string& Filename)
{
    return _access_s(Filename.c_str(), 04) == 0;
}

inline std::string LoadChinese() {
    std::string dwnld_URL = "https://cdn.chairfbi.com/ChineseFont.ttf";
    std::string savepath = "ChineseFont.ttf";
    if (!FileExists(savepath)) {
        if (URLDownloadToFileA(NULL, dwnld_URL.c_str(), savepath.c_str(), 0, NULL) != S_OK) {
            return "";
        }
    }
    return FileExists(savepath) ? savepath : "";
}

inline bool IsKeyPressed(int VK)
{
    if (VK == 0) return false;
    return (GetAsyncKeyState(VK) & 0x8000) != 0;
}

inline void AimMove(Vector2 Head2D, float distance, float fovByWeapon, float smoothByWeapon, float humanizedSmoothPercentByWeapon, bool& HumanizedSmooth) {
    float x = Head2D.x;
    float y = Head2D.y;

    Vector2 ScreenCenter = { (double)OverlayWidth / 2 , (double)OverlayHeight / 2 };
    Vector2 Target;

    // Base smoothing with a slight random variation to mimic human behavior
    float baseSmoothing = smoothByWeapon /*+ Utils::RandomRange(-0.05f, 0.05f)*/;
    float dynamicSmoothing = baseSmoothing;

    // Check if humanized smoothing is enabled
    if (HumanizedSmooth) {
        float maxIncrease = baseSmoothing * humanizedSmoothPercentByWeapon;
        if (distance <= fovByWeapon) {
            float scale = 1.0f - (distance / fovByWeapon);
            dynamicSmoothing += maxIncrease * scale;
        }
        dynamicSmoothing = (std::max)(dynamicSmoothing, baseSmoothing);
    }
    if (AIMBOT_SETTINGS.general.aimLock) {
        HumanizedSmooth = false;
        dynamicSmoothing = 1;
    }
    // Adjust the mouse target position using dynamic smoothing
    if (x != 0) {
        if (x > ScreenCenter.x) {
            Target.x = -(ScreenCenter.x - x) / dynamicSmoothing;
            if (Target.x + ScreenCenter.x > ScreenCenter.x * 2) Target.x = 0;
        }
        if (x < ScreenCenter.x) {
            Target.x = (x - ScreenCenter.x) / dynamicSmoothing;
            if (Target.x + ScreenCenter.x < 0) Target.x = 0;
        }
    }
    if (y != 0) {
        if (y > ScreenCenter.y) {
            Target.y = -(ScreenCenter.y - y) / dynamicSmoothing;
            if (Target.y + ScreenCenter.y > ScreenCenter.y * 2) Target.y = 0;
        }
        if (y < ScreenCenter.y) {
            Target.y = (y - ScreenCenter.y) / dynamicSmoothing;
            if (Target.y + ScreenCenter.y < 0) Target.y = 0;
        }
    }

    // Use system mouse event or a driver-specific function to move cursor
    Driver->MoveMouse(Target.x, Target.y, 0);
}
