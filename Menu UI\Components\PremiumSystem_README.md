# Premium System Integration

This document explains how to use the premium system with the CheckboxComponent UI for dynamic feature management.

## Overview

The premium system allows you to mark specific features as premium-only and dynamically control access based on user subscription status. It provides smooth animations, visual feedback, and prevents non-premium users from accessing premium features.

## Features

### For Non-Premium Users
- **Locked Features**: Premium features appear with a lock overlay
- **Upgrade Button**: Central "Upgrade to Premium" button with hover animations
- **Visual Feedback**: Clear indication that features are locked
- **Prevention**: Features cannot be enabled without premium access

### For Premium Users
- **Premium Badge**: Animated "PRO" badge with golden glow effect
- **Full Access**: All premium features are accessible
- **Smooth Animations**: Pulsing and scaling effects for premium badges

## Quick Start

### 1. Initialize the Premium System

```cpp
#include "PremiumSystem.h"

// In your application startup
void InitializeApp() {
    PremiumSystem& premiumSystem = PremiumSystem::getInstance();
    
    // Add premium features
    premiumSystem.addPremiumFeature("Silent Aimbot");
    premiumSystem.addPremiumFeature("Freeze Player");
    premiumSystem.addPremiumFeature("Aim Lock");
    premiumSystem.addPremiumFeature("Prediction");
    
    // Set user premium status (check license, config, etc.)
    premiumSystem.setPremiumUser(false); // Start as non-premium
}
```

### 2. Use Premium CheckboxComponents

```cpp
#include "nav_elements.h"

void RenderMenu() {
    static bool silentAimbot = false;
    static bool normalFeature = false;
    
    // Premium feature - will be locked for non-premium users
    nav_elements::CheckboxComponent("Silent Aimbot", &silentAimbot, 
        "Advanced aimbot functionality^🎯", 
        false, std::vector<nav_elements::ColorState>{}, 
        nullptr, nullptr, true); // <- isPremium = true
    
    // Regular feature - always available
    nav_elements::CheckboxComponent("Normal Feature", &normalFeature, 
        "Standard functionality^✓", 
        false, std::vector<nav_elements::ColorState>{}, 
        nullptr, nullptr, false); // <- isPremium = false
}
```

## API Reference

### PremiumSystem Class

#### Methods

```cpp
// Get singleton instance
static PremiumSystem& getInstance();

// Check premium status
bool isPremiumUser() const;
void setPremiumUser(bool premium);

// Manage premium features
bool isFeaturePremium(const std::string& featureName) const;
void addPremiumFeature(const std::string& featureName);
void removePremiumFeature(const std::string& featureName);
std::vector<std::string> getPremiumFeatures() const;

// Check if feature should be locked
bool shouldLockFeature(const std::string& featureName) const;

// Handle upgrade button click
void onUpgradeButtonClick();
```

### CheckboxComponent Premium Parameter

The `CheckboxComponent` now accepts an additional `isPremium` parameter:

```cpp
bool CheckboxComponent(
    const char* name,           // Feature name
    bool* v,                   // Value pointer
    const char* description,   // Description with optional icon
    bool hasHotkey,           // Whether feature has hotkey
    std::vector<ColorState> colorStates, // Color configurations
    int* key,                 // Hotkey pointer
    bool* mode,               // Hotkey mode pointer
    bool isPremium = false    // Premium flag (new parameter)
);
```

## Integration Examples

### Example 1: Basic Premium Feature

```cpp
// Mark feature as premium by setting isPremium = true
static bool advancedESP = false;
nav_elements::CheckboxComponent("Advanced ESP", &advancedESP, 
    "Enhanced ESP with additional info^👁", 
    false, std::vector<nav_elements::ColorState>{}, 
    nullptr, nullptr, true);
```

### Example 2: Dynamic Premium Features

```cpp
// Add features to premium system dynamically
PremiumSystem& ps = PremiumSystem::getInstance();
ps.addPremiumFeature("Dynamic Feature");

// This will be treated as premium even with isPremium = false
static bool dynamicFeature = false;
nav_elements::CheckboxComponent("Dynamic Feature", &dynamicFeature, 
    "Dynamically managed premium feature^⚡", 
    false, std::vector<nav_elements::ColorState>{}, 
    nullptr, nullptr, false); // Will still be premium due to PremiumSystem
```

### Example 3: Premium Features with Hotkeys and Colors

```cpp
static bool premiumAimbot = false;
static int aimbotKey = 0;
static bool aimbotMode = true;

std::vector<nav_elements::ColorState> premiumColors = {
    {"Primary", {1.0f, 0.84f, 0.0f, 1.0f}},  // Gold
    {"Secondary", {1.0f, 0.92f, 0.4f, 1.0f}} // Light gold
};

nav_elements::CheckboxComponent("Premium Aimbot", &premiumAimbot, 
    "Premium aimbot with advanced features^🎯", 
    true, premiumColors, &aimbotKey, &aimbotMode, true);
```

## Visual Design

### Colors
- **Premium Gold**: `#FFD700` (Primary premium color)
- **Premium Gold Light**: `#FFEB66` (Highlights and borders)
- **Premium Gold Dark**: `#CC9900` (Shadows and depth)
- **Lock Overlay**: Semi-transparent black `rgba(0,0,0,0.7)`
- **Upgrade Button**: Blue gradient `#3399FF` to `#4DAAFF`

### Animations
- **Premium Badge**: Smooth pulsing scale (±10%)
- **Premium Glow**: Animated shadow intensity
- **Lock Overlay**: Fade in/out based on premium status
- **Upgrade Button**: Scale on hover (105%)
- **Transitions**: Smooth interpolation using ImLerp

## Best Practices

### 1. Consistent Feature Naming
Use the same feature names throughout your application:
```cpp
// Good - consistent naming
premiumSystem.addPremiumFeature("Silent Aimbot");
nav_elements::CheckboxComponent("Silent Aimbot", ...);

// Bad - inconsistent naming
premiumSystem.addPremiumFeature("silent_aimbot");
nav_elements::CheckboxComponent("Silent Aimbot", ...);
```

### 2. Logical Premium Feature Selection
Choose features that add significant value:
```cpp
// Good premium features
- Advanced algorithms (Silent Aimbot, Prediction)
- Enhanced visuals (Advanced ESP, Health/Shield display)
- Convenience features (Auto-features, Smart targeting)

// Avoid making basic features premium
- Basic ESP boxes
- Simple toggles
- Essential functionality
```

### 3. Handle Upgrade Button Clicks
Implement proper upgrade flow:
```cpp
void PremiumSystem::onUpgradeButtonClick() {
    // Open upgrade page
    system("start https://yoursite.com/upgrade");
    
    // Or show licensing dialog
    ShowLicenseDialog();
    
    // Or handle in-app purchase
    ProcessUpgrade();
}
```

### 4. Persistent Premium Status
Save and load premium status:
```cpp
// On app start
bool savedPremiumStatus = LoadPremiumStatusFromConfig();
premiumSystem.setPremiumUser(savedPremiumStatus);

// On premium status change
void OnPremiumStatusChanged(bool isPremium) {
    premiumSystem.setPremiumUser(isPremium);
    SavePremiumStatusToConfig(isPremium);
}
```

## Troubleshooting

### Feature Not Showing as Premium
1. Check feature name spelling matches exactly
2. Verify `isPremium = true` is set in CheckboxComponent call
3. Ensure PremiumSystem is initialized before use

### Animations Not Working
1. Verify ImGui is rendering in a loop
2. Check that GetAnimSpeed() returns valid values
3. Ensure window DrawList is available

### Upgrade Button Not Responding
1. Implement `onUpgradeButtonClick()` method
2. Check mouse hover detection
3. Verify button rect calculations

## Advanced Usage

### Custom Premium Colors
```cpp
// Define custom premium theme
namespace CustomPremiumColors {
    const ImVec4 CUSTOM_GOLD = ImVec4(1.0f, 0.75f, 0.0f, 1.0f);
    const ImVec4 CUSTOM_HIGHLIGHT = ImVec4(1.0f, 0.85f, 0.3f, 1.0f);
}

// Use in color states
std::vector<nav_elements::ColorState> customColors = {
    {"Custom Premium", {1.0f, 0.75f, 0.0f, 1.0f}}
};
```

### Conditional Premium Features
```cpp
// Make features premium based on conditions
bool shouldBePremium = (featureComplexity > threshold);
nav_elements::CheckboxComponent("Adaptive Feature", &feature, 
    "Feature with adaptive premium status^🔄", 
    false, std::vector<nav_elements::ColorState>{}, 
    nullptr, nullptr, shouldBePremium);
```

### Integration with Existing Systems
```cpp
// Link with license system
bool hasValidLicense = LicenseManager::checkLicense();
premiumSystem.setPremiumUser(hasValidLicense);

// Link with subscription system
bool hasActiveSubscription = SubscriptionManager::isActive();
premiumSystem.setPremiumUser(hasActiveSubscription);
```

## Demo

To see the premium system in action, run the demo:
```cpp
#include "PremiumDemo.cpp"

void MainLoop() {
    // Initialize on first run
    static bool initialized = false;
    if (!initialized) {
        InitializePremiumSystem();
        initialized = true;
    }
    
    // Render demo window
    RenderPremiumDemo();
}
```

The demo shows:
- Premium vs non-premium user experience
- Dynamic feature management
- All animation effects
- Integration examples

## Conclusion

The premium system provides a complete solution for managing premium features with professional UI/UX. It's designed to be:
- **Easy to integrate**: Minimal code changes required
- **Visually appealing**: Smooth animations and consistent theming
- **Flexible**: Dynamic feature management and customization
- **User-friendly**: Clear visual feedback and upgrade path

For additional support or feature requests, refer to the source code comments or create an issue in the project repository. 