#pragma once
#include "PremiumSystem_inline.h"

/**
 * @brief Call this function once during application startup to initialize the premium system
 */
inline void InitializePremiumSystemOnStartup() {
    PremiumSystem& premiumSystem = PremiumSystem::getInstance();
    
    // Initialize default premium features
    premiumSystem.initializeDefaultPremiumFeatures();
    
    // You can add more premium features here
    premiumSystem.addPremiumFeature("Weapon ESP");
    premiumSystem.addPremiumFeature("Health ESP"); 
    premiumSystem.addPremiumFeature("Shield ESP");
    
    // Set initial premium status (in real app, check license file, subscription, etc.)
    // For testing, you can change this to true to see premium features unlocked
    premiumSystem.setPremiumUser(false);
    
    std::cout << "Premium system initialized with " 
              << premiumSystem.getPremiumFeatures().size() 
              << " premium features" << std::endl;
}

/**
 * @brief Toggle premium status for testing purposes
 */
inline void TogglePremiumStatusForTesting() {
    PremiumSystem& premiumSystem = PremiumSystem::getInstance();
    bool currentStatus = premiumSystem.isPremiumUser();
    premiumSystem.setPremiumUser(!currentStatus);
    
    std::cout << "Premium status toggled to: " 
              << (premiumSystem.isPremiumUser() ? "Premium" : "Free") 
              << std::endl;
} 