#pragma once
#include <windows.h>
#include <cstdint>
#include <algorithm>
#include <iostream>
#include <iomanip>
#include <map>
#include <functional>
#include <vector>
#include <string>
#include "Animation/ImAnim/custom_functions.h"
#include "../ImGui/imgui_settings.h"

inline float content_anim = 0.f;
namespace nav_elements {
    // Color state structure for CheckboxComponent
    struct ColorState {
        std::string label;
        float color[4] = {1.0f, 1.0f, 1.0f, 1.0f};
        float* originalColor = nullptr; // Pointer to the original color in Settings
    };

    bool Tab(const char* label, const char* icon, int* v, int number);
    bool TabVS2(const char* icon, const char* name, bool boolean);

    // New horizontal tabs for category selection
    bool HorizontalTab(const char* label, int* v, int number);
    bool HorizontalTabV2(const char* name, bool boolean);

    bool SliderScalar(const char* label, ImGuiDataType data_type, void* p_data, const void* p_min, const void* p_max, const char* format, ImGuiSliderFlags flags, const char* description , const char* icon);

    bool SliderFloat(const char* label, float* v, float v_min, float v_max, const char* description , const char* icon, const char* format = "%.1f", ImGuiSliderFlags flags = 0);
    bool SliderInt(const char* label, int* v, int v_min, int v_max, const char* description , const char* icon, const char* format = "%d", ImGuiSliderFlags flags = 0);

    bool Keybind(const char* label, const char* description, const char* icon, int* key, bool* mode);

    bool ColorPickerComponent(const char* name, std::vector<ColorState>& colorStates, const char* description, const char* icon);
    // Combo box functions
    bool BeginCombo(const char* label, const char* preview_value, int val, const char* description , const char* icon, ImGuiComboFlags flags = 0);
    void EndCombo();
    void MultiCombo(const char* label, bool variable[], const char* labels[], int count, const char* icon);
    bool BeginComboPreview();
    void EndComboPreview();


    bool Combo(const char* label, int* current_item, const char* const items[], int items_count, const char* description , const char* icon, int height_in_items = -1);
    bool ComboEx(const char* label, int* current_item, const char* const items[], int items_count, const char* description,const char* icon );
        
    bool Combo(const char* label, int* current_item, const char* items_separated_by_zeros, const char* description , const char* icon, int height_in_items = -1);
    bool Combo(const char* label, int* current_item, const char* (*getter)(void* user_data, int idx), void* user_data, int items_count, const char* description , const char* icon, int popup_max_height_in_items = -1);

    // Selectable functions
    bool SelectableEx(const char* label, bool selected, ImGuiSelectableFlags flags = 0, const ImVec2& size_arg = ImVec2(0, 0));
    bool Selectable(const char* label, bool* p_selected, ImGuiSelectableFlags flags = 0, const ImVec2& size_arg = ImVec2(0, 0));

    bool Button(const char* label, const ImVec2& size_arg, ImColor color = gui.main);

    // New button functions with icons
    bool IconButton(const char* label, const char* icon, const ImVec2& size_arg, ImColor color = gui.main, bool selected = false);
    bool IconOnlyButton(const char* icon, const ImVec2& size_arg, ImColor color = gui.main, bool selected = false);

    // Enhanced checkbox with color pickers and settings button
    bool CheckboxComponent(const char* name, bool* v, const char* description, const char* ico,bool hasHotkey, std::vector<ColorState> colorStates, int* key, bool* mode, bool isPremium = false);
    bool ColorEdit4Ex(const char* label, const char* description, float col[4], ImGuiColorEditFlags flags);
    
    // Inline expansion system functions
    bool IsFeatureExpanded(const std::string& featureName);
    float GetFeatureExpansionState(const std::string& featureName);
    
    // New function for rendering animated expanded content
    void RenderExpandedContentAnimated(const std::string& featureName, std::function<void()> contentRenderer);
    bool ColorPicker4(const char* label, float col[4], ImGuiColorEditFlags flags, const float* ref_col);
    bool ColorButton(const char* desc_id, const ImVec4& col, ImGuiColorEditFlags flags, const ImVec2& size_arg);

    // Font selection component
    bool FontSelector(const char* label, std::string* selectedFont, const char* description, const char* icon);

    void BeginGroup();
    void EndGroup();
    void Theme();
    const ImWchar* GetGlyphRangesChinese();

}

