#pragma once
#include <Windows.h>
#include "../GameClass/GameSettings.h"

// Font settings structure (UI fonts configuration)
struct AppFontSettings {
    std::string defaultFont = "Arial";
    std::string playerEspFont = "Arial";
    std::string itemEspFont = "Arial";
    std::string radarFont = "Arial";
    std::string aimbotFont = "Arial";
    std::string uiFont = "Arial";
    float defaultFontSize = 16.0f;
    float playerEspFontSize = 16.0f;
    float itemEspFontSize = 14.0f;
    float radarFontSize = 12.0f;
    float aimbotFontSize = 16.0f;
    float uiFontSize = 16.0f;
};

// Main settings struct to store all game settings
struct SettingsStruct {
    // ESP Settings
    struct EspSettings {
        bool Enable = true;
        // Player settings
        PlayerESPSettings Player;
        // Item settings
        ItemESPSettings Item;
    } Esp;

    // Aimbot Settings
    struct AimSettings {
        AimbotCore AimbotConfig;
        AimbotSettings WeaponConfigs;
        TriggerbotCore TriggerConfig;
    } Aim;

    // Colors
    struct ColorSettings {
        // Placeholder color arrays to preserve compatibility with existing UI helpers
        float AimbotColorsFov[3] {0.0f, 1.0f, 0.0f};
        float AimbotColorsCrossHair[3] {1.0f, 0.0f, 0.0f};
        float AimbotColorsTarget[3] {1.0f, 1.0f, 0.0f};

        // Player color arrays used in visuals (to be migrated in Phase 4)
        float PlayerColors_BoxVisible[4] {0.0f,1.0f,0.0f,1.0f};
        float PlayerColors_BoxNonVisible[4] {1.0f,1.0f,1.0f,1.0f};
        float PlayerColors_KnockedBoxVisible[4] {0.0f,0.0f,1.0f,1.0f};
        float PlayerColors_KnockedBoxNonVisible[4] {1.0f,1.0f,1.0f,1.0f};
        float PlayerColors_BoxFillVisible[4] {0.0f,1.0f,0.0f,0.2f};
        float PlayerColors_BoxFillNonVisible[4] {1.0f,1.0f,1.0f,0.2f};
        float PlayerColors_KnockedBoxFillVisible[4] {0.0f,0.0f,1.0f,0.2f};
        float PlayerColors_KnockedBoxFillNonVisible[4] {1.0f,1.0f,1.0f,0.2f};
        float PlayerColors_LineVisible[3] {0.0f,1.0f,0.0f};
        float PlayerColors_LineNonVisible[3] {1.0f,1.0f,1.0f};
        float PlayerColors_KnockedLineVisible[3] {0.0f,0.0f,1.0f};
        float PlayerColors_KnockedLineNonVisible[3] {1.0f,1.0f,1.0f};
        float PlayerColors_HeadVisible[3] {0.0f,1.0f,0.0f};
        float PlayerColors_HeadNonVisible[3] {1.0f,1.0f,1.0f};
        float PlayerColors_KnockedHeadVisible[3] {0.0f,0.0f,1.0f};
        float PlayerColors_KnockedHeadNonVisible[3] {1.0f,1.0f,1.0f};
        float PlayerColors_SkeletonVisible[3] {0.0f,1.0f,0.0f};
        float PlayerColors_SkeletonNonVisible[3] {1.0f,1.0f,1.0f};
        float PlayerColors_KnockedSkeletonVisible[3] {0.0f,0.0f,1.0f};
        float PlayerColors_KnockedSkeletonNonVisible[3] {1.0f,1.0f,1.0f};
        float PlayerColors_RadarVisible[3] {0.0f,1.0f,0.0f};
        float PlayerColors_RadarNonVisible[3] {1.0f,1.0f,1.0f};
        float PlayerColors_KnockedRadarVisible[3] {0.0f,0.0f,1.0f};
        float PlayerColors_KnockedRadarNonVisible[3] {1.0f,1.0f,1.0f};
    } Colors;

    // Radar
    struct RadarSettingsLegacy {
        int DistanceFontSize = 10;
    } Radar;

    // Key Bindings (legacy mirror for config system; will be bridged to HOTKEY_SETTINGS)
    struct KeyBindingsLegacy {
        int AimbotEnable = 0;
        bool AimbotEnableMode = true;
        int PlayerEspEnable = 0;
        bool PlayerEspEnableMode = true;
        int ItemConsumableEnable = 0;
        bool ItemConsumableEnableMode = true;
        int ItemWeaponEnable = 0;
        bool ItemWeaponEnableMode = true;
        int ItemAmmoEnable = 0;
        bool ItemAmmoEnableMode = true;
        int RadarEnable = 0;
        bool RadarEnableMode = true;
    } Keys;

    // Configs
    struct ConfigSettingsLegacy {
        int selectedWeapon = 0;
    } Config;

    // Font Settings
    AppFontSettings Fonts;
};

// Global settings instance
extern SettingsStruct Settings;

// Helper functions for settings management
namespace SettingsHelper {
    // Sync settings between struct and global variables (call every frame)
    void SyncSettings();

    // Initialize settings with defaults
    void InitializeSettings();

    // Save settings to file
    bool SaveSettings();

    // Load settings from file
    bool LoadSettings();

    // Reset settings to defaults
    void ResetSettings();
}