#pragma once
#include <Windows.h>
#include <unordered_map>
#include <functional>
#include <chrono>
#define IMGUI_DEFINE_MATH_OPERATORS
#include "../ImGui/imgui.h"
#include "../ImGui/imgui_internal.h"

namespace AnimationSystem {

    // Animation types for different effects
    enum class AnimationType {
        FadeIn,
        FadeOut,
        SlideUp,
        SlideDown,
        SlideLeft,
        SlideRight,
        ScaleIn,
        ScaleOut,
        ScaleWithBounce,
        RotateIn,
        Custom
    };

    // Easing function types
    enum class EasingType {
        Linear,
        EaseInQuad,
        EaseOutQuad,
        EaseInOutQuad,
        EaseInCubic,
        EaseOutCubic,
        EaseInOutCubic,
        EaseInQuart,
        EaseOutQuart,
        EaseInOutQuart,
        EaseInExpo,
        EaseOutExpo,
        EaseInOutExpo,
        EaseInBack,
        EaseOutBack,
        EaseInOutBack,
        EaseInElastic,
        EaseOutElastic,
        EaseInOutElastic,
        EaseInBounce,
        EaseOutBounce,
        EaseInOutBounce
    };

    // Animation state for individual elements
    struct AnimationState {
        float currentValue = 0.0f;
        float targetValue = 1.0f;
        float startValue = 0.0f;
        float duration = 0.5f;
        float startTime = 0.0f;
        bool isActive = false;
        bool isCompleted = false;
        bool autoReverse = false;
        float delay = 0.0f;
        AnimationType type = AnimationType::FadeIn;
        EasingType easing = EasingType::EaseOutCubic;
        std::function<void()> onComplete = nullptr;
    };

    // Window animation state
    struct WindowAnimationState {
        float alpha = 0.0f;
        float scale = 0.8f;
        ImVec2 position = ImVec2(0, 0);
        ImVec2 size = ImVec2(0, 0);
        bool isVisible = false;
        bool isAnimating = false;
        float animationProgress = 0.0f;
    };

    // Content element animation state
    struct ContentAnimationState {
        float alpha = 0.0f;
        float slideOffset = 30.0f;
        float scale = 0.95f;
        int elementIndex = 0;
        bool hasStarted = false;
        float staggerDelay = 0.05f;
    };

    // Tab transition state
    struct TabTransitionState {
        float progress = 1.0f;
        int fromTab = -1;
        int toTab = -1;
        float startTime = 0.0f;
        bool isTransitioning = false;
        AnimationType transitionType = AnimationType::SlideLeft;
    };

    // Popup animation state
    struct PopupAnimationState {
        float alpha = 0.0f;
        float scale = 0.9f;
        float blur = 10.0f;
        bool isVisible = false;
        bool isAnimating = false;
        float backgroundDim = 0.0f;
    };

    class AnimationManager {
    private:
        std::unordered_map<std::string, AnimationState> animations;
        WindowAnimationState windowState;
        TabTransitionState tabState;
        PopupAnimationState popupState;
        ContentAnimationState contentState;
        
        // Timing
        float currentTime = 0.0f;
        float deltaTime = 0.0f;
        float lastFrameTime = 0.0f;

    public:
        // Initialize the animation system
        void Initialize();
        
        // Update all animations
        void Update();
        
        // Easing functions
        static float Linear(float t);
        static float EaseInQuad(float t);
        static float EaseOutQuad(float t);
        static float EaseInOutQuad(float t);
        static float EaseInCubic(float t);
        static float EaseOutCubic(float t);
        static float EaseInOutCubic(float t);
        static float EaseInQuart(float t);
        static float EaseOutQuart(float t);
        static float EaseInOutQuart(float t);
        static float EaseInExpo(float t);
        static float EaseOutExpo(float t);
        static float EaseInOutExpo(float t);
        static float EaseInBack(float t);
        static float EaseOutBack(float t);
        static float EaseInOutBack(float t);
        static float EaseInElastic(float t);
        static float EaseOutElastic(float t);
        static float EaseInOutElastic(float t);
        static float EaseInBounce(float t);
        static float EaseOutBounce(float t);
        static float EaseInOutBounce(float t);
        
        // Apply easing function
        static float ApplyEasing(float t, EasingType type);
        
        // Animation creation and control
        void StartAnimation(const std::string& id, float from, float to, float duration, 
                          EasingType easing = EasingType::EaseOutCubic, float delay = 0.0f,
                          std::function<void()> onComplete = nullptr);
        
        void StopAnimation(const std::string& id);
        void PauseAnimation(const std::string& id);
        void ResumeAnimation(const std::string& id);
        float GetAnimationValue(const std::string& id);
        bool IsAnimationActive(const std::string& id);
        bool IsAnimationCompleted(const std::string& id);
        
        // Window animations
        void ShowWindow(float duration = 0.4f, AnimationType type = AnimationType::ScaleWithBounce);
        void HideWindow(float duration = 0.3f, AnimationType type = AnimationType::ScaleOut);
        void UpdateWindowAnimation();
        WindowAnimationState& GetWindowState() { return windowState; }
        
        // Tab transition animations
        void StartTabTransition(int fromTab, int toTab, float duration = 0.35f, 
                              AnimationType type = AnimationType::SlideLeft);
        void UpdateTabTransition();
        TabTransitionState& GetTabState() { return tabState; }
        
        // Content element animations
        void StartContentAnimation(int elementCount, float staggerDelay = 0.05f, float duration = 0.6f);
        void ResetContentAnimation();
        bool ShouldRenderElement(int elementIndex);
        float GetElementAlpha(int elementIndex);
        float GetElementSlideOffset(int elementIndex);
        float GetElementScale(int elementIndex);
        ContentAnimationState& GetContentState() { return contentState; }
        
        // Popup animations
        void ShowPopup(float duration = 0.25f, AnimationType type = AnimationType::ScaleIn);
        void HidePopup(float duration = 0.2f, AnimationType type = AnimationType::ScaleOut);
        void UpdatePopupAnimation();
        PopupAnimationState& GetPopupState() { return popupState; }
        
        // Utility functions
        static ImVec2 ApplyScaleToCenter(ImVec2 originalPos, ImVec2 originalSize, float scale);
        static void PushAnimatedStyle(float alpha, float scale = 1.0f, ImVec2 offset = ImVec2(0, 0));
        static void PopAnimatedStyle();
        
        // Advanced animations
        void CreateSpringAnimation(const std::string& id, float from, float to, 
                                 float tension = 300.0f, float friction = 20.0f);
        void CreateWaveAnimation(const std::string& id, float amplitude = 1.0f, 
                               float frequency = 2.0f, float duration = 2.0f);
    };

    // Global animation manager instance
    extern AnimationManager g_AnimationManager;

    // Helper macros for common animations
    #define ANIMATE_ALPHA(id, target, duration) g_AnimationManager.StartAnimation(id, g_AnimationManager.GetAnimationValue(id), target, duration)
    #define ANIMATE_SLIDE(id, target, duration) g_AnimationManager.StartAnimation(id, g_AnimationManager.GetAnimationValue(id), target, duration, AnimationSystem::EasingType::EaseOutBack)
    #define ANIMATE_SCALE(id, target, duration) g_AnimationManager.StartAnimation(id, g_AnimationManager.GetAnimationValue(id), target, duration, AnimationSystem::EasingType::EaseOutElastic)
    
    #define GET_ANIM_ALPHA(id) g_AnimationManager.GetAnimationValue(id)
    #define IS_ANIM_ACTIVE(id) g_AnimationManager.IsAnimationActive(id)
    #define IS_ANIM_COMPLETED(id) g_AnimationManager.IsAnimationCompleted(id)

    // Animation wrapper for ImGui elements
    class AnimatedElement {
    private:
        std::string id;
        bool wasVisible = false;
        
    public:
        AnimatedElement(const char* elementId) : id(elementId) {}
        
        void BeginElement(bool visible = true, float fadeTime = 0.3f, float slideDistance = 20.0f);
        void EndElement();
        bool IsVisible();
        float GetAlpha();
    };

} // namespace AnimationSystem 