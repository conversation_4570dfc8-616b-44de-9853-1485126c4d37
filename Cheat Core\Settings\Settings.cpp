#include "Settings.h"
#include "../GameClass/GameSettings.h"
#include "../Features/Aimbot/WeaponTypes.h"
#include <fstream>
#include <filesystem>
#include "../Features/Config/Config.h"
#include "../Features/Input/HotkeySystem.h"

// Define the global settings instance
SettingsStruct Settings;

// Legacy global variables bridged to new settings
AimbotCore& Aimbot = AIMBOT_SETTINGS.general;
TriggerbotCore& Triggerbot = TRIGGERBOT_SETTINGS.general;
AimbotSettings weaponSettings = AIMBOT_SETTINGS;
RadarSettings& Radar = RADAR_SETTINGS;
ItemESPSettings& Items = ITEM_ESP_SETTINGS;
PlayerESPSettings& Players = PLAYER_ESP_SETTINGS;
HotkeySettings& Keys = HOTKEY_SETTINGS;
ConfigurationSettings& ConfigsMenu = CONFIG_SETTINGS;

// Arrays using the proper type
AimbotCore aimbot[static_cast<int>(WeaponTypes::WeaponCount)];
TriggerbotCore triggerbot[static_cast<int>(WeaponTypes::WeaponCount)];

// Implementation of settings helper functions
namespace SettingsHelper {

    // Path for settings directory and default config file
    const std::string SettingsDir = "C:\\BitCheats\\Fortnite_Config";
    const std::string DefaultConfigFile = SettingsDir + "\\Default.cfg";

    // Function to sync settings with global variables - called each frame
    void SyncSettings() {
        // legacy mirror no longer needed; main settings are accessed via macros

        // Sync weapon-specific settings if needed
        if (AIMBOT_SETTINGS.general.perWeapon) {
            for (int i = 0; i < static_cast<int>(WeaponTypes::WeaponCount); i++) {
                aimbot[i] = Settings.Aim.WeaponConfigs.perWeaponSettings[i];
                triggerbot[i] = TRIGGERBOT_SETTINGS.general;
            }
        }
    }

    // Ensure the config directory exists
    void EnsureConfigDirExists() {
        if (!std::filesystem::exists(SettingsDir)) {
            std::filesystem::create_directories(SettingsDir);
        }
    }

    // Initialize settings with defaults
    void InitializeSettings() {
        // Ensure config directory exists
        EnsureConfigDirExists();

        // Initialize Weapon Settings map if empty
        if (Settings.Aim.WeaponConfigs.perWeaponSettings.empty()) {
            for (int i = 0; i < static_cast<int>(WeaponTypes::WeaponCount); i++) {
                // Create default settings for each weapon type
                Settings.Aim.WeaponConfigs.perWeaponSettings[i] = AimbotCore();
            }
        }

        // Sync settings immediately after initialization
        SyncSettings();
    }

    // Save settings to file
    bool SaveSettings() {
        try {
            // Make sure directory exists
            EnsureConfigDirExists();

            // Sync all settings first
            SyncSettings();

            // Save settings using the config system
            bool result = false;
            if (g_ConfigSystem.selectedConfigIndex >= 0 &&
                g_ConfigSystem.selectedConfigIndex < g_ConfigSystem.configs.size()) {
                // Save to the currently selected config
                std::string filename = g_ConfigSystem.configs[g_ConfigSystem.selectedConfigIndex].filename;
                result = SaveConfiguration(filename);
            } else {
                // Save to default config if none is selected
                result = SaveConfiguration("Default.cfg");
            }

            return result;
        }
        catch (const std::exception& e) {
            // Handle exception
            std::cout << "Exception in SettingsHelper::SaveSettings: " << e.what() << std::endl;
            return false;
        }
    }

    // Load settings from file
    bool LoadSettings() {
        try {
            // Initialize the configuration system if needed
            if (g_ConfigSystem.configs.empty()) {
                InitializeConfigSystem();
            }

            // Load the selected configuration, or default if none selected
            if (g_ConfigSystem.selectedConfigIndex >= 0 &&
                g_ConfigSystem.selectedConfigIndex < g_ConfigSystem.configs.size()) {
                // Load the currently selected config
                bool result = LoadConfiguration(g_ConfigSystem.configs[g_ConfigSystem.selectedConfigIndex].filename);
                if (!result) {
                    // Try to load default config as fallback
                    LoadConfiguration("Default.cfg");
                }
            } else {
                // Try to load default config
                LoadConfiguration("Default.cfg");
            }

            // Update the global settings after loading
            SyncSettings();

            return true;
        }
        catch (const std::exception& e) {
            // Handle exception
            std::cout << "Exception in LoadSettings: " << e.what() << std::endl;
            return false;
        }
    }

    // Reset settings to defaults
    void ResetSettings() {
        // Reinitialize with defaults
        SettingsStruct defaultSettings;
        Settings = defaultSettings;

        // Initialize weapon settings
        InitializeSettings();

        // Sync after reset
        SyncSettings();
    }
}