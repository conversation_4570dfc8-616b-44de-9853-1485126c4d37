#pragma once
#include <Windows.h>
#include <vector>
#include <string>
#include "../Cheat Core/Kernel Driver/Driver/Driver.h"

namespace SignatureScanner {

    /**
     * @brief Converts a signature string to bytes and mask
     * @param signature Pattern like "48 8B 3D ? ? ? ? 4A 85 FF 74 ? 4A 8B CF"
     * @param bytes Output vector for pattern bytes
     * @param mask Output string for mask (x = match, ? = wildcard)
     * @return true if conversion successful
     */
    bool ParseSignature(const std::string& signature, std::vector<uint8_t>& bytes, std::string& mask);

    /**
     * @brief Scans memory for a signature pattern
     * @param baseAddress Starting address to scan from
     * @param scanSize Size of memory region to scan
     * @param signature Pattern like "48 8B 3D ? ? ? ? 4A 85 FF 74 ? 4A 8B CF"
     * @return Address where pattern was found, or 0 if not found
     */
    uintptr_t FindPattern(uintptr_t baseAddress, size_t scanSize, const std::string& signature);

    /**
     * @brief Scans entire module for signature
     * @param moduleBase Base address of module to scan
     * @param signature Pattern to search for
     * @return Address where pattern was found, or 0 if not found
     */
    uintptr_t FindPatternInModule(uintptr_t moduleBase, const std::string& signature);

    /**
     * @brief Resolves RIP relative address from found signature
     * @param signatureAddress Address where signature was found
     * @param instructionOffset Offset to the RIP relative instruction
     * @param instructionSize Size of the instruction containing the RIP relative address
     * @return Resolved absolute address
     */
    uintptr_t ResolveRipRelative(uintptr_t signatureAddress, int instructionOffset, int instructionSize);

    /**
     * @brief Finds UWorld offset using signature scanning
     * @param baseAddress Process base address
     * @return UWorld offset, or 0 if not found
     */
    uintptr_t FindUWorldOffset(uintptr_t baseAddress);

} 