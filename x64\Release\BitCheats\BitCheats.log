﻿  DarkOverlayManager.cpp
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(3,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(74,39): error C2589: '(': illegal token on right side of '::'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(74): error C2062: type 'unknown-type' unexpected
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(74,39): error C2059: syntax error: ')'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(180,50): error C2668: 'ImGui::GetColorU32': ambiguous call to overloaded function
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui.h(448,29): message : could be 'ImU32 ImGui::GetColorU32(ImU32)'
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui.h(447,29): message : or       'ImU32 ImGui::GetColorU32(const ImVec4 &)'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(180,50): message : while trying to match the argument list '(ImColor)'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(186,36): error C2589: '(': illegal token on right side of '::'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(186): error C2062: type 'unknown-type' unexpected
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(186,36): error C2059: syntax error: ')'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(200,85): error C2668: 'ImGui::GetColorU32': ambiguous call to overloaded function
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui.h(448,29): message : could be 'ImU32 ImGui::GetColorU32(ImU32)'
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui.h(447,29): message : or       'ImU32 ImGui::GetColorU32(const ImVec4 &)'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(200,85): message : while trying to match the argument list '(ImColor)'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(200,86): error C2660: 'ImDrawList::AddTriangleFilled': function does not take 3 arguments
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui.h(2767,21): message : see declaration of 'ImDrawList::AddTriangleFilled'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(225,45): error C2668: 'ImGui::GetColorU32': ambiguous call to overloaded function
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui.h(448,29): message : could be 'ImU32 ImGui::GetColorU32(ImU32)'
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui.h(447,29): message : or       'ImU32 ImGui::GetColorU32(const ImVec4 &)'
C:\bitcheats_cheats - New UI\Menu UI\Components\DarkOverlayManager.cpp(225,45): message : while trying to match the argument list '(ImColor)'
  nav_elements.cpp
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1296,1): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1286): message : while compiling class template member function 'void std::vector<std::string,std::allocator<std::string>>::_Reallocate_exactly(const unsigned __int64)'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1363): message : see reference to function template instantiation 'void std::vector<std::string,std::allocator<std::string>>::_Reallocate_exactly(const unsigned __int64)' being compiled
C:\bitcheats_cheats - New UI\Menu UI\ImGui\imgui_settings.h(36): message : see reference to class template instantiation 'std::vector<std::string,std::allocator<std::string>>' being compiled
  GUI.cpp
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(238,46): warning C4838: conversion from 'int' to 'ImWchar' requires a narrowing conversion
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(2443,17): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
